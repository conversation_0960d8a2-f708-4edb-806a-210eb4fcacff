{"name": "fwyy-tools", "description": "一个功能丰富的浏览器扩展工具集合", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "npm run setup-styles && wxt", "dev:firefox": "npm run setup-styles && wxt -b firefox", "build": "npm run setup-styles && wxt build", "build:firefox": "npm run setup-styles && wxt build -b firefox", "zip": "wxt zip", "zip:firefox": "wxt zip -b firefox", "compile": "tsc --noEmit", "test": "vitest", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:ui": "vitest --ui", "test:watch": "vitest --watch", "test:e2e": "vitest run tests/e2e/", "test:integration": "node scripts/integration-test.js", "validate:build": "node scripts/validate-build.js", "test:all": "npm run test:run && npm run test:e2e && npm run test:integration", "postinstall": "wxt prepare && npm run setup-styles", "create-tool": "node scripts/create-tool.js", "tool:create": "node scripts/create-tool.js", "setup-styles": "mkdir -p public/entrypoints/popup public/entrypoints/toolpage && [ ! -L public/styles ] && ln -sf ../styles public/styles || true && [ ! -L public/entrypoints/popup/style.css ] && ln -sf ../../../entrypoints/popup/style.css public/entrypoints/popup/style.css || true && [ ! -L public/entrypoints/toolpage/style.css ] && ln -sf ../../../entrypoints/toolpage/style.css public/entrypoints/toolpage/style.css || true"}, "devDependencies": {"@vitest/ui": "^1.6.0", "jsdom": "^24.1.0", "typescript": "^5.8.3", "vitest": "^1.6.0", "wxt": "^0.20.6"}}