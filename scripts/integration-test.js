#!/usr/bin/env node

/**
 * API Diff Tool Integration Test Script
 * 集成验证脚本 - 验证工具的完整功能
 */

const fs = require('fs');
const path = require('path');

class IntegrationValidator {
  constructor() {
    this.results = {
      passed: 0,
      failed: 0,
      errors: []
    };
  }

  /**
   * 运行所有集成测试
   */
  async runAllTests() {
    console.log('🚀 开始 API Diff Tool 集成验证...\n');

    try {
      await this.validateFileStructure();
      await this.validateTypeDefinitions();
      await this.validateComponentIntegration();
      await this.validateToolRegistration();
      await this.validateStylesIntegration();
      await this.validateTestCoverage();
      
      this.printResults();
    } catch (error) {
      console.error('❌ 集成验证过程中发生错误:', error.message);
      process.exit(1);
    }
  }

  /**
   * 验证文件结构
   */
  async validateFileStructure() {
    console.log('📁 验证文件结构...');

    const requiredFiles = [
      'tools/api-diff-tool.ts',
      'tools/api-diff/types/api-diff-types.ts',
      'tools/api-diff/utils/validation-utils.ts',
      'tools/api-diff/utils/http-request-utils.ts',
      'tools/api-diff/utils/curl-parser.ts',
      'tools/api-diff/utils/diff-utils.ts',
      'tools/api-diff/components/request-builder.ts',
      'tools/api-diff/components/dual-request-executor.ts',
      'tools/api-diff/components/response-renderer.ts',
      'tools/api-diff/components/diff-renderer.ts',
      'tools/api-diff/components/config-manager.ts',
      'tools/api-diff/styles/api-diff.css'
    ];

    const requiredTestFiles = [
      'tests/api-diff/validation-utils.test.ts',
      'tests/api-diff/http-request-utils.test.ts',
      'tests/api-diff/curl-parser.test.ts',
      'tests/api-diff/diff-utils.test.ts',
      'tests/api-diff/config-manager.test.ts',
      'tests/api-diff/api-diff-tool.test.ts',
      'tests/e2e/api-diff-e2e.test.ts'
    ];

    let allFilesExist = true;

    [...requiredFiles, ...requiredTestFiles].forEach(file => {
      if (!fs.existsSync(file)) {
        this.addError(`缺少必需文件: ${file}`);
        allFilesExist = false;
      }
    });

    if (allFilesExist) {
      this.addSuccess('文件结构验证通过');
    }
  }

  /**
   * 验证类型定义
   */
  async validateTypeDefinitions() {
    console.log('🔍 验证类型定义...');

    try {
      const typesFile = 'tools/api-diff/types/api-diff-types.ts';
      const content = fs.readFileSync(typesFile, 'utf8');

      const requiredTypes = [
        'RequestConfig',
        'ApiResponse',
        'DualResponse',
        'DualExecutionResult',
        'DiffOptions',
        'ComponentState',
        'RequestExecutionState',
        'ToolConfig'
      ];

      let allTypesFound = true;

      requiredTypes.forEach(type => {
        if (!content.includes(`interface ${type}`) && !content.includes(`type ${type}`)) {
          this.addError(`缺少类型定义: ${type}`);
          allTypesFound = false;
        }
      });

      if (allTypesFound) {
        this.addSuccess('类型定义验证通过');
      }
    } catch (error) {
      this.addError(`类型定义验证失败: ${error.message}`);
    }
  }

  /**
   * 验证组件集成
   */
  async validateComponentIntegration() {
    console.log('🔧 验证组件集成...');

    try {
      const mainToolFile = 'tools/api-diff-tool.ts';
      const content = fs.readFileSync(mainToolFile, 'utf8');

      const requiredImports = [
        'RequestBuilder',
        'DualRequestExecutor',
        'ResponseRenderer',
        'DiffRenderer',
        'ConfigManager'
      ];

      let allImportsFound = true;

      requiredImports.forEach(component => {
        if (!content.includes(`import { ${component} }`)) {
          this.addError(`缺少组件导入: ${component}`);
          allImportsFound = false;
        }
      });

      // 验证组件实例化
      const requiredInstances = [
        'new RequestBuilder',
        'new DualRequestExecutor',
        'new ResponseRenderer',
        'new DiffRenderer',
        'new ConfigManager'
      ];

      requiredInstances.forEach(instance => {
        if (!content.includes(instance)) {
          this.addError(`缺少组件实例化: ${instance}`);
          allImportsFound = false;
        }
      });

      if (allImportsFound) {
        this.addSuccess('组件集成验证通过');
      }
    } catch (error) {
      this.addError(`组件集成验证失败: ${error.message}`);
    }
  }

  /**
   * 验证工具注册
   */
  async validateToolRegistration() {
    console.log('📋 验证工具注册...');

    try {
      const popupFile = 'entrypoints/popup/main.ts';
      const content = fs.readFileSync(popupFile, 'utf8');

      const registrationChecks = [
        'import { ApiDiffTool }',
        'new ApiDiffTool()',
        'registerToolWithSettings'
      ];

      let allChecksPass = true;

      registrationChecks.forEach(check => {
        if (!content.includes(check)) {
          this.addError(`工具注册检查失败: ${check}`);
          allChecksPass = false;
        }
      });

      if (allChecksPass) {
        this.addSuccess('工具注册验证通过');
      }
    } catch (error) {
      this.addError(`工具注册验证失败: ${error.message}`);
    }
  }

  /**
   * 验证样式集成
   */
  async validateStylesIntegration() {
    console.log('🎨 验证样式集成...');

    try {
      const popupFile = 'entrypoints/popup/main.ts';
      const content = fs.readFileSync(popupFile, 'utf8');

      const styleChecks = [
        'api-diff',
        'loadModule',
        'registerModule'
      ];

      let allStylesIntegrated = true;

      styleChecks.forEach(check => {
        if (!content.includes(check)) {
          this.addError(`样式集成检查失败: ${check}`);
          allStylesIntegrated = false;
        }
      });

      // 验证样式文件存在
      const styleFile = 'tools/api-diff/styles/api-diff.css';
      if (!fs.existsSync(styleFile)) {
        this.addError('样式文件不存在');
        allStylesIntegrated = false;
      } else {
        const styleContent = fs.readFileSync(styleFile, 'utf8');
        if (styleContent.length < 100) {
          this.addError('样式文件内容过少');
          allStylesIntegrated = false;
        }
      }

      if (allStylesIntegrated) {
        this.addSuccess('样式集成验证通过');
      }
    } catch (error) {
      this.addError(`样式集成验证失败: ${error.message}`);
    }
  }

  /**
   * 验证测试覆盖率
   */
  async validateTestCoverage() {
    console.log('🧪 验证测试覆盖率...');

    try {
      const testFiles = [
        'tests/api-diff/validation-utils.test.ts',
        'tests/api-diff/http-request-utils.test.ts',
        'tests/api-diff/curl-parser.test.ts',
        'tests/api-diff/diff-utils.test.ts',
        'tests/api-diff/config-manager.test.ts',
        'tests/api-diff/api-diff-tool.test.ts',
        'tests/e2e/api-diff-e2e.test.ts'
      ];

      let totalTests = 0;
      let allTestsValid = true;

      testFiles.forEach(testFile => {
        if (fs.existsSync(testFile)) {
          const content = fs.readFileSync(testFile, 'utf8');
          const testCount = (content.match(/it\(/g) || []).length;
          totalTests += testCount;

          if (testCount === 0) {
            this.addError(`测试文件无测试用例: ${testFile}`);
            allTestsValid = false;
          }
        } else {
          this.addError(`测试文件不存在: ${testFile}`);
          allTestsValid = false;
        }
      });

      // 验证测试配置
      const vitestConfig = 'vitest.config.ts';
      if (!fs.existsSync(vitestConfig)) {
        this.addError('缺少 Vitest 配置文件');
        allTestsValid = false;
      }

      const setupFile = 'tests/setup.ts';
      if (!fs.existsSync(setupFile)) {
        this.addError('缺少测试设置文件');
        allTestsValid = false;
      }

      if (allTestsValid && totalTests > 50) {
        this.addSuccess(`测试覆盖率验证通过 (${totalTests} 个测试用例)`);
      } else if (allTestsValid) {
        this.addError(`测试用例数量不足: ${totalTests} (建议 > 50)`);
      }
    } catch (error) {
      this.addError(`测试覆盖率验证失败: ${error.message}`);
    }
  }

  /**
   * 验证关键工作流
   */
  async validateKeyWorkflows() {
    console.log('🔄 验证关键工作流...');

    const workflows = [
      {
        name: '配置构建工作流',
        files: ['tools/api-diff/components/request-builder.ts'],
        keywords: ['RequestConfig', 'validation', 'buildRequest']
      },
      {
        name: 'cURL 导入工作流',
        files: ['tools/api-diff/utils/curl-parser.ts'],
        keywords: ['parse', 'curl', 'extractUrl', 'extractHeaders']
      },
      {
        name: '双端请求工作流',
        files: ['tools/api-diff/components/dual-request-executor.ts'],
        keywords: ['execute', 'oldUrl', 'newUrl', 'DualExecutionResult']
      },
      {
        name: '差异对比工作流',
        files: ['tools/api-diff/utils/diff-utils.ts'],
        keywords: ['calculateDiff', 'changes', 'DiffResult']
      },
      {
        name: '配置管理工作流',
        files: ['tools/api-diff/components/config-manager.ts'],
        keywords: ['saveConfig', 'loadConfig', 'exportConfig', 'importConfig']
      }
    ];

    let allWorkflowsValid = true;

    workflows.forEach(workflow => {
      workflow.files.forEach(file => {
        if (fs.existsSync(file)) {
          const content = fs.readFileSync(file, 'utf8');
          const missingKeywords = workflow.keywords.filter(keyword => 
            !content.includes(keyword)
          );

          if (missingKeywords.length > 0) {
            this.addError(`${workflow.name} 缺少关键功能: ${missingKeywords.join(', ')}`);
            allWorkflowsValid = false;
          }
        } else {
          this.addError(`${workflow.name} 文件不存在: ${file}`);
          allWorkflowsValid = false;
        }
      });
    });

    if (allWorkflowsValid) {
      this.addSuccess('关键工作流验证通过');
    }
  }

  /**
   * 添加成功结果
   */
  addSuccess(message) {
    console.log(`  ✅ ${message}`);
    this.results.passed++;
  }

  /**
   * 添加错误结果
   */
  addError(message) {
    console.log(`  ❌ ${message}`);
    this.results.failed++;
    this.results.errors.push(message);
  }

  /**
   * 打印最终结果
   */
  printResults() {
    console.log('\n📊 集成验证结果:');
    console.log(`✅ 通过: ${this.results.passed}`);
    console.log(`❌ 失败: ${this.results.failed}`);

    if (this.results.failed > 0) {
      console.log('\n🔍 失败详情:');
      this.results.errors.forEach((error, index) => {
        console.log(`${index + 1}. ${error}`);
      });
      
      console.log('\n❌ 集成验证失败，请修复上述问题后重试。');
      process.exit(1);
    } else {
      console.log('\n🎉 所有集成验证通过！API Diff Tool 已准备就绪。');
      
      console.log('\n📋 下一步建议:');
      console.log('1. 运行单元测试: npm test');
      console.log('2. 运行端到端测试: npm run test:e2e');
      console.log('3. 构建扩展: npm run build');
      console.log('4. 在浏览器中测试完整功能');
    }
  }
}

// 运行集成验证
if (require.main === module) {
  const validator = new IntegrationValidator();
  validator.runAllTests().catch(error => {
    console.error('验证过程中发生未处理的错误:', error);
    process.exit(1);
  });
}

module.exports = IntegrationValidator;
