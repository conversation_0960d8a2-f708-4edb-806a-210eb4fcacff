#!/usr/bin/env node

/**
 * 工具生成器CLI
 * 自动生成工具模板和脚手架代码
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import readline from 'readline';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 工具类型定义
const TOOL_TYPES = {
  basic: {
    name: '基础工具',
    description: '简单的工具，执行单一功能',
    template: 'basic-tool.template.ts'
  },
  ui: {
    name: 'UI工具',
    description: '需要用户界面交互的工具',
    template: 'ui-tool.template.ts'
  },
  api: {
    name: 'API工具',
    description: '需要调用外部API的工具',
    template: 'api-tool.template.ts'
  },
  content: {
    name: '内容脚本工具',
    description: '需要在页面中执行脚本的工具',
    template: 'content-tool.template.ts'
  }
};



// 创建readline接口
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// 提示用户输入
function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

// 验证工具ID格式
function validateToolId(id) {
  const regex = /^[a-z][a-z0-9-]*[a-z0-9]$/;
  return regex.test(id) && id.length >= 3 && id.length <= 50;
}

// 转换为PascalCase
function toPascalCase(str) {
  return str.split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join('');
}



// 获取工具信息
async function getToolInfo() {
  console.log('\n🛠️  工具生成器');
  console.log('================\n');

  // 工具ID
  let toolId;
  while (true) {
    toolId = await question('工具ID (kebab-case, 如: my-awesome-tool): ');
    if (validateToolId(toolId)) {
      break;
    }
    console.log('❌ 工具ID格式不正确。请使用小写字母、数字和连字符，长度3-50字符。');
  }

  // 工具名称
  const toolName = await question('工具名称 (如: 我的超棒工具): ');
  if (!toolName.trim()) {
    console.log('❌ 工具名称不能为空');
    process.exit(1);
  }

  // 工具描述
  const toolDescription = await question('工具描述 (如: 这是一个很有用的工具): ');
  if (!toolDescription.trim()) {
    console.log('❌ 工具描述不能为空');
    process.exit(1);
  }

  // 工具图标
  const toolIcon = await question('工具图标 (emoji, 如: 🚀): ') || '🛠️';

  // 使用默认分类 'all'
  const categoryChoice = 'all';

  // 工具类型
  console.log('\n选择工具类型:');
  Object.entries(TOOL_TYPES).forEach(([key, value], index) => {
    console.log(`${index + 1}. ${key} - ${value.name}: ${value.description}`);
  });

  let typeChoice;
  while (true) {
    typeChoice = await question('请选择类型 (1-4): ');
    const choice = parseInt(typeChoice);
    if (choice >= 1 && choice <= 4) {
      typeChoice = Object.keys(TOOL_TYPES)[choice - 1];
      break;
    }
    console.log('❌ 请选择有效的类型编号');
  }

  return {
    id: toolId,
    name: toolName.trim(),
    description: toolDescription.trim(),
    icon: toolIcon.trim(),
    category: categoryChoice,
    type: typeChoice,
    className: toPascalCase(toolId) + 'Tool'
  };
}

// 创建工具文件
async function createToolFile(toolInfo) {
  const toolsDir = path.join(__dirname, '../tools');
  const toolFilePath = path.join(toolsDir, `${toolInfo.id}.ts`);

  // 检查文件是否已存在
  if (fs.existsSync(toolFilePath)) {
    const overwrite = await question(`⚠️  工具文件 ${toolInfo.id}.ts 已存在，是否覆盖？ (y/N): `);
    if (overwrite.toLowerCase() !== 'y') {
      console.log('❌ 取消创建工具');
      return false;
    }
  }

  // 确保tools目录存在
  if (!fs.existsSync(toolsDir)) {
    fs.mkdirSync(toolsDir, { recursive: true });
  }

  // 生成工具代码
  const toolCode = generateToolCode(toolInfo);
  
  // 写入文件
  fs.writeFileSync(toolFilePath, toolCode, 'utf8');
  
  console.log(`✅ 工具文件已创建: ${toolFilePath}`);
  return true;
}

// 生成工具代码
function generateToolCode(toolInfo) {
  const templates = {
    basic: generateBasicToolTemplate,
    ui: generateUIToolTemplate,
    api: generateAPIToolTemplate,
    content: generateContentToolTemplate
  };

  return templates[toolInfo.type](toolInfo);
}

// 基础工具模板
function generateBasicToolTemplate(toolInfo) {
  return `/**
 * ${toolInfo.name}
 * ${toolInfo.description}
 */

import { BaseTool, TOOL_CATEGORIES, TOOL_ICONS } from '../utils/tool-template';

export class ${toolInfo.className} extends BaseTool {
  id = '${toolInfo.id}';
  name = '${toolInfo.name}';
  description = '${toolInfo.description}';
  icon = '${toolInfo.icon}';
  categories = ['${toolInfo.category}']; // 使用新的多分类标签格式
  badge?: 'new' = 'new';

  async action(): Promise<void> {
    try {
      // TODO: 实现工具功能
      console.log('执行${toolInfo.name}');
      
      // 示例：显示通知
      await this.showNotification('${toolInfo.name}', '工具执行成功！');
      
    } catch (error) {
      console.error('${toolInfo.name}执行失败:', error);
      await this.showNotification('错误', \`${toolInfo.name}执行失败: \${error.message}\`);
    }
  }
}
`;
}

// UI工具模板
function generateUIToolTemplate(toolInfo) {
  return `/**
 * ${toolInfo.name}
 * ${toolInfo.description}
 */

import { BaseTool, TOOL_CATEGORIES, TOOL_ICONS } from '../utils/tool-template';

export class ${toolInfo.className} extends BaseTool {
  id = '${toolInfo.id}';
  name = '${toolInfo.name}';
  description = '${toolInfo.description}';
  icon = '${toolInfo.icon}';
  categories = ['${toolInfo.category}']; // 使用新的多分类标签格式
  badge?: 'new' = 'new';

  async action(): Promise<void> {
    try {
      const modal = this.createModal();
      document.body.appendChild(modal);
      
    } catch (error) {
      console.error('${toolInfo.name}启动失败:', error);
      await this.showNotification('错误', '${toolInfo.name}启动失败');
    }
  }

  private createModal(): HTMLElement {
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = \`
      <div class="modal-content">
        <div class="modal-header">
          <h3>${toolInfo.icon} ${toolInfo.name}</h3>
          <button class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
          <p>${toolInfo.description}</p>
          <!-- TODO: 添加工具界面 -->
        </div>
        <div class="modal-footer">
          <button class="btn btn-primary" id="execute-btn">执行</button>
          <button class="btn btn-secondary" id="cancel-btn">取消</button>
        </div>
      </div>
    \`;

    this.bindModalEvents(modal);
    return modal;
  }

  private bindModalEvents(modal: HTMLElement): void {
    // 关闭模态框
    const closeBtn = modal.querySelector('.modal-close') as HTMLButtonElement;
    const cancelBtn = modal.querySelector('#cancel-btn') as HTMLButtonElement;
    
    [closeBtn, cancelBtn].forEach(btn => {
      btn?.addEventListener('click', () => {
        modal.remove();
      });
    });

    // 执行按钮
    const executeBtn = modal.querySelector('#execute-btn') as HTMLButtonElement;
    executeBtn?.addEventListener('click', async () => {
      try {
        await this.executeAction();
        modal.remove();
        await this.showNotification('成功', '${toolInfo.name}执行完成');
      } catch (error) {
        console.error('执行失败:', error);
        await this.showNotification('错误', \`执行失败: \${error.message}\`);
      }
    });

    // 点击背景关闭
    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        modal.remove();
      }
    });
  }

  private async executeAction(): Promise<void> {
    // TODO: 实现具体的工具逻辑
    console.log('执行${toolInfo.name}逻辑');
  }
}
`;
}

// API工具模板
function generateAPIToolTemplate(toolInfo) {
  return `/**
 * ${toolInfo.name}
 * ${toolInfo.description}
 */

import { BaseTool, TOOL_CATEGORIES, TOOL_ICONS } from '../utils/tool-template';

interface APIResponse {
  success: boolean;
  data?: any;
  error?: string;
}

export class ${toolInfo.className} extends BaseTool {
  id = '${toolInfo.id}';
  name = '${toolInfo.name}';
  description = '${toolInfo.description}';
  icon = '${toolInfo.icon}';
  categories = ['${toolInfo.category}']; // 使用新的多分类标签格式
  badge?: 'new' = 'new';

  private apiBaseUrl = 'https://api.example.com'; // TODO: 设置实际的API地址

  async action(): Promise<void> {
    try {
      const modal = this.createModal();
      document.body.appendChild(modal);
      
    } catch (error) {
      console.error('${toolInfo.name}启动失败:', error);
      await this.showNotification('错误', '${toolInfo.name}启动失败');
    }
  }

  private createModal(): HTMLElement {
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = \`
      <div class="modal-content">
        <div class="modal-header">
          <h3>${toolInfo.icon} ${toolInfo.name}</h3>
          <button class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
          <div class="form-group">
            <label for="api-input">输入参数:</label>
            <input type="text" id="api-input" class="form-control" placeholder="请输入参数">
          </div>
          <div id="result-section" class="result-section" style="display: none;">
            <h4>结果:</h4>
            <div id="result-content"></div>
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn btn-primary" id="call-api-btn">调用API</button>
          <button class="btn btn-secondary" id="cancel-btn">取消</button>
        </div>
      </div>
    \`;

    this.bindModalEvents(modal);
    return modal;
  }

  private bindModalEvents(modal: HTMLElement): void {
    // 关闭模态框
    const closeBtn = modal.querySelector('.modal-close') as HTMLButtonElement;
    const cancelBtn = modal.querySelector('#cancel-btn') as HTMLButtonElement;
    
    [closeBtn, cancelBtn].forEach(btn => {
      btn?.addEventListener('click', () => {
        modal.remove();
      });
    });

    // API调用按钮
    const callApiBtn = modal.querySelector('#call-api-btn') as HTMLButtonElement;
    callApiBtn?.addEventListener('click', async () => {
      const input = modal.querySelector('#api-input') as HTMLInputElement;
      const resultSection = modal.querySelector('#result-section') as HTMLElement;
      const resultContent = modal.querySelector('#result-content') as HTMLElement;

      if (!input.value.trim()) {
        await this.showNotification('提示', '请输入参数');
        return;
      }

      try {
        callApiBtn.disabled = true;
        callApiBtn.textContent = '调用中...';

        const result = await this.callAPI(input.value.trim());
        
        resultContent.innerHTML = \`
          <div class="result-success">
            <pre>\${JSON.stringify(result, null, 2)}</pre>
          </div>
        \`;
        resultSection.style.display = 'block';

      } catch (error) {
        resultContent.innerHTML = \`
          <div class="result-error">
            <p>API调用失败: \${error.message}</p>
          </div>
        \`;
        resultSection.style.display = 'block';
      } finally {
        callApiBtn.disabled = false;
        callApiBtn.textContent = '调用API';
      }
    });

    // 点击背景关闭
    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        modal.remove();
      }
    });
  }

  private async callAPI(param: string): Promise<APIResponse> {
    // TODO: 实现实际的API调用逻辑
    const response = await fetch(\`\${this.apiBaseUrl}/endpoint\`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ param })
    });

    if (!response.ok) {
      throw new Error(\`API调用失败: \${response.status}\`);
    }

    return await response.json();
  }
}
`;
}

// 内容脚本工具模板
function generateContentToolTemplate(toolInfo) {
  return `/**
 * ${toolInfo.name}
 * ${toolInfo.description}
 */

import { BaseTool, TOOL_CATEGORIES, TOOL_ICONS } from '../utils/tool-template';

export class ${toolInfo.className} extends BaseTool {
  id = '${toolInfo.id}';
  name = '${toolInfo.name}';
  description = '${toolInfo.description}';
  icon = '${toolInfo.icon}';
  categories = ['${toolInfo.category}']; // 使用新的多分类标签格式
  badge?: 'new' = 'new';

  async action(): Promise<void> {
    try {
      const tab = await this.getCurrentTab();
      if (!tab?.id) {
        throw new Error('无法获取当前标签页');
      }

      // 执行内容脚本
      const result = await this.executeScript(() => {
        // TODO: 在页面中执行的脚本逻辑
        return {
          title: document.title,
          url: window.location.href,
          // 添加更多需要获取的页面信息
        };
      });

      console.log('页面信息:', result);
      
      // 处理结果
      await this.processResult(result[0].result);
      
      await this.showNotification('${toolInfo.name}', '页面处理完成');
      
    } catch (error) {
      console.error('${toolInfo.name}执行失败:', error);
      await this.showNotification('错误', \`${toolInfo.name}执行失败: \${error.message}\`);
    }
  }

  private async processResult(pageInfo: any): Promise<void> {
    // TODO: 处理从页面获取的信息
    console.log('处理页面信息:', pageInfo);
    
    // 示例：复制信息到剪贴板
    const info = \`页面标题: \${pageInfo.title}\\n页面URL: \${pageInfo.url}\`;
    await this.copyToClipboard(info);
  }

  // 高级内容脚本示例：修改页面内容
  private async modifyPageContent(): Promise<void> {
    const tab = await this.getCurrentTab();
    if (!tab?.id) return;

    await chrome.scripting.executeScript({
      target: { tabId: tab.id },
      func: () => {
        // TODO: 在页面中执行的修改逻辑
        const elements = document.querySelectorAll('p');
        elements.forEach(el => {
          // 示例：高亮所有段落
          el.style.backgroundColor = 'yellow';
        });
      }
    });
  }

  // 高级内容脚本示例：注入CSS
  private async injectCSS(): Promise<void> {
    const tab = await this.getCurrentTab();
    if (!tab?.id) return;

    await chrome.scripting.insertCSS({
      target: { tabId: tab.id },
      css: \`
        /* TODO: 添加需要注入的CSS样式 */
        .${toolInfo.id}-highlight {
          background-color: yellow !important;
          border: 2px solid orange !important;
        }
      \`
    });
  }
}
`;
}

// 更新工具注册
async function updateToolRegistration(toolInfo) {
  const mainFilePath = path.join(__dirname, '../entrypoints/popup/main.ts');
  
  if (!fs.existsSync(mainFilePath)) {
    console.log('⚠️  main.ts文件不存在，请手动注册工具');
    return;
  }

  let mainContent = fs.readFileSync(mainFilePath, 'utf8');
  
  // 添加导入语句
  const importStatement = `import { ${toolInfo.className} } from '../../tools/${toolInfo.id}';`;
  
  // 查找现有导入的位置
  const importRegex = /import.*from.*tools.*;\s*$/gm;
  const matches = [...mainContent.matchAll(importRegex)];
  
  if (matches.length > 0) {
    // 在最后一个工具导入后添加新的导入
    const lastMatch = matches[matches.length - 1];
    const insertPos = lastMatch.index + lastMatch[0].length;
    mainContent = mainContent.slice(0, insertPos) + '\n' + importStatement + mainContent.slice(insertPos);
  } else {
    // 在现有导入后添加
    const afterImports = mainContent.indexOf('\n// 工具注册表已从utils/tool-registry.ts导入');
    if (afterImports !== -1) {
      mainContent = mainContent.slice(0, afterImports) + '\n' + importStatement + mainContent.slice(afterImports);
    }
  }

  // 添加工具注册代码
  const registrationCode = `    const ${toolInfo.id.replace(/-/g, '')} = new ${toolInfo.className}();
    await toolRegistry.register(${toolInfo.id.replace(/-/g, '')});`;

  // 查找注册区域
  const registrationRegex = /await toolRegistry\.register\([^)]+\);\s*$/gm;
  const regMatches = [...mainContent.matchAll(registrationRegex)];
  
  if (regMatches.length > 0) {
    // 在最后一个注册后添加新的注册
    const lastRegMatch = regMatches[regMatches.length - 1];
    const insertPos = lastRegMatch.index + lastRegMatch[0].length;
    mainContent = mainContent.slice(0, insertPos) + '\n\n' + registrationCode + mainContent.slice(insertPos);
  }

  // 写回文件
  fs.writeFileSync(mainFilePath, mainContent, 'utf8');
  console.log('✅ 工具注册已更新到 main.ts');
}

// 生成使用说明
function generateUsageInstructions(toolInfo) {
  console.log('\n📖 使用说明');
  console.log('============\n');
  console.log(`✅ 工具 "${toolInfo.name}" 已成功创建！`);
  console.log(`📁 文件位置: tools/${toolInfo.id}.ts`);
  console.log(`🏷️  工具类名: ${toolInfo.className}`);
  console.log(`📂 分类: 全部 (默认)`);
  console.log(`🔧 类型: ${TOOL_TYPES[toolInfo.type].name}`);
  console.log('\n📝 下一步操作:');
  console.log('1. 编辑工具文件，实现具体功能');
  console.log('2. 如果需要特殊权限，请更新 wxt.config.ts');
  console.log('3. 运行 npm run dev 测试工具');
  console.log('4. 完善工具功能和错误处理');
  console.log('\n🚀 开发提示:');
  console.log('- 使用 this.showNotification() 显示通知');
  console.log('- 使用 this.copyToClipboard() 复制内容');
  console.log('- 使用 this.getCurrentTab() 获取当前标签页');
  console.log('- 使用 this.executeScript() 在页面中执行脚本');
}

// 主函数
async function main() {
  try {
    const toolInfo = await getToolInfo();
    
    console.log('\n📋 工具信息确认:');
    console.log(`ID: ${toolInfo.id}`);
    console.log(`名称: ${toolInfo.name}`);
    console.log(`描述: ${toolInfo.description}`);
    console.log(`图标: ${toolInfo.icon}`);
    console.log(`分类: 全部 (默认)`);
    console.log(`类型: ${TOOL_TYPES[toolInfo.type].name}`);
    console.log(`类名: ${toolInfo.className}`);

    const confirm = await question('\n确认创建工具？ (Y/n): ');
    if (confirm.toLowerCase() === 'n') {
      console.log('❌ 取消创建工具');
      process.exit(0);
    }

    // 创建工具文件
    const created = await createToolFile(toolInfo);
    if (!created) {
      process.exit(1);
    }

    // 更新工具注册
    await updateToolRegistration(toolInfo);

    // 生成使用说明
    generateUsageInstructions(toolInfo);

  } catch (error) {
    console.error('❌ 创建工具失败:', error);
    process.exit(1);
  } finally {
    rl.close();
  }
}

// 运行主函数
main();
