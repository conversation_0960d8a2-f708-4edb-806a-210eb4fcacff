#!/usr/bin/env node

/**
 * Build Validation Script
 * 构建验证脚本 - 验证构建产物的完整性和正确性
 */

const fs = require('fs');
const path = require('path');

class BuildValidator {
  constructor() {
    this.buildDir = '.output/chrome-mv3';
    this.results = {
      passed: 0,
      failed: 0,
      warnings: 0,
      errors: []
    };
  }

  /**
   * 运行构建验证
   */
  async validateBuild() {
    console.log('🔍 开始验证构建产物...\n');

    if (!fs.existsSync(this.buildDir)) {
      console.error(`❌ 构建目录不存在: ${this.buildDir}`);
      console.log('请先运行: npm run build');
      process.exit(1);
    }

    try {
      await this.validateManifest();
      await this.validateEntrypoints();
      await this.validateAssets();
      await this.validateToolFiles();
      await this.validateStyles();
      await this.validatePermissions();
      
      this.printResults();
    } catch (error) {
      console.error('❌ 构建验证过程中发生错误:', error.message);
      process.exit(1);
    }
  }

  /**
   * 验证 manifest.json
   */
  async validateManifest() {
    console.log('📋 验证 manifest.json...');

    const manifestPath = path.join(this.buildDir, 'manifest.json');
    
    if (!fs.existsSync(manifestPath)) {
      this.addError('manifest.json 文件不存在');
      return;
    }

    try {
      const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'));

      // 验证基本字段
      const requiredFields = ['name', 'version', 'manifest_version', 'description'];
      const missingFields = requiredFields.filter(field => !manifest[field]);
      
      if (missingFields.length > 0) {
        this.addError(`manifest.json 缺少必需字段: ${missingFields.join(', ')}`);
      }

      // 验证权限
      if (!manifest.permissions || !Array.isArray(manifest.permissions)) {
        this.addError('manifest.json 缺少 permissions 字段');
      } else {
        const requiredPermissions = ['storage', 'activeTab'];
        const missingPermissions = requiredPermissions.filter(
          perm => !manifest.permissions.includes(perm)
        );
        
        if (missingPermissions.length > 0) {
          this.addWarning(`建议添加权限: ${missingPermissions.join(', ')}`);
        }
      }

      // 验证入口点
      if (!manifest.action || !manifest.action.default_popup) {
        this.addError('manifest.json 缺少 popup 配置');
      }

      if (manifest.manifest_version !== 3) {
        this.addWarning('建议使用 Manifest V3');
      }

      this.addSuccess('manifest.json 验证通过');
    } catch (error) {
      this.addError(`manifest.json 解析失败: ${error.message}`);
    }
  }

  /**
   * 验证入口点文件
   */
  async validateEntrypoints() {
    console.log('🚪 验证入口点文件...');

    const entrypoints = [
      { name: 'popup.html', required: true },
      { name: 'popup.js', required: true },
      { name: 'newtab.html', required: true },
      { name: 'newtab.js', required: true }
    ];

    let allEntrypointsValid = true;

    entrypoints.forEach(entry => {
      const filePath = path.join(this.buildDir, entry.name);
      
      if (!fs.existsSync(filePath)) {
        if (entry.required) {
          this.addError(`缺少入口点文件: ${entry.name}`);
          allEntrypointsValid = false;
        } else {
          this.addWarning(`可选入口点文件不存在: ${entry.name}`);
        }
      } else {
        const content = fs.readFileSync(filePath, 'utf8');
        
        if (content.length < 100) {
          this.addWarning(`入口点文件内容过少: ${entry.name}`);
        }

        // 验证 HTML 文件结构
        if (entry.name.endsWith('.html')) {
          if (!content.includes('<!DOCTYPE html>')) {
            this.addWarning(`${entry.name} 缺少 DOCTYPE 声明`);
          }
          
          if (!content.includes('<script')) {
            this.addWarning(`${entry.name} 可能缺少脚本引用`);
          }
        }

        // 验证 JS 文件
        if (entry.name.endsWith('.js')) {
          if (content.includes('console.log') && !content.includes('console.error')) {
            this.addWarning(`${entry.name} 包含调试代码`);
          }
        }
      }
    });

    if (allEntrypointsValid) {
      this.addSuccess('入口点文件验证通过');
    }
  }

  /**
   * 验证资源文件
   */
  async validateAssets() {
    console.log('📦 验证资源文件...');

    const assetDirs = ['styles', 'icons'];
    let allAssetsValid = true;

    assetDirs.forEach(dir => {
      const dirPath = path.join(this.buildDir, dir);
      
      if (fs.existsSync(dirPath)) {
        const files = fs.readdirSync(dirPath);
        
        if (files.length === 0) {
          this.addWarning(`资源目录为空: ${dir}`);
        }

        // 验证样式文件
        if (dir === 'styles') {
          const cssFiles = files.filter(f => f.endsWith('.css'));
          if (cssFiles.length === 0) {
            this.addWarning('没有找到 CSS 文件');
          }
        }

        // 验证图标文件
        if (dir === 'icons') {
          const iconFiles = files.filter(f => f.match(/\.(png|jpg|svg)$/));
          if (iconFiles.length === 0) {
            this.addWarning('没有找到图标文件');
          }
        }
      } else {
        this.addWarning(`资源目录不存在: ${dir}`);
      }
    });

    this.addSuccess('资源文件验证完成');
  }

  /**
   * 验证工具文件
   */
  async validateToolFiles() {
    console.log('🔧 验证工具文件...');

    // 检查是否有工具相关的 JS 文件
    const files = this.getAllJSFiles(this.buildDir);
    
    let hasApiDiffTool = false;
    let hasToolRegistry = false;

    files.forEach(file => {
      const content = fs.readFileSync(file, 'utf8');
      
      if (content.includes('ApiDiffTool') || content.includes('api-diff-tool')) {
        hasApiDiffTool = true;
      }
      
      if (content.includes('ToolRegistry') || content.includes('registerTool')) {
        hasToolRegistry = true;
      }
    });

    if (!hasApiDiffTool) {
      this.addError('构建产物中未找到 API Diff Tool 相关代码');
    }

    if (!hasToolRegistry) {
      this.addWarning('构建产物中未找到工具注册相关代码');
    }

    if (hasApiDiffTool && hasToolRegistry) {
      this.addSuccess('工具文件验证通过');
    }
  }

  /**
   * 验证样式文件
   */
  async validateStyles() {
    console.log('🎨 验证样式文件...');

    const stylesDir = path.join(this.buildDir, 'styles');
    
    if (!fs.existsSync(stylesDir)) {
      this.addWarning('样式目录不存在');
      return;
    }

    const cssFiles = fs.readdirSync(stylesDir).filter(f => f.endsWith('.css'));
    
    if (cssFiles.length === 0) {
      this.addWarning('没有找到 CSS 文件');
      return;
    }

    let hasApiDiffStyles = false;

    cssFiles.forEach(file => {
      const filePath = path.join(stylesDir, file);
      const content = fs.readFileSync(filePath, 'utf8');
      
      if (content.includes('api-diff') || content.includes('API Diff')) {
        hasApiDiffStyles = true;
      }

      // 检查样式文件大小
      if (content.length < 500) {
        this.addWarning(`样式文件内容较少: ${file}`);
      }
    });

    if (hasApiDiffStyles) {
      this.addSuccess('API Diff 样式文件验证通过');
    } else {
      this.addWarning('未找到 API Diff 相关样式');
    }
  }

  /**
   * 验证权限配置
   */
  async validatePermissions() {
    console.log('🔐 验证权限配置...');

    const manifestPath = path.join(this.buildDir, 'manifest.json');
    
    if (!fs.existsSync(manifestPath)) {
      this.addError('无法验证权限：manifest.json 不存在');
      return;
    }

    try {
      const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'));
      const permissions = manifest.permissions || [];

      // API Diff Tool 需要的权限
      const requiredPermissions = ['storage', 'activeTab'];
      const optionalPermissions = ['clipboardWrite', 'notifications'];

      const missingRequired = requiredPermissions.filter(
        perm => !permissions.includes(perm)
      );

      const missingOptional = optionalPermissions.filter(
        perm => !permissions.includes(perm)
      );

      if (missingRequired.length > 0) {
        this.addError(`缺少必需权限: ${missingRequired.join(', ')}`);
      }

      if (missingOptional.length > 0) {
        this.addWarning(`缺少可选权限: ${missingOptional.join(', ')}`);
      }

      // 检查是否有不必要的权限
      const unnecessaryPermissions = permissions.filter(perm => 
        !requiredPermissions.includes(perm) && 
        !optionalPermissions.includes(perm) &&
        !perm.startsWith('http') // 允许 host permissions
      );

      if (unnecessaryPermissions.length > 0) {
        this.addWarning(`可能不必要的权限: ${unnecessaryPermissions.join(', ')}`);
      }

      if (missingRequired.length === 0) {
        this.addSuccess('权限配置验证通过');
      }
    } catch (error) {
      this.addError(`权限验证失败: ${error.message}`);
    }
  }

  /**
   * 获取所有 JS 文件
   */
  getAllJSFiles(dir) {
    let jsFiles = [];
    
    const items = fs.readdirSync(dir);
    
    items.forEach(item => {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        jsFiles = jsFiles.concat(this.getAllJSFiles(fullPath));
      } else if (item.endsWith('.js')) {
        jsFiles.push(fullPath);
      }
    });
    
    return jsFiles;
  }

  /**
   * 添加成功结果
   */
  addSuccess(message) {
    console.log(`  ✅ ${message}`);
    this.results.passed++;
  }

  /**
   * 添加警告
   */
  addWarning(message) {
    console.log(`  ⚠️  ${message}`);
    this.results.warnings++;
  }

  /**
   * 添加错误结果
   */
  addError(message) {
    console.log(`  ❌ ${message}`);
    this.results.failed++;
    this.results.errors.push(message);
  }

  /**
   * 打印最终结果
   */
  printResults() {
    console.log('\n📊 构建验证结果:');
    console.log(`✅ 通过: ${this.results.passed}`);
    console.log(`⚠️  警告: ${this.results.warnings}`);
    console.log(`❌ 失败: ${this.results.failed}`);

    if (this.results.failed > 0) {
      console.log('\n🔍 错误详情:');
      this.results.errors.forEach((error, index) => {
        console.log(`${index + 1}. ${error}`);
      });
      
      console.log('\n❌ 构建验证失败，请修复上述错误后重新构建。');
      process.exit(1);
    } else {
      console.log('\n🎉 构建验证通过！扩展已准备好安装使用。');
      
      if (this.results.warnings > 0) {
        console.log('\n💡 建议处理上述警告以获得更好的用户体验。');
      }
      
      console.log('\n📋 下一步:');
      console.log('1. 在浏览器中加载扩展进行测试');
      console.log('2. 运行手动测试用例');
      console.log('3. 准备发布包');
    }
  }
}

// 运行构建验证
if (require.main === module) {
  const validator = new BuildValidator();
  validator.validateBuild().catch(error => {
    console.error('验证过程中发生未处理的错误:', error);
    process.exit(1);
  });
}

module.exports = BuildValidator;
