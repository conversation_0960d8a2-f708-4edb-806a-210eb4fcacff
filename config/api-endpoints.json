{"update": {"baseUrl": "https://api.fwyy-tools.com/v1", "endpoints": {"checkUpdate": "/updates/check", "batchCheckUpdates": "/updates/batch-check", "getVersionList": "/tools/{toolId}/versions", "getDownloadUrl": "/downloads/url", "getUpdateStats": "/stats/updates", "reportUpdateSuccess": "/updates/report/success", "reportUpdateFailure": "/updates/report/failure"}, "timeout": 10000, "retryAttempts": 3, "retryDelay": 1000, "cacheExpiry": 300000}, "development": {"baseUrl": "http://localhost:3000/api/v1", "endpoints": {"checkUpdate": "/updates/check", "batchCheckUpdates": "/updates/batch-check", "getVersionList": "/tools/{toolId}/versions", "getDownloadUrl": "/downloads/url", "getUpdateStats": "/stats/updates", "reportUpdateSuccess": "/updates/report/success", "reportUpdateFailure": "/updates/report/failure"}, "timeout": 5000, "retryAttempts": 1, "retryDelay": 500, "cacheExpiry": 60000}, "fallback": {"enabled": true, "mockData": {"updateProbability": 0.3, "updateTypes": ["patch", "minor", "major"], "priorities": ["low", "medium", "high"], "sampleChangelog": [{"type": "bugfix", "description": "修复了界面显示问题"}, {"type": "improvement", "description": "优化了性能表现"}, {"type": "feature", "description": "新增了便捷功能"}]}}}