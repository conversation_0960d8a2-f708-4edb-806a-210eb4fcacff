# New Tab Page 功能实施指南

## 快速开始

本文档提供了实现 New Tab Page 工具跳转功能的分步实施指南。

## 前置条件

- 熟悉项目现有架构
- 了解 WXT 框架基础
- 掌握 TypeScript 开发
- 具备 Chrome 扩展开发经验

## 实施步骤

### 第一步：创建 New Tab 入口点

#### 1.1 创建目录结构

```bash
mkdir -p entrypoints/newtab
touch entrypoints/newtab/index.html
touch entrypoints/newtab/main.ts
touch entrypoints/newtab/style.css
```

#### 1.2 实现基础 HTML

创建 `entrypoints/newtab/index.html`：

```html
<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>服务运营工具集合</title>
    <meta name="manifest.type" content="chrome_url_overrides" />
  </head>
  <body>
    <div id="app">
      <div id="loading" class="loading-screen">
        <div class="loading-content">
          <div class="loading-spinner">🛠️</div>
          <div class="loading-text">正在启动工具...</div>
        </div>
      </div>
      
      <div id="tool-container" class="tool-container" style="display: none;"></div>
      
      <div id="tool-selector" class="tool-selector" style="display: none;"></div>
    </div>
    <script type="module" src="./main.ts"></script>
  </body>
</html>
```

### 第二步：配置清单文件

#### 2.1 修改 wxt.config.ts

```typescript
// wxt.config.ts
export default defineConfig({
  manifest: {
    // 现有配置...
    chrome_url_overrides: {
      newtab: 'newtab.html'
    }
  }
});
```

### 第三步：扩展工具接口

#### 3.1 修改 Tool 接口

编辑 `utils/tool-template.ts`：

```typescript
export interface Tool {
  // 现有字段...
  displayMode: 'popup' | 'newtab';        // 显示模式
  newtabData?: any;                       // 传递给newtab的数据
  requiresFullscreen?: boolean;           // 是否需要全屏环境
  onNewTabInit?: () => void | Promise<void>; // newtab初始化钩子
  onNewTabDestroy?: () => void | Promise<void>; // newtab销毁钩子
}

export abstract class BaseTool implements Tool {
  // 新增字段默认值
  displayMode: 'popup' | 'newtab' = 'popup';
  newtabData?: any;
  requiresFullscreen: boolean = false;
  
  // 新增生命周期钩子
  async onNewTabInit(): Promise<void> {
    // 默认实现
  }
  
  async onNewTabDestroy(): Promise<void> {
    // 默认实现
  }
}
```

### 第四步：实现跳转逻辑

#### 4.1 修改 ToolManager

编辑 `entrypoints/popup/main.ts`：

```typescript
// 在 ToolManager 类中添加跳转方法
private async executeTool(tool: Tool) {
  try {
    const toolInstance = toolRegistry.getById(tool.id);
    
    if (!toolInstance) {
      throw new Error(`工具 ${tool.name} 未找到`);
    }
    
    // 检查是否需要跳转到newtab
    if (toolInstance.displayMode === 'newtab') {
      await this.openToolInNewTab(toolInstance);
      return;
    }
    
    // 原有执行逻辑
    if (typeof toolInstance.action === 'function') {
      await toolInstance.action();
    }
    
  } catch (error) {
    console.error(`❌ 执行工具 ${tool.name} 时出错:`, error);
    notificationManager.error(`执行工具失败: ${error.message}`);
  }
}

private async openToolInNewTab(tool: Tool) {
  const launchData = {
    toolId: tool.id,
    timestamp: Date.now(),
    data: tool.newtabData || {},
    source: 'popup'
  };
  
  // 存储启动数据
  await browser.storage.local.set({
    'newtab-tool-launch': launchData
  });
  
  // 打开newtab页面
  const newTabUrl = browser.runtime.getURL('newtab.html');
  await browser.tabs.create({ url: newTabUrl });
  
  // 延迟关闭popup
  setTimeout(() => window.close(), 500);
}
```

### 第五步：实现 New Tab 管理器

#### 5.1 创建 New Tab 主逻辑

编辑 `entrypoints/newtab/main.ts`：

```typescript
import { toolRegistry } from '../../utils/tool-registry';
import { settingsManager } from '../../utils/settings-manager';
import { styleManager } from '../../utils/style-manager';

interface NewTabLaunchData {
  toolId: string;
  timestamp: number;
  data?: any;
  source?: 'popup' | 'direct';
}

class NewTabManager {
  private currentTool: any = null;
  
  constructor() {
    this.init();
  }
  
  private async init() {
    try {
      this.showLoading();
      
      // 初始化管理器
      await settingsManager.init();
      await toolRegistry.init();
      
      // 加载样式
      await this.loadStyles();
      
      // 检查启动数据
      await this.checkLaunchData();
      
    } catch (error) {
      console.error('❌ New Tab初始化失败:', error);
      this.showError('初始化失败');
    }
  }
  
  private async loadStyles() {
    try {
      styleManager.registerModule({
        name: 'newtab',
        path: '/entrypoints/newtab/style.css',
        dependencies: ['design-tokens', 'components']
      });
      
      await styleManager.loadModule('newtab');
    } catch (error) {
      console.warn('❌ 样式加载失败:', error);
    }
  }
  
  private async checkLaunchData() {
    const result = await browser.storage.local.get('newtab-tool-launch');
    const launchData = result['newtab-tool-launch'] as NewTabLaunchData;
    
    if (launchData) {
      // 清理数据
      await browser.storage.local.remove('newtab-tool-launch');
      
      // 启动工具
      await this.launchTool(launchData.toolId, launchData.data);
    } else {
      this.showToolSelector();
    }
  }
  
  private async launchTool(toolId: string, data?: any) {
    try {
      const tool = toolRegistry.getById(toolId);
      if (!tool) {
        throw new Error(`工具 ${toolId} 不存在`);
      }
      
      this.currentTool = tool;
      
      // 调用初始化钩子
      if (tool.onNewTabInit) {
        await tool.onNewTabInit();
      }
      
      // 执行工具
      if (typeof tool.action === 'function') {
        await tool.action();
      }
      
      this.hideLoading();
      this.showToolContainer();
      
    } catch (error) {
      console.error(`❌ 启动工具失败:`, error);
      this.showError('工具启动失败');
      this.showToolSelector();
    }
  }
  
  private showToolSelector() {
    // 实现工具选择界面
    const selector = document.getElementById('tool-selector')!;
    selector.style.display = 'block';
    
    // 渲染工具列表
    this.renderToolSelector();
  }
  
  private renderToolSelector() {
    const container = document.getElementById('selector-tools')!;
    const tools = toolRegistry.getEnabled();
    
    container.innerHTML = tools.map(tool => `
      <div class="tool-card" data-tool-id="${tool.id}">
        <span class="tool-icon">${tool.icon}</span>
        <div class="tool-info">
          <div class="tool-name">${tool.name}</div>
          <div class="tool-description">${tool.description}</div>
        </div>
        <button class="launch-btn" data-tool-id="${tool.id}">启动</button>
      </div>
    `).join('');
    
    // 添加点击事件
    container.querySelectorAll('.launch-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        const toolId = (e.target as HTMLElement).dataset.toolId;
        if (toolId) {
          this.launchTool(toolId);
        }
      });
    });
  }
  
  private showLoading() {
    document.getElementById('loading')!.style.display = 'flex';
  }
  
  private hideLoading() {
    document.getElementById('loading')!.style.display = 'none';
  }
  
  private showToolContainer() {
    document.getElementById('tool-container')!.style.display = 'block';
  }
  
  private showError(message: string) {
    const container = document.getElementById('tool-container')!;
    container.innerHTML = `<div class="error">${message}</div>`;
  }
}

// 启动管理器
new NewTabManager();
```

### 第六步：添加样式

#### 6.1 创建基础样式

编辑 `entrypoints/newtab/style.css`：

```css
/* 基础样式 */
:root {
  --primary-color: #4f46e5;
  --background-color: #f8fafc;
  --surface-color: #ffffff;
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --border-color: #e2e8f0;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background-color: var(--background-color);
  color: var(--text-primary);
  min-height: 100vh;
}

/* 加载状态 */
.loading-screen {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
}

.loading-spinner {
  font-size: 4rem;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 工具选择界面 */
.tool-selector {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.selector-tools {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
}

.tool-card {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: 1rem;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  cursor: pointer;
  transition: all 0.2s;
}

.tool-card:hover {
  border-color: var(--primary-color);
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
}

.launch-btn {
  background: var(--primary-color);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  cursor: pointer;
}

.launch-btn:hover {
  opacity: 0.9;
}
```

### 第七步：创建示例工具

#### 7.1 创建 New Tab 工具

创建 `tools/example-newtab-tool.ts`：

```typescript
import { BaseTool } from '../utils/tool-template';

export class ExampleNewTabTool extends BaseTool {
  id = 'example-newtab-tool';
  name = 'New Tab 工具示例';
  description = '一个在新标签页中运行的工具示例';
  icon = '🚀';
  categories = ['all', 'development'];
  displayMode = 'newtab';  // 关键：指定为newtab模式
  
  async action(): Promise<void> {
    try {
      console.log('🎬 NewTab工具开始执行');
      
      // 创建工具界面
      this.createInterface();
      
      // 初始化功能
      await this.initializeFeatures();
      
      console.log('✅ NewTab工具执行完成');
      
    } catch (error) {
      console.error('❌ NewTab工具执行失败:', error);
      throw error;
    }
  }
  
  private createInterface(): void {
    const container = document.getElementById('tool-container');
    if (!container) return;
    
    container.innerHTML = `
      <div class="newtab-tool">
        <header>
          <h1>${this.icon} ${this.name}</h1>
          <button onclick="window.close()">关闭</button>
        </header>
        
        <main>
          <div class="content">
            <h2>欢迎来到 New Tab 工具</h2>
            <p>这是一个全屏工具的示例界面</p>
            
            <div class="features">
              <div class="feature">
                <h3>🎯 功能一</h3>
                <p>全屏工作区</p>
              </div>
              <div class="feature">
                <h3>📊 功能二</h3>
                <p>丰富的交互体验</p>
              </div>
              <div class="feature">
                <h3>⚡ 功能三</h3>
                <p>高性能运行</p>
              </div>
            </div>
          </div>
        </main>
      </div>
    `;
  }
  
  private async initializeFeatures(): Promise<void> {
    // 初始化工具功能
    console.log('🔧 初始化工具功能...');
    
    // 模拟异步初始化
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    console.log('✅ 工具功能初始化完成');
  }
}
```

#### 7.2 注册新工具

编辑 `entrypoints/popup/main.ts`：

```typescript
// 在 initApp() 函数中添加
import { ExampleNewTabTool } from '../../tools/example-newtab-tool';

async function initApp() {
  try {
    // 现有初始化代码...
    
    // 注册New Tab工具
    await registerToolWithSettings(new ExampleNewTabTool());
    
    // 创建工具管理器
    new ToolManager();
    
  } catch (error) {
    console.error('应用初始化失败:', error);
  }
}
```

### 第八步：测试验证

#### 8.1 基础功能测试

1. **构建项目**：
   ```bash
   npm run build
   ```

2. **加载扩展**：
   - 在 Chrome 中加载扩展
   - 确认 New Tab 页面被替换

3. **测试跳转**：
   - 打开 popup
   - 点击 New Tab 工具
   - 验证是否跳转到新标签页

#### 8.2 功能验证清单

- [ ] popup 中正常工具正常执行
- [ ] newtab 工具正确跳转
- [ ] 数据传递准确
- [ ] 工具选择界面正常显示
- [ ] 样式加载正确
- [ ] 错误处理机制工作

#### 8.3 调试技巧

1. **查看控制台日志**：
   - New Tab 页面的开发者工具
   - popup 的控制台日志

2. **检查存储数据**：
   ```javascript
   // 在控制台中查看
   chrome.storage.local.get('newtab-tool-launch')
   ```

3. **验证权限**：
   - 检查 `chrome_url_overrides` 权限
   - 确认 storage 权限

## 常见问题

### Q1: New Tab 页面不显示
**解决方案**：
- 检查 `wxt.config.ts` 中的 `chrome_url_overrides` 配置
- 确认文件路径正确
- 重新构建并加载扩展

### Q2: 工具跳转失败
**解决方案**：
- 检查工具的 `displayMode` 设置
- 验证数据存储和读取
- 查看控制台错误信息

### Q3: 样式加载失败
**解决方案**：
- 检查样式文件路径
- 确认 styleManager 配置
- 验证依赖模块加载

### Q4: 数据传递失败
**解决方案**：
- 检查 Chrome Storage 权限
- 验证数据序列化
- 确认数据清理逻辑

## 部署注意事项

1. **权限要求**：
   - 确保 `chrome_url_overrides` 权限
   - 检查 storage 权限

2. **性能优化**：
   - 优化 New Tab 页面加载速度
   - 实现资源懒加载

3. **用户体验**：
   - 提供清晰的加载状态
   - 实现友好的错误提示

4. **兼容性**：
   - 测试不同 Chrome 版本
   - 验证在不同操作系统上的表现

## 下一步

完成基础实施后，可以考虑：

1. **添加更多 New Tab 工具**
2. **实现工具间通信**
3. **添加数据持久化**
4. **优化用户界面**
5. **添加快捷键支持**

这份实施指南提供了完整的实现步骤，按照这个流程可以成功实现 New Tab Page 工具跳转功能。