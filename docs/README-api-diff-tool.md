# API Diff Tool

> 强大的接口差异对比工具，专为开发者设计的浏览器扩展

[![Version](https://img.shields.io/badge/version-1.0.0-blue.svg)](https://github.com/your-repo/api-diff-tool)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![Tests](https://img.shields.io/badge/tests-passing-brightgreen.svg)](tests/)

## ✨ 特性

- 🔄 **双端点对比** - 同时请求新旧接口，智能对比响应差异
- 📋 **cURL 导入** - 从 cURL 命令快速导入请求配置
- 🎨 **可视化差异** - 直观的差异高亮和统计分析
- 💾 **配置管理** - 保存、加载、导入、导出请求配置
- 🔐 **多种认证** - 支持 Bearer Token、Basic Auth、自定义头部
- 📱 **响应式设计** - 适配不同屏幕尺寸
- ⚡ **高性能** - 并发请求执行，快速响应

## 🚀 快速开始

### 安装

1. 克隆项目
```bash
git clone https://github.com/your-repo/api-diff-tool.git
cd api-diff-tool
```

2. 安装依赖
```bash
npm install
```

3. 构建扩展
```bash
npm run build
```

4. 在浏览器中加载扩展
- Chrome: 打开 `chrome://extensions/`，启用开发者模式，加载 `.output/chrome-mv3` 目录
- Firefox: 打开 `about:debugging`，临时载入 `.output/firefox-mv2` 目录

### 使用

1. 点击扩展图标
2. 选择 "接口 Diff 测试台"
3. 配置新旧接口 URL
4. 点击 "开始对比"
5. 查看差异分析结果

## 📖 文档

- [完整使用说明](docs/api-diff-tool.md)
- [手动测试指南](docs/manual-testing-guide.md)
- [测试报告模板](docs/test-report-template.md)

## 🧪 测试

### 运行所有测试
```bash
npm run test:all
```

### 单独运行测试
```bash
# 单元测试
npm run test:run

# 端到端测试
npm run test:e2e

# 集成测试
npm run test:integration

# 构建验证
npm run validate:build
```

### 测试覆盖率
```bash
npm run test:coverage
```

## 🏗️ 开发

### 开发模式
```bash
npm run dev
```

### 代码检查
```bash
npm run compile
```

### 构建生产版本
```bash
npm run build
```

## 📁 项目结构

```
api-diff-tool/
├── tools/api-diff-tool.ts          # 主工具类
├── tools/api-diff/                 # 工具组件
│   ├── types/                      # 类型定义
│   ├── utils/                      # 工具函数
│   ├── components/                 # 核心组件
│   └── styles/                     # 样式文件
├── tests/                          # 测试文件
│   ├── api-diff/                   # 单元测试
│   └── e2e/                        # 端到端测试
├── docs/                           # 文档
├── scripts/                        # 构建脚本
└── package.json                    # 项目配置
```

## 🔧 核心组件

### 工具类
- `ApiDiffTool` - 主工具类，负责整体协调
- `RequestBuilder` - 请求构建器
- `DualRequestExecutor` - 双端请求执行器
- `ResponseRenderer` - 响应渲染器
- `DiffRenderer` - 差异渲染器
- `ConfigManager` - 配置管理器

### 工具函数
- `ValidationUtils` - 验证工具
- `HttpRequestUtils` - HTTP 请求工具
- `CurlParser` - cURL 解析器
- `DiffUtils` - 差异计算工具

## 🎯 使用场景

### API 版本升级验证
```
旧版本: https://api.example.com/v1/users
新版本: https://api.example.com/v2/users
```

### 环境一致性检查
```
测试环境: https://test-api.example.com/users
生产环境: https://api.example.com/users
```

### A/B 测试对比
```
版本A: https://api.example.com/users?version=a
版本B: https://api.example.com/users?version=b
```

## 🤝 贡献

欢迎贡献代码！请遵循以下步骤：

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

### 开发规范

- 使用 TypeScript 编写代码
- 遵循 ESLint 规则
- 编写单元测试
- 更新相关文档

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🙏 致谢

- [WXT Framework](https://wxt.dev/) - 扩展开发框架
- [Vitest](https://vitest.dev/) - 测试框架
- [TypeScript](https://www.typescriptlang.org/) - 类型安全

## 📞 支持

如果您遇到问题或有建议，请：

- 查看 [文档](docs/api-diff-tool.md)
- 提交 [Issue](https://github.com/your-repo/api-diff-tool/issues)
- 发起 [讨论](https://github.com/your-repo/api-diff-tool/discussions)

---

**开发者**: [您的名字]  
**邮箱**: <EMAIL>  
**版本**: 1.0.0
