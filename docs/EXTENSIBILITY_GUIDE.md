# 可扩展性架构指南

本指南介绍了WXT浏览器扩展项目的可扩展性架构改进，包括工具生成器、依赖管理、版本控制和生命周期管理等功能。

## 🚀 快速开始

### 使用工具生成器创建新工具

```bash
# 使用npm脚本
npm run create-tool

# 或者直接运行
npm run tool:create
```

工具生成器将引导您完成以下步骤：
1. 输入工具ID（kebab-case格式）
2. 设置工具名称和描述
3. 选择工具图标（emoji）
4. 选择工具分类（productivity/development/utility）
5. 选择工具类型（basic/ui/api/content）

## 🏗️ 工具类型说明

### 1. 基础工具 (basic)
适用于简单的功能工具，执行单一操作。

```typescript
export class MyBasicTool extends BaseTool {
  id = 'my-basic-tool';
  name = '我的基础工具';
  version = { major: 1, minor: 0, patch: 0 };
  
  async action(): Promise<void> {
    // 实现工具功能
  }
}
```

### 2. UI工具 (ui)
需要用户界面交互的工具，自动生成模态框模板。

```typescript
export class MyUITool extends BaseTool {
  async action(): Promise<void> {
    const modal = this.createModal();
    document.body.appendChild(modal);
  }
  
  private createModal(): HTMLElement {
    // 模态框创建逻辑
  }
}
```

### 3. API工具 (api)
需要调用外部API的工具，包含HTTP请求处理。

```typescript
export class MyAPITool extends BaseTool {
  private async callAPI(param: string): Promise<APIResponse> {
    return await this.httpJSON('/api/endpoint', {
      method: 'POST',
      body: JSON.stringify({ param })
    });
  }
}
```

### 4. 内容脚本工具 (content)
需要在页面中执行脚本的工具。

```typescript
export class MyContentTool extends BaseTool {
  async action(): Promise<void> {
    const result = await this.executeScript(() => {
      return document.title;
    });
    
    await this.processResult(result[0].result);
  }
}
```

## 📦 依赖管理

### 声明依赖

```typescript
export class MyTool extends BaseTool {
  dependencies = [
    { id: 'base-tool', version: '^1.0.0' },
    { id: 'optional-tool', version: '~2.1.0', optional: true }
  ];
}
```

### 版本格式说明

- `^1.0.0` - 兼容主版本（1.x.x）
- `~1.2.0` - 兼容次版本（1.2.x）
- `1.0.0` - 精确匹配

### 依赖检查

工具注册时会自动检查：
- 必需依赖是否存在
- 版本兼容性
- 循环依赖

## 🔄 生命周期管理

### 生命周期钩子

```typescript
export class MyTool extends BaseTool {
  async onInit(): Promise<void> {
    // 工具初始化逻辑
    console.log('工具初始化');
  }
  
  async onDestroy(): Promise<void> {
    // 工具销毁逻辑
    console.log('工具销毁');
  }
  
  async onEnable(): Promise<void> {
    // 工具启用逻辑
    console.log('工具启用');
  }
  
  async onDisable(): Promise<void> {
    // 工具禁用逻辑
    console.log('工具禁用');
  }
}
```

### 生命周期状态

- `uninitialized` - 未初始化
- `initializing` - 初始化中
- `ready` - 就绪
- `running` - 运行中
- `error` - 错误
- `destroyed` - 已销毁

## 💬 工具间通信

### 发送消息

```typescript
export class SenderTool extends BaseTool {
  async sendDataToOtherTool() {
    try {
      const response = await this.sendMessage('receiver-tool', {
        type: 'data-request',
        payload: { key: 'value' }
      });
      
      console.log('收到响应:', response);
    } catch (error) {
      console.error('通信失败:', error);
    }
  }
}
```

### 接收消息

```typescript
export class ReceiverTool extends BaseTool {
  async onInit(): Promise<void> {
    this.onMessage((from, message) => {
      if (message.type === 'data-request') {
        return { status: 'success', data: 'response data' };
      }
    });
  }
}
```

## ⚙️ 配置管理

### 保存和读取配置

```typescript
export class ConfigurableTool extends BaseTool {
  async saveSettings() {
    await this.setConfig('apiUrl', 'https://api.example.com');
    await this.setConfig('timeout', 5000);
  }
  
  async loadSettings() {
    const apiUrl = await this.getConfig('apiUrl', 'https://default.api.com');
    const timeout = await this.getConfig('timeout', 3000);
  }
}
```

## 💾 数据持久化

### 保存和加载数据

```typescript
export class DataTool extends BaseTool {
  async saveUserData(data: any) {
    await this.saveData('userData', data);
  }
  
  async loadUserData() {
    return await this.loadData('userData', {});
  }
  
  async clearUserData() {
    await this.removeData('userData');
  }
}
```

## 🌐 HTTP请求

### 基础HTTP请求

```typescript
export class HTTPTool extends BaseTool {
  async fetchData() {
    // 基础请求
    const response = await this.httpRequest('https://api.example.com/data');
    const text = await response.text();
    
    // JSON请求
    const jsonData = await this.httpJSON('https://api.example.com/json');
    
    // POST请求
    const postData = await this.httpJSON('https://api.example.com/post', {
      method: 'POST',
      body: JSON.stringify({ key: 'value' })
    });
  }
}
```

## ✅ 表单验证

### 使用内置验证器

```typescript
export class FormTool extends BaseTool {
  private validateUserForm(form: HTMLFormElement) {
    const errors = this.validateForm(form, {
      email: BaseTool.validators.email,
      password: BaseTool.validators.minLength(8),
      website: BaseTool.validators.url,
      username: (value) => {
        if (!BaseTool.validators.required(value)) {
          return BaseTool.validators.pattern(/^[a-zA-Z0-9_]+$/, '用户名只能包含字母、数字和下划线')(value);
        }
        return BaseTool.validators.required(value);
      }
    });
    
    return Object.keys(errors).length === 0;
  }
}
```

## 🔧 工具注册表API

### 管理工具生命周期

```typescript
import { toolRegistry } from '@/utils/tool-registry';

// 启用/禁用工具
await toolRegistry.enableTool('my-tool');
await toolRegistry.disableTool('my-tool');

// 销毁工具
await toolRegistry.destroyTool('my-tool');

// 检查依赖
const cycles = toolRegistry.checkCircularDependencies();
if (cycles.length > 0) {
  console.warn('发现循环依赖:', cycles);
}

// 获取依赖图
const graph = toolRegistry.getDependencyGraph();
```

## 🎯 最佳实践

1. **遵循单一职责原则** - 每个工具只做一件事
2. **合理使用依赖** - 避免过度依赖和循环依赖
3. **实现生命周期钩子** - 正确初始化和清理资源
4. **错误处理** - 使用try-catch处理异常
5. **用户体验** - 提供清晰的反馈和通知
6. **性能优化** - 使用防抖和节流优化用户交互
7. **数据安全** - 敏感数据使用加密存储

## 🐛 调试技巧

1. 查看控制台日志了解工具注册和生命周期状态
2. 使用Chrome DevTools检查存储数据
3. 监听工具通信事件进行调试
4. 检查依赖图排查依赖问题

## 📚 更多资源

- [工具迁移指南](./MIGRATION_GUIDE.md)
- [样式管理指南](./STYLE_GUIDE.md)
- [API参考文档](./API_REFERENCE.md)
