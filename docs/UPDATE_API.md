# 更新API接口文档

本文档描述了fwyy-tools扩展的更新API接口规范和使用方法。

## 🔗 API端点

### 基础URL
- **生产环境**: `https://api.fwyy-tools.com/v1`
- **开发环境**: `http://localhost:3000/api/v1`

## 📋 接口列表

### 1. 检查单个工具更新

**端点**: `POST /updates/check`

**请求体**:
```json
{
  "toolId": "xuid",
  "currentVersion": {
    "major": 1,
    "minor": 0,
    "patch": 0
  },
  "platform": "Win32",
  "environment": "production"
}
```

**响应**:
```json
{
  "success": true,
  "code": 200,
  "message": "检查完成",
  "data": {
    "toolId": "xuid",
    "currentVersion": {
      "major": 1,
      "minor": 0,
      "patch": 0
    },
    "latestVersion": {
      "major": 1,
      "minor": 0,
      "patch": 1
    },
    "hasUpdate": true,
    "updateType": "patch",
    "releaseDate": "2024-01-15T10:00:00Z",
    "downloadUrl": "https://releases.fwyy-tools.com/xuid/v1.0.1.zip",
    "downloadSize": 1024000,
    "checksum": "sha256:abc123...",
    "changelog": [
      {
        "type": "bugfix",
        "description": "修复了XUID切换问题",
        "issueId": "#123"
      }
    ],
    "compatibility": {
      "minExtensionVersion": "1.0.0",
      "supportedBrowsers": ["chrome", "firefox", "edge"]
    },
    "updatePriority": "medium",
    "isSecurityUpdate": false
  },
  "timestamp": 1705320000000
}
```

### 2. 批量检查工具更新

**端点**: `POST /updates/batch-check`

**请求体**:
```json
{
  "tools": [
    {
      "toolId": "xuid",
      "currentVersion": {
        "major": 1,
        "minor": 0,
        "patch": 0
      },
      "platform": "Win32",
      "environment": "production"
    },
    {
      "toolId": "alert-parser",
      "currentVersion": {
        "major": 2,
        "minor": 1,
        "patch": 0
      },
      "platform": "Win32",
      "environment": "production"
    }
  ],
  "clientInfo": {
    "extensionVersion": "1.0.0",
    "browserName": "Chrome",
    "browserVersion": "120.0",
    "platform": "Win32"
  }
}
```

**响应**:
```json
{
  "success": true,
  "code": 200,
  "message": "批量检查完成",
  "data": {
    "updates": [
      {
        "toolId": "xuid",
        "hasUpdate": true,
        "currentVersion": {
          "major": 1,
          "minor": 0,
          "patch": 0
        },
        "latestVersion": {
          "major": 1,
          "minor": 0,
          "patch": 1
        },
        "updateType": "patch",
        "downloadUrl": "https://releases.fwyy-tools.com/xuid/v1.0.1.zip",
        "updatePriority": "medium",
        "isSecurityUpdate": false,
        "changelog": [...]
      }
    ],
    "summary": {
      "totalTools": 2,
      "updatesAvailable": 1,
      "criticalUpdates": 0,
      "securityUpdates": 0
    }
  },
  "timestamp": 1705320000000
}
```

## 🔧 使用示例

### TypeScript客户端使用

```typescript
import { updateApiClient } from '../utils/update-api-client';
import { UpdateCheckRequest } from '../utils/update-api-types';

// 检查单个工具更新
const checkSingleUpdate = async () => {
  const request: UpdateCheckRequest = {
    toolId: 'xuid',
    currentVersion: { major: 1, minor: 0, patch: 0 },
    platform: 'Win32',
    environment: 'production'
  };

  try {
    const response = await updateApiClient.checkUpdate(request);
    if (response.success && response.data?.hasUpdate) {
      console.log('发现更新:', response.data.latestVersion);
    }
  } catch (error) {
    console.error('检查更新失败:', error);
  }
};

// 批量检查更新
const checkBatchUpdates = async (tools: Tool[]) => {
  const request = {
    tools: tools.map(tool => ({
      toolId: tool.id,
      currentVersion: tool.version,
      platform: navigator.platform,
      environment: 'production'
    })),
    clientInfo: {
      extensionVersion: '1.0.0',
      browserName: 'Chrome',
      browserVersion: '120.0',
      platform: navigator.platform
    }
  };

  try {
    const response = await updateApiClient.batchCheckUpdates(request);
    if (response.success) {
      console.log('更新摘要:', response.data?.summary);
    }
  } catch (error) {
    console.error('批量检查失败:', error);
  }
};
```

## 🚨 错误处理

### 错误代码

| 代码 | 说明 | 处理建议 |
|------|------|----------|
| 1000 | 未知错误 | 重试或联系支持 |
| 1001 | 请求参数无效 | 检查请求格式 |
| 1002 | 认证失败 | 检查API密钥 |
| 1003 | 权限不足 | 联系管理员 |
| 1004 | 请求频率限制 | 降低请求频率 |
| 2001 | 工具不存在 | 检查工具ID |
| 3001 | 版本不存在 | 检查版本号 |
| 4001 | 下载失败 | 重试下载 |
| 5001 | 服务器错误 | 稍后重试 |

### 错误响应示例

```json
{
  "success": false,
  "code": 2001,
  "message": "工具不存在",
  "data": null,
  "timestamp": 1705320000000
}
```

## 🔒 安全考虑

1. **HTTPS**: 所有API调用必须使用HTTPS
2. **校验和**: 下载文件时验证SHA256校验和
3. **签名验证**: 验证下载文件的数字签名
4. **版本兼容性**: 检查最低扩展版本要求
5. **频率限制**: 遵守API调用频率限制

## 📊 监控和统计

API提供了丰富的监控和统计功能：

- 下载次数统计
- 更新成功率
- 平均更新时间
- 版本分布情况
- 错误率监控

这些数据可以通过 `/stats/updates` 端点获取。
