# 更新流程图

本文档描述了fwyy-tools扩展的完整更新流程，包括系统架构、更新检查、用户交互和API调用等各个环节。

## 📋 概述

更新系统采用模块化设计，主要包含以下核心组件：
- **UpdateManager**: 更新管理核心，协调整个更新流程
- **VersionManager**: 版本管理和API调用
- **UpdateNotificationManager**: 更新通知管理
- **UpdateDetailModal**: 更新详情界面
- **UpdateApiClient**: API客户端（计划实现）

## 🏗️ 系统架构图

```mermaid
graph TB
    subgraph "扩展入口"
        A[扩展启动] --> B[ToolManager]
        B --> C[加载工具列表]
    end
    
    subgraph "更新管理层"
        D[UpdateManager] --> E[VersionManager]
        D --> F[UpdateNotificationManager]
        D --> G[SettingsManager]
    end
    
    subgraph "UI层"
        H[UpdateBanner] --> I[UpdateDetailModal]
        F --> H
        I --> J[用户操作]
    end
    
    subgraph "存储层"
        K[browser.storage.local]
        D --> K
        G --> K
    end
    
    subgraph "API层"
        L[UpdateApiClient]
        E --> L
        L --> M[更新API服务器]
    end
    
    C --> D
    J --> D
```

## 🔄 更新检查流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant TM as ToolManager
    participant UM as UpdateManager
    participant VM as VersionManager
    participant API as UpdateApiClient
    participant Storage as browser.storage
    participant UI as UpdateNotification

    Note over User,UI: 扩展启动流程
    User->>TM: 启动扩展
    TM->>UM: 初始化更新管理器
    UM->>Storage: 加载更新状态
    Storage-->>UM: 返回已保存状态
    UM->>UM: 加载通知选项
    
    Note over User,UI: 自动更新检查
    TM->>UM: 检查工具更新
    UM->>UM: 判断是否需要检查
    alt 需要检查更新
        UM->>VM: 批量检查更新
        VM->>API: 调用更新API
        API->>API: 模拟API调用
        API-->>VM: 返回更新结果
        VM-->>UM: 返回检查结果
        UM->>UM: 处理更新结果
        UM->>Storage: 保存更新状态
        UM->>UI: 触发更新事件
        UI->>User: 显示更新通知
    else 无需检查
        UM-->>TM: 跳过检查
    end
```

## 👤 用户交互流程

```mermaid
flowchart TD
    A[用户看到更新横幅] --> B{用户操作}
    
    B -->|点击查看详情| C[打开更新详情模态框]
    B -->|点击稍后提醒| D[暂时关闭通知]
    B -->|点击关闭| E[隐藏横幅]
    
    C --> F[显示工具更新列表]
    F --> G{用户选择操作}
    
    G -->|全量更新| H[打开更新页面]
    G -->|忽略所有| I[标记所有更新为已忽略]
    G -->|关闭| J[关闭模态框]
    
    H --> K[显示下载链接]
    K --> L[用户手动下载更新]
    
    D --> M[调用dismissAllUpdates]
    I --> N[调用ignoreUpdate]
    
    M --> O[更新状态到存储]
    N --> O
    O --> P[触发UI更新事件]
    P --> Q[隐藏相关通知]
```

## 🌐 API交互流程

```mermaid
sequenceDiagram
    participant VM as VersionManager
    participant Cache as 本地缓存
    participant API as UpdateApiClient
    participant Server as 更新服务器

    Note over VM,Server: 单个工具更新检查
    VM->>Cache: 检查缓存
    alt 缓存有效
        Cache-->>VM: 返回缓存结果
    else 缓存过期或不存在
        VM->>API: 调用fetchUpdateInfo
        API->>API: 模拟网络延迟
        API->>API: 生成模拟更新数据
        API-->>VM: 返回更新信息
        VM->>Cache: 缓存结果
    end

    Note over VM,Server: 批量更新检查
    VM->>VM: 分批处理工具列表
    loop 每批工具
        VM->>API: 并发检查更新
        API-->>VM: 返回批次结果
    end
    VM->>VM: 合并所有结果
```

## 📊 状态管理流程

```mermaid
stateDiagram-v2
    [*] --> 初始化
    初始化 --> 加载状态: 从存储加载
    加载状态 --> 检查更新: 自动检查
    加载状态 --> 等待操作: 手动模式
    
    检查更新 --> 有更新: API返回更新
    检查更新 --> 无更新: 无可用更新
    
    有更新 --> 显示通知: 用户未忽略
    有更新 --> 静默处理: 用户已忽略
    
    显示通知 --> 用户交互: 用户操作
    
    用户交互 --> 查看详情: 点击详情
    用户交互 --> 暂时关闭: 稍后提醒
    用户交互 --> 忽略更新: 忽略操作
    
    查看详情 --> 下载更新: 全量更新
    查看详情 --> 忽略更新: 忽略所有
    
    暂时关闭 --> 等待操作: 设置dismissed标志
    忽略更新 --> 等待操作: 设置ignored标志
    下载更新 --> 等待操作: 用户手动安装
    
    无更新 --> 等待操作
    静默处理 --> 等待操作
    
    等待操作 --> 检查更新: 定时检查
    等待操作 --> [*]: 扩展关闭
```

## ⚙️ 配置和设置流程

```mermaid
flowchart LR
    A[用户打开设置] --> B[更新设置面板]
    
    B --> C{修改设置}
    C -->|自动检查更新| D[更新autoCheck选项]
    C -->|检查间隔| E[更新checkInterval]
    C -->|通知方式| F[更新通知样式]
    
    D --> G[保存到SettingsManager]
    E --> G
    F --> G
    
    G --> H[UpdateManager重新加载配置]
    H --> I[应用新的更新策略]
```

## 🔧 核心组件详解

### UpdateManager
- **职责**: 更新流程的核心协调器
- **主要方法**:
  - `checkAndNotifyUpdates()`: 检查并通知更新
  - `ignoreUpdate()`: 忽略特定工具更新
  - `dismissUpdate()`: 暂时关闭更新通知
  - `getUpdateCount()`: 获取可用更新数量

### VersionManager
- **职责**: 版本检查和API调用
- **主要方法**:
  - `checkForUpdate()`: 检查单个工具更新
  - `checkMultipleUpdates()`: 批量检查更新
  - `fetchUpdateInfo()`: 获取更新信息（当前为模拟）

### UpdateNotificationManager
- **职责**: 管理更新通知的显示
- **主要方法**:
  - `showUpdateBanner()`: 显示更新横幅
  - `hideBanner()`: 隐藏横幅
  - `showUpdateDetails()`: 显示更新详情

### UpdateDetailModal
- **职责**: 更新详情界面
- **功能**:
  - 显示版本对比信息
  - 展示更新日志
  - 提供批量操作选项
  - 生成下载页面

## 🚨 错误处理

```mermaid
flowchart TD
    A[API调用] --> B{调用成功?}
    B -->|是| C[处理响应数据]
    B -->|否| D[错误处理]
    
    D --> E{错误类型}
    E -->|网络错误| F[显示网络错误提示]
    E -->|超时| G[显示超时提示]
    E -->|服务器错误| H[显示服务器错误提示]
    
    F --> I[记录错误日志]
    G --> I
    H --> I
    
    I --> J[返回默认结果]
    C --> K[缓存结果]
    J --> L[继续执行]
    K --> L
```

## 📈 性能优化策略

1. **缓存机制**: API响应缓存5分钟，避免重复请求
2. **批量处理**: 限制并发数为3，避免过多同时请求
3. **状态持久化**: 更新状态保存到本地存储
4. **事件驱动**: 使用CustomEvent进行组件间通信
5. **延迟加载**: 模态框按需创建，减少内存占用

## 🔮 未来改进计划

1. **真实API集成**: 替换模拟API为真实的更新服务
2. **增量更新**: 支持工具的增量更新机制
3. **自动安装**: 在允许的情况下支持自动更新安装
4. **更新统计**: 添加更新成功率和使用统计
5. **回滚机制**: 支持更新失败后的版本回滚

---

## 📞 相关文档

- [更新API文档](./UPDATE_API.md)
- [更新功能实现](./UPDATE_FEATURE_IMPLEMENTATION.md)
- [样式指南](./STYLE_GUIDE.md)
- [扩展性指南](./EXTENSIBILITY_GUIDE.md)