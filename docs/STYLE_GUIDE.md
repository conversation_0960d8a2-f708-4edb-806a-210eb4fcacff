# 样式管理系统指南

本指南介绍了WXT浏览器扩展项目的样式管理系统重构，包括设计令牌、组件库和模块化管理等功能。

## 🔧 重要更新：模态框统一化

**所有工具的模态框现已统一规范！** 确保一致的用户体验：
- 头部高度：48px，内边距：16px 20px
- 标题样式：18px字体，600粗细
- 主体内边距：20px
- 底部高度：48px，内边距：16px 20px

**开发者注意：** 创建新工具时，必须遵循统一的模态框规范，不得自定义头部、标题或底部尺寸。

## 🎨 设计令牌系统

### 颜色系统
我们建立了完整的颜色体系，包括主色调、中性色和语义化颜色：

```css
/* 主色调 */
--color-primary-500: #6366f1;
--color-primary-600: #4f46e5;
--color-primary-700: #4338ca;

/* 语义化颜色 */
--success: var(--color-success-600);
--warning: var(--color-warning-600);
--error: var(--color-error-600);
--info: var(--color-info-600);
```

### 字体系统
统一的字体规范：

```css
/* 字体大小 */
--font-size-xs: 0.75rem;    /* 12px */
--font-size-sm: 0.875rem;   /* 14px */
--font-size-base: 1rem;     /* 16px */
--font-size-lg: 1.125rem;   /* 18px */

/* 字体粗细 */
--font-weight-normal: 400;
--font-weight-medium: 500;
--font-weight-semibold: 600;
--font-weight-bold: 700;
```

### 间距系统
基于4px网格的间距系统：

```css
--spacing-1: 0.25rem;   /* 4px */
--spacing-2: 0.5rem;    /* 8px */
--spacing-3: 0.75rem;   /* 12px */
--spacing-4: 1rem;      /* 16px */
--spacing-5: 1.25rem;   /* 20px */
--spacing-6: 1.5rem;    /* 24px */
--spacing-8: 2rem;      /* 32px */
```

### 组件令牌
组件特定的设计令牌，确保一致性：

```css
/* 模态框组件令牌 */
--modal-header-padding: var(--spacing-4) var(--spacing-5);
--modal-body-padding: var(--spacing-5);
--modal-footer-padding: var(--spacing-4) var(--spacing-5);
--modal-header-min-height: 3rem;
--modal-footer-min-height: 3rem;
--modal-title-font-size: var(--font-size-lg);
--modal-title-font-weight: var(--font-weight-semibold);

/* 按钮组件令牌 */
--button-padding-x: var(--spacing-4);
--button-padding-y: var(--spacing-2);
--button-radius: var(--radius-md);
--button-font-weight: var(--font-weight-medium);
```

## 🧩 组件库

### 按钮组件
```html
<!-- 基础按钮 -->
<button class="btn btn-primary">主要按钮</button>
<button class="btn btn-secondary">次要按钮</button>
<button class="btn btn-outline">轮廓按钮</button>

<!-- 不同尺寸 -->
<button class="btn btn-primary btn-sm">小按钮</button>
<button class="btn btn-primary">默认按钮</button>
<button class="btn btn-primary btn-lg">大按钮</button>

<!-- 状态按钮 -->
<button class="btn btn-success">成功</button>
<button class="btn btn-warning">警告</button>
<button class="btn btn-error">错误</button>
```

### 表单组件
```html
<div class="form-group">
  <label class="form-label">用户名</label>
  <input type="text" class="form-control" placeholder="请输入用户名">
</div>

<div class="form-group">
  <label class="form-label">描述</label>
  <textarea class="form-control form-textarea" placeholder="请输入描述"></textarea>
</div>

<div class="form-check">
  <input type="checkbox" class="form-check-input" id="agree">
  <label class="form-check-label" for="agree">我同意条款</label>
</div>
```

### 卡片组件
```html
<div class="card">
  <div class="card-header">
    <h3 class="card-title">卡片标题</h3>
  </div>
  <div class="card-body">
    <p class="card-text">卡片内容</p>
  </div>
  <div class="card-footer">
    <button class="btn btn-primary">操作</button>
  </div>
</div>
```

### 模态框组件
```typescript
import { Modal } from '@/utils/ui-components';

// 创建模态框
const modal = new Modal('<p>模态框内容</p>', {
  title: '标题',
  size: 'md',
  closable: true
});

// 添加底部按钮
modal.addFooter(`
  <button class="btn btn-secondary" onclick="modal.close()">取消</button>
  <button class="btn btn-primary">确认</button>
`);

// 显示模态框
modal.open();
```

#### 模态框统一规范
所有模态框组件都遵循统一的视觉规范，确保一致的用户体验：

```css
/* 统一的模态框设计令牌 */
:root {
  --modal-header-padding: 16px 20px;
  --modal-body-padding: 20px;
  --modal-footer-padding: 16px 20px;
  --modal-header-min-height: 48px;
  --modal-footer-min-height: 48px;
  --modal-title-font-size: 18px;
  --modal-title-font-weight: 600;
}
```

**规范要点：**
- 头部高度：最小48px，内边距16px 20px
- 标题样式：18px字体，600粗细
- 主体内边距：20px
- 底部高度：最小48px，内边距16px 20px
- 所有工具的模态框必须遵循此规范

## 📦 模块化管理

### 样式管理器
```typescript
import { styleManager, StyleUtils } from '@/utils/style-manager';

// 加载样式模块
await styleManager.loadModule('components');
await styleManager.loadModules(['modal', 'form', 'button']);

// 为工具加载必要样式
await StyleUtils.loadToolStyles('ui'); // 加载UI工具所需样式

// 主题管理
styleManager.setTheme('dark'); // 设置深色主题
styleManager.setTheme('light'); // 设置浅色主题
styleManager.setTheme('auto'); // 自动跟随系统
```

### 可用样式模块
- `design-tokens` - 设计令牌（自动加载）
- `components` - 基础组件样式
- `layout` - 布局工具类
- `utilities` - 原子化工具类
- `animations` - 动画效果
- `modal` - 模态框组件
- `form` - 表单组件
- `button` - 按钮组件
- `card` - 卡片组件
- `toast` - 通知组件
- `dropdown` - 下拉菜单组件

### 按需加载
```typescript
// 懒加载样式模块
await styleManager.lazyLoadModule('animations', () => {
  return document.querySelector('.animate-fade-in') !== null;
});

// 预加载关键模块
await styleManager.preloadCriticalModules();
```

## 🛠️ 工具类系统

### 间距工具类
```html
<!-- 外边距 -->
<div class="m-4">四周边距</div>
<div class="mx-4">水平边距</div>
<div class="my-4">垂直边距</div>
<div class="mt-4">顶部边距</div>

<!-- 内边距 -->
<div class="p-4">四周内边距</div>
<div class="px-4">水平内边距</div>
<div class="py-4">垂直内边距</div>
```

### 文本工具类
```html
<p class="text-lg font-semibold text-primary">大号半粗体主色文本</p>
<p class="text-sm text-secondary">小号次要文本</p>
<p class="text-center">居中文本</p>
<p class="truncate">超长文本会被截断...</p>
```

### 布局工具类
```html
<!-- Flexbox -->
<div class="flex items-center justify-between">
  <span>左侧内容</span>
  <span>右侧内容</span>
</div>

<!-- Grid -->
<div class="grid grid-cols-3 gap-4">
  <div>项目1</div>
  <div>项目2</div>
  <div>项目3</div>
</div>
```

### 背景和边框
```html
<div class="bg-primary text-white p-4 rounded-lg shadow-md">
  主色背景的卡片
</div>

<div class="border border-primary rounded-md p-3">
  带边框的容器
</div>
```

## 🎭 动画系统

### 基础动画
```html
<!-- 淡入淡出 -->
<div class="animate-fade-in">淡入元素</div>
<div class="animate-fade-out">淡出元素</div>

<!-- 滑动动画 -->
<div class="animate-slide-in-up">从下滑入</div>
<div class="animate-slide-in-left">从左滑入</div>

<!-- 缩放动画 -->
<div class="animate-scale-in">缩放进入</div>
```

### 悬停效果
```html
<button class="btn btn-primary hover-lift">悬停上升</button>
<div class="card hover-grow">悬停放大</div>
<img class="hover-rotate" src="image.jpg" alt="悬停旋转">
```

### 加载动画
```html
<div class="spinner"></div>
<div class="spinner spinner-lg"></div>
<div class="loading-dots">加载中</div>
```

## 🌙 主题系统

### 主题切换
```typescript
import { StyleUtils } from '@/utils/style-manager';

// 创建主题切换器
const themeToggle = StyleUtils.createThemeToggle();
document.body.appendChild(themeToggle);

// 检测系统主题
const systemTheme = StyleUtils.getSystemTheme();
console.log('系统主题:', systemTheme);
```

### 自定义主题
```typescript
import { styleManager } from '@/utils/style-manager';

// 应用自定义主题
styleManager.applyCustomTheme({
  name: 'custom-theme',
  variables: {
    '--primary': '#ff6b6b',
    '--background': '#f8f9fa',
    '--text-primary': '#212529'
  }
});

// 动态设置CSS变量
styleManager.setCSSVariable('--custom-color', '#42a5f5');
```

## 📱 响应式设计

### 响应式工具类
```html
<!-- 在不同屏幕尺寸显示不同布局 -->
<div class="block md:hidden">仅在小屏幕显示</div>
<div class="hidden md:block">仅在中等及以上屏幕显示</div>

<!-- 响应式网格 -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
  <div>项目1</div>
  <div>项目2</div>
  <div>项目3</div>
</div>
```

## 🎯 最佳实践

### 1. 使用设计令牌
```css
/* ✅ 推荐 */
.my-component {
  padding: var(--spacing-4);
  color: var(--text-primary);
  border-radius: var(--radius-md);
}

/* ❌ 不推荐 */
.my-component {
  padding: 16px;
  color: #111827;
  border-radius: 6px;
}
```

### 2. 优先使用工具类
```html
<!-- ✅ 推荐 -->
<div class="flex items-center gap-3 p-4 bg-surface rounded-lg">
  内容
</div>

<!-- ❌ 不推荐 -->
<div style="display: flex; align-items: center; gap: 12px; padding: 16px; background: #f9fafb; border-radius: 8px;">
  内容
</div>
```

### 3. 模块化加载
```typescript
// ✅ 推荐 - 按需加载
await styleManager.loadModule('modal'); // 只在需要时加载

// ❌ 不推荐 - 全量加载
import './all-styles.css'; // 加载所有样式
```

### 4. 语义化类名
```html
<!-- ✅ 推荐 -->
<button class="btn btn-primary">提交</button>

<!-- ❌ 不推荐 -->
<button class="blue-button">提交</button>
```

### 5. 模态框统一规范
```html
<!-- ✅ 推荐 - 使用统一的模态框结构 -->
<div class="modal-overlay">
  <div class="modal-content">
    <div class="modal-header">
      <h3 class="modal-title">标题</h3>
      <button class="modal-close">&times;</button>
    </div>
    <div class="modal-body">
      <!-- 内容 -->
    </div>
    <div class="modal-footer">
      <button class="btn btn-secondary">取消</button>
      <button class="btn btn-primary">确认</button>
    </div>
  </div>
</div>

<!-- ❌ 不推荐 - 自定义模态框尺寸 -->
<div class="custom-modal" style="padding: 10px;">
  <h3 style="font-size: 14px;">标题</h3>
  <!-- 不一致的样式 -->
</div>
```

## 🔧 开发工具

### 样式调试
```typescript
// 查看已加载的模块
console.log(styleManager.getLoadedModules());

// 检查模块是否已加载
if (styleManager.isModuleLoaded('components')) {
  console.log('组件样式已加载');
}

// 获取CSS变量值
const primaryColor = styleManager.getCSSVariable('--primary');
console.log('主色:', primaryColor);
```

### 性能优化
- 使用`preloadCriticalModules()`预加载关键样式
- 使用`lazyLoadModule()`按需加载非关键样式
- 定期调用`cleanup()`清理未使用的模块

## 📚 更多资源

- [设计令牌参考](./design-tokens.md)
- [组件API文档](./components-api.md)
- [动画效果演示](./animations-demo.md)
- [主题定制指南](./theme-customization.md)
