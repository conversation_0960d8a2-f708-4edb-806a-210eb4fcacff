# API Diff Tool 使用说明

**版本**: 1.0.0  
**更新日期**: 2024-12-01

## 🎯 工具简介

API Diff Tool 是一个强大的接口差异对比工具，专为开发者设计，用于对比新旧两个 API 接口的响应差异。它提供了直观的可视化界面，支持多种请求方式，并能智能分析和高亮显示接口变化。

### 核心特性

- 🔄 **双端点对比**: 同时请求新旧两个接口，自动对比响应差异
- 📋 **cURL 导入**: 支持从 cURL 命令快速导入请求配置
- 🎨 **可视化差异**: 直观的差异高亮显示和统计分析
- 💾 **配置管理**: 保存、加载、导入、导出请求配置
- 🔐 **多种认证**: 支持 Bearer Token、Basic Auth、自定义头部认证
- 📱 **响应式设计**: 适配不同屏幕尺寸，支持移动端使用
- ⚡ **高性能**: 并发请求执行，快速响应
- 🛡️ **安全可靠**: 本地存储，数据隐私保护

## 🚀 快速开始

### 1. 安装扩展

1. 下载扩展文件或从源码构建
2. 打开浏览器扩展管理页面
   - Chrome: `chrome://extensions/`
   - Edge: `edge://extensions/`
3. 启用"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择扩展目录

### 2. 启动工具

1. 点击浏览器工具栏中的扩展图标
2. 在弹出的工具列表中找到"接口 Diff 测试台"
3. 点击工具名称，将在新标签页中打开

### 3. 基础使用

1. **配置请求**
   - 输入旧接口 URL
   - 输入新接口 URL
   - 选择 HTTP 方法
   - 添加必要的请求头和参数

2. **执行对比**
   - 点击"开始对比"按钮
   - 等待请求完成
   - 查看响应结果和差异分析

3. **查看结果**
   - 在"响应对比"标签查看原始响应
   - 在"差异分析"标签查看变化详情

## 📋 功能详解

### 请求配置

#### HTTP 方法
支持所有标准 HTTP 方法：
- `GET` - 获取数据
- `POST` - 创建数据
- `PUT` - 更新数据
- `PATCH` - 部分更新
- `DELETE` - 删除数据
- `HEAD` - 获取头部信息
- `OPTIONS` - 获取支持的方法

#### 请求头设置
```
Content-Type: application/json
Authorization: Bearer your-token-here
X-Custom-Header: custom-value
```

#### 查询参数
```
page=1
limit=10
search=keyword
```

#### 请求体格式
- **JSON**: 结构化数据，自动验证格式
- **Form Data**: 表单数据，支持文件上传
- **URL Encoded**: 表单编码数据
- **Raw Text**: 原始文本数据

### 认证方式

#### Bearer Token 认证
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

#### Basic 认证
```
用户名: your-username
密码: your-password
```

#### 自定义头部认证
```
头部名称: X-API-Key
头部值: your-api-key-here
```

### cURL 导入

支持导入标准 cURL 命令，自动解析以下内容：
- URL 和查询参数
- HTTP 方法
- 请求头
- 请求体数据
- 认证信息

#### 示例 cURL 命令
```bash
curl -X POST 'https://api.example.com/users' \
  -H 'Content-Type: application/json' \
  -H 'Authorization: Bearer token123' \
  -d '{"name": "John Doe", "email": "<EMAIL>"}'
```

### 差异分析

#### 支持的差异类型
- **新增字段**: 新接口中新增的字段
- **删除字段**: 旧接口中被删除的字段
- **修改字段**: 值发生变化的字段
- **类型变化**: 数据类型发生变化的字段

#### 差异统计
- 总变化数量
- 各类型变化统计
- 变化百分比
- 影响评估

#### 忽略字段设置
可以配置忽略以下类型的字段：
```
timestamp
createdAt
updatedAt
traceId
requestId
version
```

### 配置管理

#### 保存配置
1. 配置完整的请求参数
2. 点击"保存配置"按钮
3. 输入配置名称
4. 确认保存

#### 加载配置
1. 点击"加载配置"按钮
2. 从列表中选择已保存的配置
3. 配置将自动填充到界面

#### 导出配置
1. 选择要导出的配置
2. 点击"导出配置"按钮
3. 下载 JSON 格式的配置文件

#### 导入配置
1. 点击"导入配置"按钮
2. 选择 JSON 格式的配置文件
3. 配置将自动加载到界面

## 💡 使用技巧

### 最佳实践

1. **接口版本对比**
   ```
   旧接口: https://api.example.com/v1/users
   新接口: https://api.example.com/v2/users
   ```

2. **环境差异对比**
   ```
   测试环境: https://test-api.example.com/users
   生产环境: https://api.example.com/users
   ```

3. **A/B 测试对比**
   ```
   版本A: https://api.example.com/users?version=a
   版本B: https://api.example.com/users?version=b
   ```

### 高效工作流

1. **使用 cURL 导入**
   - 从开发工具网络面板复制 cURL
   - 直接导入到工具中
   - 修改 URL 进行对比

2. **配置模板化**
   - 为常用接口创建配置模板
   - 保存不同环境的配置
   - 快速切换和复用

3. **批量测试**
   - 保存多个接口配置
   - 逐一加载进行测试
   - 记录差异结果

### 常见场景

#### API 升级验证
```
场景: 验证 API 升级后的兼容性
步骤:
1. 配置旧版本接口
2. 配置新版本接口
3. 使用相同的测试数据
4. 对比响应差异
5. 分析兼容性影响
```

#### 环境一致性检查
```
场景: 确保不同环境的接口行为一致
步骤:
1. 配置测试环境接口
2. 配置生产环境接口
3. 使用相同的请求参数
4. 对比响应结果
5. 识别环境差异
```

#### 接口回归测试
```
场景: 验证代码变更对接口的影响
步骤:
1. 记录变更前的接口响应
2. 部署代码变更
3. 请求变更后的接口
4. 对比前后差异
5. 确认变更影响
```

## ⚙️ 高级配置

### 请求超时设置
```
默认超时: 10000ms (10秒)
最小超时: 1000ms (1秒)
最大超时: 300000ms (5分钟)
```

### 忽略字段配置
支持多种忽略模式：
```
精确匹配: timestamp
路径匹配: user.timestamp
通配符匹配: *.timestamp
正则表达式: /^trace_/
```

### 差异显示选项
```
显示模式:
- 仅显示差异
- 显示全部内容
- 并排对比
- 统一差异视图
```

### 导出格式选项
```
支持格式:
- JSON 配置文件
- 差异报告 (HTML)
- 测试用例 (JSON)
- cURL 命令
```

## 🔧 故障排除

### 常见问题

#### 1. 请求失败
**问题**: 接口请求返回错误
**可能原因**:
- 网络连接问题
- 接口地址错误
- 认证信息无效
- CORS 限制

**解决方案**:
- 检查网络连接
- 验证接口地址
- 确认认证信息
- 使用支持 CORS 的接口

#### 2. 差异显示异常
**问题**: 差异分析结果不正确
**可能原因**:
- 响应格式不是 JSON
- 数据结构过于复杂
- 忽略字段配置错误

**解决方案**:
- 确认响应是 JSON 格式
- 简化数据结构
- 检查忽略字段设置

#### 3. 配置保存失败
**问题**: 无法保存或加载配置
**可能原因**:
- 浏览器存储限制
- 配置数据过大
- 权限不足

**解决方案**:
- 清理浏览器存储
- 简化配置数据
- 检查扩展权限

#### 4. 工具无法启动
**问题**: 点击工具后无响应
**可能原因**:
- 扩展未正确安装
- 浏览器版本不兼容
- 权限配置错误

**解决方案**:
- 重新安装扩展
- 更新浏览器版本
- 检查权限设置

### 调试技巧

1. **查看控制台日志**
   - 按 F12 打开开发者工具
   - 查看 Console 标签页
   - 寻找错误信息

2. **检查网络请求**
   - 在 Network 标签页查看请求
   - 确认请求参数和响应
   - 分析失败原因

3. **验证配置格式**
   - 检查 JSON 格式是否正确
   - 验证 URL 格式
   - 确认认证信息格式

## 📚 API 参考

### 配置文件格式

#### 请求配置 (RequestConfig)
```json
{
  "method": "POST",
  "oldUrl": "https://api-old.example.com/users",
  "newUrl": "https://api-new.example.com/users",
  "headers": {
    "Content-Type": "application/json",
    "Authorization": "Bearer token123"
  },
  "queryParams": {
    "page": "1",
    "limit": "10"
  },
  "body": {
    "type": "json",
    "content": "{\"name\": \"John Doe\"}"
  },
  "auth": {
    "type": "bearer",
    "credentials": {
      "token": "your-token-here"
    }
  },
  "timeout": 10000,
  "ignoreFields": ["timestamp", "traceId"]
}
```

#### 导出配置格式
```json
{
  "name": "用户接口对比",
  "description": "对比新旧用户接口的差异",
  "config": {
    // RequestConfig 对象
  },
  "exportedAt": "2024-12-01T10:00:00Z",
  "version": "1.0.0"
}
```

### 差异结果格式

#### 差异对象 (DiffResult)
```json
{
  "hasChanges": true,
  "changes": [
    {
      "path": "user.name",
      "type": "modified",
      "oldValue": "John",
      "newValue": "John Doe"
    },
    {
      "path": "user.email",
      "type": "added",
      "newValue": "<EMAIL>"
    }
  ],
  "summary": {
    "totalChanges": 2,
    "added": 1,
    "removed": 0,
    "modified": 1
  }
}
```

## 🔄 更新日志

### v1.0.0 (2024-12-01)
- ✨ 首次发布
- 🔄 双端点对比功能
- 📋 cURL 导入支持
- 🎨 可视化差异显示
- 💾 配置管理功能
- 🔐 多种认证方式
- 📱 响应式设计
- 🧪 完整测试覆盖

## 🤝 支持与反馈

### 获取帮助
- 📖 查看本文档
- 🐛 提交 Issue
- 💬 参与讨论
- 📧 联系开发者

### 贡献代码
- 🍴 Fork 项目
- 🔧 提交 Pull Request
- 📝 改进文档
- 🧪 添加测试

### 许可证
本项目采用 MIT 许可证，详见 LICENSE 文件。

---

**最后更新**: 2024-12-01  
**文档版本**: 1.0.0  
**工具版本**: 1.0.0
