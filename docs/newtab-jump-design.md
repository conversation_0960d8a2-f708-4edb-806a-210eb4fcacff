# New Tab Page 工具跳转设计方案

## 概述

本文档详细描述了如何实现指定工具从popup跳转到New Tab Page展示的完整方案，包括技术架构、实现步骤和关键代码设计。

## 核心目标

- 让指定工具能够在New Tab Page中展示和执行
- 提供灵活的工具展示模式配置
- 实现无缝的用户体验跳转
- 保持数据一致性和状态同步

## 技术架构

### 现有架构分析

**当前架构特点：**
- 基于 WXT 框架的浏览器扩展
- 三个入口点：background、content、popup
- 工具注册表管理系统 (`toolRegistry`)
- 模块化样式系统和组件架构

**关键触发点：**
- `entrypoints/popup/main.ts:435` - `ToolManager.executeTool()` 方法
- 工具执行流程：用户点击 → 检查模式 → 执行或跳转

### 新架构设计

**文件结构扩展：**
```
entrypoints/
├── newtab/
│   ├── index.html    # New Tab页面结构
│   ├── main.ts       # 主要逻辑
│   └── style.css     # 样式文件
├── popup/
│   ├── index.html
│   ├── main.ts       # 修改：增加跳转逻辑
│   └── style.css
├── background.ts
└── content.ts
```

## 详细实现方案

### 1. 工具配置扩展

**修改 `utils/tool-template.ts` 中的 Tool 接口：**

```typescript
export interface Tool {
  id: string;
  name: string;
  description: string;
  icon: string;
  categories: string[];
  enabled: boolean;
  badge?: 'new' | 'beta';
  position: number;
  version: ToolVersion;
  dependencies?: ToolDependency[];
  lifecycleState?: ToolLifecycleState;
  config?: ToolConfig;
  permissions?: string[];
  action: () => void | Promise<void>;
  
  // 新增字段
  displayMode: 'popup' | 'newtab';        // 显示模式
  newtabData?: any;                       // 传递给newtab的数据
  requiresFullscreen?: boolean;           // 是否需要全屏环境
  onNewTabInit?: () => void | Promise<void>; // newtab环境初始化钩子
  onNewTabDestroy?: () => void | Promise<void>; // newtab环境销毁钩子
}
```

**修改 `BaseTool` 类：**

```typescript
export abstract class BaseTool implements Tool {
  // 现有字段...
  
  // 新增字段默认值
  displayMode: 'popup' | 'newtab' = 'popup';
  newtabData?: any;
  requiresFullscreen: boolean = false;
  
  // 新增生命周期钩子
  async onNewTabInit(): Promise<void> {
    // newtab环境初始化逻辑
  }
  
  async onNewTabDestroy(): Promise<void> {
    // newtab环境销毁逻辑
  }
}
```

### 2. 跳转触发机制

**修改 `entrypoints/popup/main.ts` 中的 `ToolManager.executeTool()` 方法：**

```typescript
private async executeTool(tool: Tool) {
  try {
    console.log(`🔍 获取工具实例: ${tool.id}`);
    const toolInstance = toolRegistry.getById(tool.id);
    
    if (!toolInstance) {
      throw new Error(`工具 ${tool.name} 未找到`);
    }
    
    // 检查是否需要跳转到newtab
    if (toolInstance.displayMode === 'newtab') {
      console.log(`🚀 工具 ${tool.name} 需要在New Tab中执行，开始跳转...`);
      await this.openToolInNewTab(toolInstance);
      return;
    }
    
    // 原有popup执行逻辑
    if (typeof toolInstance.action === 'function') {
      console.log(`🎬 在popup中执行工具: ${toolInstance.name}`);
      await toolInstance.action();
      console.log(`✅ 工具 ${tool.name} 执行完成`);
    } else {
      throw new Error(`工具 ${tool.name} 的 action 方法不存在`);
    }
    
  } catch (error) {
    console.error(`❌ 执行工具 ${tool.name} 时出错:`, error);
    const errorMessage = error instanceof Error ? error.message : '未知错误';
    notificationManager.error(`执行工具失败: ${errorMessage}`);
  }
}

private async openToolInNewTab(tool: Tool) {
  try {
    // 1. 准备传递数据
    const launchData: NewTabLaunchData = {
      toolId: tool.id,
      timestamp: Date.now(),
      data: tool.newtabData || {},
      source: 'popup'
    };
    
    console.log('📦 准备New Tab启动数据:', launchData);
    
    // 2. 存储到Chrome Storage
    await browser.storage.local.set({
      'newtab-tool-launch': launchData
    });
    
    // 3. 显示跳转提示
    notificationManager.info(`正在新标签页中打开 ${tool.name}...`);
    
    // 4. 打开newtab页面
    const newTabUrl = browser.runtime.getURL('newtab.html');
    console.log('🌐 打开New Tab页面:', newTabUrl);
    
    await browser.tabs.create({ 
      url: newTabUrl,
      active: true
    });
    
    // 5. 延迟关闭popup，给用户反馈时间
    setTimeout(() => {
      window.close();
    }, 500);
    
  } catch (error) {
    console.error('❌ 打开New Tab失败:', error);
    notificationManager.error('打开新标签页失败，请重试');
    
    // 回退到popup执行
    if (typeof tool.action === 'function') {
      console.log('🔄 回退到popup执行...');
      await tool.action();
    }
  }
}
```

**添加启动数据接口定义：**

```typescript
interface NewTabLaunchData {
  toolId: string;                    // 目标工具ID
  timestamp: number;                // 启动时间戳
  data?: any;                       // 工具特定数据
  source?: 'popup' | 'direct';      // 来源标识
  sessionId?: string;              // 会话ID（用于追踪）
}
```

### 3. New Tab Page实现

**创建 `entrypoints/newtab/index.html`：**

```html
<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>服务运营工具集合</title>
    <meta name="manifest.type" content="chrome_url_overrides" />
  </head>
  <body>
    <div id="app">
      <!-- 加载状态 -->
      <div id="loading" class="loading-screen">
        <div class="loading-content">
          <div class="loading-spinner">🛠️</div>
          <div class="loading-text">正在启动工具...</div>
        </div>
      </div>
      
      <!-- 工具容器 -->
      <div id="tool-container" class="tool-container" style="display: none;">
        <!-- 工具内容将在这里动态加载 -->
      </div>
      
      <!-- 工具选择界面 -->
      <div id="tool-selector" class="tool-selector" style="display: none;">
        <header class="selector-header">
          <h1>🛠️ 选择工具</h1>
          <div class="search-container">
            <input type="text" id="selector-search" placeholder="搜索工具..." class="search-input">
            <span class="search-icon">🔍</span>
          </div>
        </header>
        
        <main class="selector-tools" id="selector-tools">
          <!-- 工具列表将在这里动态生成 -->
        </main>
      </div>
    </div>
    <script type="module" src="./main.ts"></script>
  </body>
</html>
```

**创建 `entrypoints/newtab/main.ts`：**

```typescript
import { toolRegistry } from '../../utils/tool-registry';
import { settingsManager } from '../../utils/settings-manager';
import { categoryManager } from '../../utils/category-manager';
import { notificationManager } from '../../utils/notification-manager';
import { styleManager } from '../../utils/style-manager';
import type { Tool, NewTabLaunchData } from '../../utils/tool-template';

class NewTabManager {
  private currentTool: Tool | null = null;
  private launchData: NewTabLaunchData | null = null;
  
  constructor() {
    this.init();
  }
  
  private async init() {
    try {
      console.log('🚀 New Tab Page初始化开始...');
      
      // 显示加载状态
      this.showLoading();
      
      // 初始化管理器
      await settingsManager.init();
      await toolRegistry.init();
      await categoryManager.init();
      
      // 加载样式
      await this.loadStyles();
      
      // 检查是否有启动数据
      await this.checkLaunchData();
      
      console.log('✅ New Tab Page初始化完成');
      
    } catch (error) {
      console.error('❌ New Tab Page初始化失败:', error);
      this.showError('初始化失败: ' + (error as Error).message);
    }
  }
  
  private async loadStyles() {
    try {
      // 注册和加载样式模块
      styleManager.registerModule({
        name: 'newtab',
        path: '/entrypoints/newtab/style.css',
        dependencies: ['design-tokens', 'components']
      });
      
      await styleManager.loadModule('newtab');
      console.log('✅ New Tab样式加载成功');
    } catch (error) {
      console.warn('❌ New Tab样式加载失败:', error);
    }
  }
  
  private async checkLaunchData() {
    try {
      const result = await browser.storage.local.get('newtab-tool-launch');
      const launchData = result['newtab-tool-launch'] as NewTabLaunchData;
      
      if (launchData) {
        console.log('📦 收到工具启动数据:', launchData);
        this.launchData = launchData;
        
        // 清理启动数据
        await browser.storage.local.remove('newtab-tool-launch');
        
        // 启动目标工具
        await this.launchTool(launchData.toolId, launchData.data);
      } else {
        console.log('📋 没有启动数据，显示工具选择界面');
        this.showToolSelector();
      }
    } catch (error) {
      console.error('❌ 检查启动数据失败:', error);
      this.showToolSelector();
    }
  }
  
  private async launchTool(toolId: string, data?: any) {
    try {
      console.log(`🎯 启动工具: ${toolId}`);
      
      const tool = toolRegistry.getById(toolId);
      if (!tool) {
        throw new Error(`工具 ${toolId} 不存在`);
      }
      
      this.currentTool = tool;
      
      // 检查工具是否支持newtab模式
      if (tool.displayMode !== 'newtab') {
        console.warn(`⚠️ 工具 ${toolId} 不支持newtab模式，但仍然尝试执行`);
      }
      
      // 调用newtab初始化钩子
      if (tool.onNewTabInit) {
        console.log(`🔧 执行工具 ${toolId} 的newtab初始化...`);
        await tool.onNewTabInit();
      }
      
      // 执行工具主逻辑
      if (typeof tool.action === 'function') {
        console.log(`🎬 执行工具 ${toolId} 的主逻辑...`);
        await tool.action();
        console.log(`✅ 工具 ${toolId} 执行完成`);
      } else {
        throw new Error(`工具 ${toolId} 的 action 方法不存在`);
      }
      
      // 隐藏加载状态，显示工具容器
      this.hideLoading();
      this.showToolContainer();
      
    } catch (error) {
      console.error(`❌ 启动工具 ${toolId} 失败:`, error);
      this.showError('工具启动失败: ' + (error as Error).message);
      this.showToolSelector();
    }
  }
  
  private showToolSelector() {
    console.log('📋 显示工具选择界面');
    
    // 隐藏加载状态和工具容器
    document.getElementById('loading')!.style.display = 'none';
    document.getElementById('tool-container')!.style.display = 'none';
    
    // 显示工具选择器
    const selector = document.getElementById('tool-selector')!;
    selector.style.display = 'block';
    
    // 渲染工具列表
    this.renderToolSelector();
  }
  
  private renderToolSelector() {
    const container = document.getElementById('selector-tools')!;
    const tools = toolRegistry.getEnabled();
    
    container.innerHTML = tools.map(tool => `
      <div class="tool-card" data-tool-id="${tool.id}">
        <span class="tool-icon">${tool.icon}</span>
        <div class="tool-info">
          <div class="tool-name">${tool.name}</div>
          <div class="tool-description">${tool.description}</div>
        </div>
        <div class="tool-actions">
          ${tool.displayMode === 'newtab' ? '<span class="badge newtab-badge">NewTab</span>' : ''}
          <button class="launch-btn" data-tool-id="${tool.id}">启动</button>
        </div>
      </div>
    `).join('');
    
    // 添加点击事件
    container.querySelectorAll('.launch-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        const toolId = (e.target as HTMLElement).dataset.toolId;
        if (toolId) {
          this.launchTool(toolId);
        }
      });
    });
    
    // 搜索功能
    const searchInput = document.getElementById('selector-search') as HTMLInputElement;
    searchInput.addEventListener('input', (e) => {
      const query = (e.target as HTMLInputElement).value.toLowerCase();
      this.filterTools(query);
    });
  }
  
  private filterTools(query: string) {
    const cards = document.querySelectorAll('#selector-tools .tool-card');
    cards.forEach(card => {
      const toolName = card.querySelector('.tool-name')!.textContent.toLowerCase();
      const toolDesc = card.querySelector('.tool-description')!.textContent.toLowerCase();
      const matches = toolName.includes(query) || toolDesc.includes(query);
      (card as HTMLElement).style.display = matches ? 'flex' : 'none';
    });
  }
  
  private showLoading() {
    document.getElementById('loading')!.style.display = 'flex';
    document.getElementById('tool-container')!.style.display = 'none';
    document.getElementById('tool-selector')!.style.display = 'none';
  }
  
  private hideLoading() {
    document.getElementById('loading')!.style.display = 'none';
  }
  
  private showToolContainer() {
    document.getElementById('tool-container')!.style.display = 'block';
  }
  
  private showError(message: string) {
    const container = document.getElementById('tool-container')!;
    container.innerHTML = `
      <div class="error-message">
        <div class="error-icon">❌</div>
        <div class="error-text">${message}</div>
        <button class="retry-btn" onclick="location.reload()">重试</button>
        <button class="back-btn" onclick="this.showToolSelector()">返回工具选择</button>
      </div>
    `;
  }
}

// 启动New Tab管理器
new NewTabManager();
```

**创建 `entrypoints/newtab/style.css`：**

```css
/* New Tab Page样式 */
:root {
  --primary-color: #4f46e5;
  --secondary-color: #6366f1;
  --background-color: #f8fafc;
  --surface-color: #ffffff;
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --border-color: #e2e8f0;
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background-color: var(--background-color);
  color: var(--text-primary);
  line-height: 1.6;
  min-height: 100vh;
}

#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 加载状态 */
.loading-screen {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background-color: var(--background-color);
}

.loading-content {
  text-align: center;
}

.loading-spinner {
  font-size: 4rem;
  animation: spin 1s linear infinite;
}

.loading-text {
  margin-top: 1rem;
  font-size: 1.125rem;
  color: var(--text-secondary);
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 工具容器 */
.tool-container {
  flex: 1;
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

/* 工具选择界面 */
.tool-selector {
  flex: 1;
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

.selector-header {
  text-align: center;
  margin-bottom: 3rem;
}

.selector-header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.search-container {
  position: relative;
  max-width: 400px;
  margin: 0 auto;
}

.search-input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 3rem;
  border: 2px solid var(--border-color);
  border-radius: 0.75rem;
  font-size: 1rem;
  transition: all 0.2s;
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.search-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
}

/* 工具卡片 */
.selector-tools {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 1.5rem;
}

.tool-card {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: 1rem;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all 0.2s;
  cursor: pointer;
}

.tool-card:hover {
  border-color: var(--primary-color);
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.tool-icon {
  font-size: 2.5rem;
  flex-shrink: 0;
}

.tool-info {
  flex: 1;
  min-width: 0;
}

.tool-name {
  font-weight: 600;
  font-size: 1.125rem;
  margin-bottom: 0.25rem;
  color: var(--text-primary);
}

.tool-description {
  font-size: 0.875rem;
  color: var(--text-secondary);
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.tool-actions {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.5rem;
}

.newtab-badge {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 500;
}

.launch-btn {
  background: var(--primary-color);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.launch-btn:hover {
  background: var(--secondary-color);
  transform: translateY(-1px);
}

/* 错误消息 */
.error-message {
  text-align: center;
  padding: 3rem;
}

.error-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.error-text {
  font-size: 1.125rem;
  color: var(--text-secondary);
  margin-bottom: 2rem;
}

.retry-btn, .back-btn {
  background: var(--primary-color);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  margin: 0 0.5rem;
  transition: all 0.2s;
}

.retry-btn:hover, .back-btn:hover {
  background: var(--secondary-color);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .selector-tools {
    grid-template-columns: 1fr;
  }
  
  .selector-header h1 {
    font-size: 2rem;
  }
  
  .tool-selector {
    padding: 1rem;
  }
  
  .tool-container {
    padding: 1rem;
  }
}

/* 工具特定样式占位符 */
/* 工具可以在这个文件中添加自己的样式 */
```

### 4. 配置文件修改

**修改 `wxt.config.ts`：**

```typescript
import { defineConfig } from 'wxt';

export default defineConfig({
  manifest: {
    name: '服务运营工具集合',
    description: '一个功能丰富的浏览器扩展工具集合，提供各种实用的开发和日常工具',
    version: '1.0.0',
    permissions: [
      'activeTab',
      'storage',
      'tabs',
      'scripting',
      'notifications',
      'downloads',
      'clipboardWrite',
      'cookies',
      'webRequest',
      'clipboardRead'
    ],
    host_permissions: [
      '<all_urls>',
      'https://falcon.op.zuoyebang.cc/*',
      'https://log-search-docker.zuoyebang.cc/*'
    ],
    action: {
      default_title: '服务运营工具集合',
      default_popup: 'popup.html'
    },
    // 新增：New Tab Page覆盖
    chrome_url_overrides: {
      newtab: 'newtab.html'
    },
    icons: {
      16: '/icon/16.png',
      32: '/icon/32.png',
      48: '/icon/48.png',
      96: '/icon/96.png',
      128: '/icon/128.png'
    },
    web_accessible_resources: [
      {
        resources: ['config/*.json', 'styles/*.css', 'entrypoints/**/*.css'],
        matches: ['<all_urls>']
      }
    ]
  }
});
```

### 5. 工具适配示例

**创建一个支持New Tab的工具示例：**

```typescript
// tools/my-newtab-tool.ts
import { BaseTool } from '../utils/tool-template';

export class MyNewTabTool extends BaseTool {
  id = 'my-newtab-tool';
  name = '全屏工具示例';
  description = '一个需要在全屏环境中运行的复杂工具';
  icon = '🖥️';
  categories = ['all', 'development'];
  displayMode = 'newtab';  // 指定在新标签页中执行
  requiresFullscreen = true;
  
  // 传递给newtab的数据
  newtabData = {
    initialConfig: {
      theme: 'dark',
      layout: 'advanced'
    },
    permissions: ['clipboard', 'storage']
  };

  async action(): Promise<void> {
    try {
      console.log('🎬 NewTab工具开始执行');
      
      // 检查是否在newtab环境中
      if (!this.isInNewTabEnvironment()) {
        throw new Error('此工具必须在New Tab环境中运行');
      }
      
      // 创建全屏界面
      await this.createFullscreenInterface();
      
      // 初始化工具功能
      await this.initializeFeatures();
      
      console.log('✅ NewTab工具执行完成');
      
    } catch (error) {
      console.error('❌ NewTab工具执行失败:', error);
      throw error;
    }
  }
  
  async onNewTabInit(): Promise<void> {
    console.log('🔧 NewTab环境初始化');
    
    // 初始化全屏特定的资源
    await this.loadFullscreenResources();
    
    // 设置事件监听器
    this.setupFullscreenEventListeners();
  }
  
  async onNewTabDestroy(): Promise<void> {
    console.log('🧹 NewTab环境清理');
    
    // 清理资源
    await this.cleanupResources();
    
    // 移除事件监听器
    this.removeEventListeners();
  }
  
  private isInNewTabEnvironment(): boolean {
    // 检查当前是否在newtab页面中
    return window.location.pathname.includes('newtab.html');
  }
  
  private async createFullscreenInterface(): Promise<void> {
    const container = document.getElementById('tool-container');
    if (!container) return;
    
    container.innerHTML = `
      <div class="fullscreen-tool">
        <header class="tool-header">
          <h1>${this.icon} ${this.name}</h1>
          <div class="tool-controls">
            <button id="minimize-btn">最小化</button>
            <button id="close-btn">关闭</button>
          </div>
        </header>
        
        <main class="tool-main">
          <div class="workspace">
            <!-- 工具主要功能区域 -->
            <div class="tool-content">
              <h2>全屏工作区</h2>
              <p>这是一个全屏工具的示例界面</p>
              
              <div class="tool-features">
                <div class="feature-card">
                  <h3>🎯 功能一</h3>
                  <p>复杂的数据分析和可视化</p>
                </div>
                
                <div class="feature-card">
                  <h3>📊 功能二</h3>
                  <p>实时数据处理和图表展示</p>
                </div>
                
                <div class="feature-card">
                  <h3>⚡ 功能三</h3>
                  <p>高性能计算和批量操作</p>
                </div>
              </div>
            </div>
          </div>
        </main>
        
        <footer class="tool-footer">
          <div class="status-bar">
            <span class="status-indicator ready">就绪</span>
            <span class="memory-usage">内存: 45MB</span>
            <span class="performance">性能: 优秀</span>
          </div>
        </footer>
      </div>
    `;
    
    // 添加事件监听器
    this.setupToolControls();
  }
  
  private setupToolControls(): void {
    const minimizeBtn = document.getElementById('minimize-btn');
    const closeBtn = document.getElementById('close-btn');
    
    minimizeBtn?.addEventListener('click', () => {
      this.minimizeTool();
    });
    
    closeBtn?.addEventListener('click', () => {
      this.closeTool();
    });
  }
  
  private minimizeTool(): void {
    // 返回工具选择界面
    this.showToolSelector();
  }
  
  private closeTool(): void {
    // 关闭当前标签页
    window.close();
  }
  
  private showToolSelector(): void {
    // 触发显示工具选择界面
    document.dispatchEvent(new CustomEvent('show-tool-selector'));
  }
  
  private async initializeFeatures(): Promise<void> {
    // 初始化工具的各种功能
    console.log('🔧 初始化工具功能...');
    
    // 模拟功能初始化
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    console.log('✅ 工具功能初始化完成');
  }
  
  private async loadFullscreenResources(): Promise<void> {
    // 加载全屏特定的资源
    console.log('📦 加载全屏资源...');
  }
  
  private setupFullscreenEventListeners(): void {
    // 设置全屏特定的事件监听器
    console.log('🎧 设置全屏事件监听器...');
  }
  
  private async cleanupResources(): Promise<void> {
    // 清理资源
    console.log('🧹 清理资源...');
  }
  
  private removeEventListeners(): void {
    // 移除事件监听器
    console.log('🔇 移除事件监听器...');
  }
}
```

### 6. 注册New Tab工具

**修改 `entrypoints/popup/main.ts` 中的工具注册：**

```typescript
// 在 initApp() 函数中添加新工具
async function initApp() {
  try {
    // 初始化管理器
    await settingsManager.init();
    await toolRegistry.init();
    await categoryManager.init();
    
    // 注册现有工具
    await registerToolWithSettings(new XuidTool());
    await registerToolWithSettings(new AlertParserTool());
    await registerToolWithSettings(new TaskListTool());
    await registerToolWithSettings(new ApiMigrationValidatorTool());
    
    // 注册New Tab工具
    await registerToolWithSettings(new MyNewTabTool());
    
    console.log('✅ 所有工具注册完成');
    
    // 创建工具管理器
    new ToolManager();
    
  } catch (error) {
    console.error('应用初始化失败:', error);
  }
}
```

## 实施步骤

### 阶段1：基础架构搭建
1. 创建 `entrypoints/newtab/` 目录和文件
2. 修改 `wxt.config.ts` 添加 `chrome_url_overrides` 配置
3. 扩展 `Tool` 接口和 `BaseTool` 类

### 阶段2：跳转机制实现
1. 修改 `ToolManager.executeTool()` 添加跳转逻辑
2. 实现数据传递和存储机制
3. 创建 New Tab 页面基础框架

### 阶段3：New Tab功能开发
1. 完成 New Tab 页面的主要功能
2. 实现工具选择界面
3. 添加样式和响应式设计

### 阶段4：工具适配和测试
1. 创建示例 New Tab 工具
2. 测试跳转和执行流程
3. 优化用户体验和错误处理

### 阶段5：完善和优化
1. 性能优化
2. 错误处理完善
3. 用户反馈和调试功能

## 注意事项

1. **权限要求**：确保 `chrome_url_overrides` 权限正确配置
2. **数据清理**：及时清理 Chrome Storage 中的启动数据
3. **错误处理**：提供完善的错误处理和回退机制
4. **性能考虑**：New Tab 页面加载性能优化
5. **用户体验**：提供清晰的加载状态和错误反馈

## 测试验证

1. **基础功能测试**：
   - popup中正常工具执行
   - newtab工具跳转
   - 数据传递准确性

2. **边界情况测试**：
   - 跳转失败回退
   - 重复启动处理
   - 网络异常处理

3. **用户体验测试**：
   - 加载速度
   - 界面响应
   - 错误提示友好性

这个方案提供了完整的 New Tab Page 跳转实现，涵盖了从架构设计到具体代码实现的所有方面。