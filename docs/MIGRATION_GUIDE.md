# 工具迁移指南

本指南将帮助您将现有浏览器扩展的功能迁移到服务运营工具集合中。

## 📋 迁移前准备

### 1. 分析现有扩展
- 确定扩展的核心功能
- 分析所需的权限
- 了解数据存储需求
- 检查是否有外部API依赖

### 2. 选择工具分类
- **productivity**: 效率提升工具（如文本处理、时间管理）
- **development**: 开发辅助工具（如代码格式化、API测试）
- **utility**: 实用工具（如截图、密码生成）

## 🛠️ 迁移步骤

### 步骤1: 创建工具类

使用提供的工具模板创建新工具：

```typescript
import { BaseTool, TOOL_CATEGORIES, TOOL_ICONS } from '../utils/tool-template';

export class MyMigratedTool extends BaseTool {
  id = 'my-migrated-tool';
  name = '我的迁移工具';
  description = '从其他扩展迁移的功能';
  icon = TOOL_ICONS.TOOL;
  category = TOOL_CATEGORIES.UTILITY;
  badge = 'new'; // 可选：标记为新工具

  async action(): Promise<void> {
    // 实现工具的核心功能
    try {
      // 1. 获取当前标签页（如果需要）
      const tab = await this.getCurrentTab();
      
      // 2. 执行内容脚本（如果需要）
      await this.executeScript(() => {
        // 在页面中执行的代码
        console.log('在页面中执行');
      });
      
      // 3. 显示通知
      await this.showNotification('成功', '工具执行完成');
      
    } catch (error) {
      console.error('工具执行失败:', error);
      await this.showNotification('错误', '工具执行失败');
    }
  }
}
```

### 步骤2: 注册工具

在 `entrypoints/popup/main.ts` 中注册新工具：

```typescript
import { MyMigratedTool } from '../../tools/my-migrated-tool';

// 在 initApp 函数中添加
const myTool = new MyMigratedTool();
await toolRegistry.register(myTool);
```

### 步骤3: 配置权限

在 `wxt.config.ts` 中添加所需权限：

```typescript
export default defineConfig({
  manifest: {
    permissions: [
      'activeTab',      // 访问当前标签页
      'storage',        // 本地存储
      'tabs',           // 标签页管理
      'scripting',      // 脚本注入
      'notifications',  // 通知
      'downloads',      // 下载文件
      'clipboardWrite', // 剪贴板写入
      // 根据需要添加其他权限
    ],
    host_permissions: [
      '<all_urls>'      // 如果需要访问所有网站
    ]
  }
});
```

## 🔧 常见迁移场景

### 1. 内容脚本功能

如果原扩展使用内容脚本操作页面：

```typescript
async action(): Promise<void> {
  await this.executeScript(() => {
    // 原内容脚本的代码
    const elements = document.querySelectorAll('.target-element');
    elements.forEach(el => {
      // 处理元素
    });
  });
}
```

### 2. 弹出窗口功能

如果原扩展有复杂的弹出窗口：

```typescript
async action(): Promise<void> {
  // 创建模态框
  const modal = this.createModal();
  document.body.appendChild(modal);
}

private createModal(): HTMLElement {
  const modal = document.createElement('div');
  modal.className = 'my-tool-modal';
  modal.innerHTML = `
    <div class="modal-content">
      <!-- 原弹出窗口的HTML结构 -->
    </div>
  `;
  
  // 添加样式和事件监听
  this.addModalStyles();
  this.bindModalEvents(modal);
  
  return modal;
}
```

### 3. 数据存储

使用Chrome存储API保存数据：

```typescript
// 保存数据
async saveData(key: string, data: any): Promise<void> {
  await browser.storage.local.set({ [key]: data });
}

// 读取数据
async loadData(key: string): Promise<any> {
  const result = await browser.storage.local.get(key);
  return result[key];
}
```

### 4. 网络请求

如果需要发送HTTP请求：

```typescript
async fetchData(url: string): Promise<any> {
  try {
    const response = await fetch(url);
    return await response.json();
  } catch (error) {
    console.error('请求失败:', error);
    throw error;
  }
}
```

### 5. 文件下载

下载文件功能：

```typescript
async downloadFile(content: string, filename: string): Promise<void> {
  const blob = new Blob([content], { type: 'text/plain' });
  const url = URL.createObjectURL(blob);
  
  await chrome.downloads.download({
    url: url,
    filename: filename,
    saveAs: true
  });
  
  URL.revokeObjectURL(url);
}
```

## 📝 迁移检查清单

### 功能迁移
- [ ] 核心功能已实现
- [ ] 用户界面已适配
- [ ] 错误处理已添加
- [ ] 数据存储已迁移

### 权限配置
- [ ] 必要权限已添加到manifest
- [ ] 主机权限已正确配置
- [ ] 权限使用已最小化

### 测试验证
- [ ] 基本功能正常工作
- [ ] 错误情况处理正确
- [ ] 性能表现良好
- [ ] 用户体验流畅

### 文档更新
- [ ] 工具描述准确
- [ ] 使用说明清晰
- [ ] 已知问题已记录

## 🚨 常见问题

### Q: 如何处理原扩展的复杂UI？
A: 可以创建模态框来承载复杂界面，参考密码生成器和JSON格式化工具的实现。

## 🎨 UI迁移详细指南

### 主题适配要求

**⚠️ 重要：所有UI组件必须支持深色和浅色主题**

#### 1. 使用设计令牌系统
```css
/* ❌ 错误：硬编码颜色 */
.my-component {
  background-color: #f8f9fa;
  color: #212529;
  border: 1px solid #dee2e6;
}

/* ✅ 正确：使用设计令牌 */
.my-component {
  background-color: var(--surface);
  color: var(--text-primary);
  border: 1px solid var(--border);
}
```

#### 2. 常用设计令牌
```css
/* 背景颜色 */
--background        /* 主背景 */
--surface          /* 卡片/面板背景 */
--surface-hover    /* 悬停状态背景 */

/* 文本颜色 */
--text-primary     /* 主要文本 */
--text-secondary   /* 次要文本 */
--text-tertiary    /* 辅助文本 */
--text-disabled    /* 禁用文本 */

/* 边框颜色 */
--border           /* 默认边框 */
--border-hover     /* 悬停边框 */
--border-focus     /* 焦点边框 */

/* 状态颜色 */
--primary          /* 主色调 */
--success          /* 成功状态 */
--warning          /* 警告状态 */
--error            /* 错误状态 */
--info             /* 信息状态 */

/* 间距系统 */
--spacing-1 到 --spacing-24  /* 4px 到 96px */

/* 字体系统 */
--font-family-sans /* 无衬线字体 */
--font-family-mono /* 等宽字体 */
--font-size-xs 到 --font-size-4xl
--font-weight-light 到 --font-weight-bold
```

#### 3. 深色主题特殊处理
```css
/* 需要在深色主题下特殊处理的元素 */
.special-element {
  background-color: var(--color-info-50);
  border-color: var(--info);
}

/* 深色主题下的覆盖样式 */
[data-theme="dark"] .special-element {
  background-color: var(--color-info-900);
  border-color: var(--info);
}
```

#### 4. 焦点状态处理
```css
/* 使用半透明主色调作为焦点阴影 */
.input-element:focus {
  outline: none;
  border-color: var(--border-focus);
  box-shadow: 0 0 0 2px color-mix(in srgb, var(--primary) 25%, transparent);
}
```

#### 5. 按钮颜色规范
```css
/* 使用语义化颜色而非硬编码白色 */
.btn-primary {
  background-color: var(--primary);
  color: var(--color-gray-50);  /* 而非 white */
}
```

### UI组件迁移检查清单

#### 输入组件
- [ ] 背景色使用 `var(--background)`
- [ ] 文字颜色使用 `var(--text-primary)`
- [ ] 边框颜色使用 `var(--border)`
- [ ] 占位符颜色使用 `var(--text-tertiary)`
- [ ] 焦点状态使用设计令牌
- [ ] 深色主题下测试通过

#### 按钮组件
- [ ] 背景色使用语义化变量
- [ ] 文字颜色使用 `var(--color-gray-50)` 而非 `white`
- [ ] 悬停状态使用对应的 `-hover` 变量
- [ ] 禁用状态正确处理

#### 卡片/面板组件
- [ ] 背景使用 `var(--surface)`
- [ ] 边框使用 `var(--border)`
- [ ] 阴影使用设计令牌中的阴影变量
- [ ] 内容文字使用正确的文本颜色变量

#### 状态指示组件
- [ ] 成功状态使用 `var(--color-success-*)` 系列
- [ ] 错误状态使用 `var(--color-error-*)` 系列
- [ ] 警告状态使用 `var(--color-warning-*)` 系列
- [ ] 信息状态使用 `var(--color-info-*)` 系列
- [ ] 深色主题下使用对应的深色变体

### 测试要求

#### 主题切换测试
1. **浅色主题测试**
   - 所有文字清晰可读
   - 对比度符合无障碍标准
   - 视觉层次分明

2. **深色主题测试**
   - 所有文字在深色背景下清晰可读
   - 没有白色闪烁或不协调的颜色
   - 整体视觉效果和谐

3. **自动切换测试**
   - 跟随系统主题自动切换
   - 切换过程平滑无闪烁
   - 状态保持正确

#### 响应式测试
- [ ] 不同窗口尺寸下正常显示
- [ ] 移动设备友好（如果适用）
- [ ] 文字不会被截断或重叠

### 常见问题解决

#### 问题1：深色主题下文字不可见
**原因**：使用了硬编码的浅色背景和深色文字
**解决**：使用 `var(--background)` 和 `var(--text-primary)`

#### 问题2：特殊状态颜色在深色主题下不协调
**原因**：只使用了浅色主题的颜色变体
**解决**：添加深色主题的特殊处理

#### 问题3：焦点状态在深色主题下不明显
**原因**：使用了固定的rgba值
**解决**：使用 `color-mix()` 函数基于主色调生成半透明颜色

## 🔍 硬编码问题全面检查

### 硬编码检查清单

#### 颜色相关
- [ ] 无十六进制颜色值 (`#ffffff`, `#000000` 等)
- [ ] 无RGB/RGBA颜色值 (`rgb(255,255,255)`, `rgba(0,0,0,0.5)` 等)
- [ ] 无颜色名称 (`white`, `black`, `red` 等)
- [ ] 焦点阴影使用 `color-mix()` 函数

#### 尺寸和间距
- [ ] 无硬编码像素值 (`10px`, `20px` 等)
- [ ] 边框宽度使用 `var(--input-border-width)`
- [ ] 间距使用 `var(--spacing-*)` 系列
- [ ] 字体大小使用 `var(--font-size-*)` 系列
- [ ] 圆角使用 `var(--radius-*)` 系列

#### 字体相关
- [ ] 字体族使用 `var(--font-family-sans)` 或 `var(--font-family-mono)`
- [ ] 字体粗细使用 `var(--font-weight-*)` 系列
- [ ] 行高使用 `var(--line-height-*)` 系列

#### 动画和过渡
- [ ] 过渡时间使用 `var(--duration-*)` 系列
- [ ] 缓动函数使用 `var(--ease-*)` 系列
- [ ] 变换值使用计算值而非硬编码

#### 响应式设计
- [ ] 断点使用rem单位而非px
- [ ] 网格列宽使用rem单位
- [ ] 最大/最小宽度使用相对单位

### 常见硬编码模式及修复

#### 1. 边框宽度
```css
/* ❌ 错误 */
border: 1px solid var(--border);

/* ✅ 正确 */
border: var(--input-border-width) solid var(--border);
```

#### 2. 变换效果
```css
/* ❌ 错误 */
transform: translateY(-1px);

/* ✅ 正确 */
transform: translateY(calc(-1 * var(--spacing-1) / 4));
```

#### 3. 焦点阴影
```css
/* ❌ 错误 */
box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.25);

/* ✅ 正确 */
box-shadow: 0 0 0 2px color-mix(in srgb, var(--primary) 25%, transparent);
```

#### 4. 网格布局
```css
/* ❌ 错误 */
grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));

/* ✅ 正确 */
grid-template-columns: repeat(auto-fit, minmax(12.5rem, 1fr));
```

#### 5. 响应式断点
```css
/* ❌ 错误 */
@media (max-width: 600px) { }

/* ✅ 正确 */
@media (max-width: 37.5rem) { }
```

#### 6. 最大宽度
```css
/* ❌ 错误 */
max-width: 800px;

/* ✅ 正确 */
max-width: 50rem;
```

#### 7. 最小高度
```css
/* ❌ 错误 */
min-height: 120px;

/* ✅ 正确 */
min-height: calc(var(--spacing-24) * 1.25);
```

### 自动化检查工具

可以使用以下正则表达式检查硬编码问题：

```bash
# 检查颜色硬编码
grep -r "#[0-9a-fA-F]\{3,6\}\|rgb\(\|rgba\(" styles/ tools/

# 检查尺寸硬编码
grep -r "\d\+px\|margin:\s*\d\+\|padding:\s*\d\+" styles/ tools/

# 检查边框硬编码
grep -r "border:\s*1px\|border-width:\s*1px" styles/ tools/
```

### 验证标准

#### 完全符合设计系统
- ✅ 所有颜色值来自设计令牌
- ✅ 所有尺寸值使用相对单位或设计令牌
- ✅ 所有动画参数使用设计令牌
- ✅ 响应式设计使用统一断点

#### 主题兼容性
- ✅ 深色主题下所有元素正常显示
- ✅ 浅色主题下保持良好效果
- ✅ 主题切换无闪烁或错位

#### 可维护性
- ✅ 样式修改只需更改设计令牌
- ✅ 新增主题只需定义令牌变量
- ✅ 代码结构清晰易懂

### Q: 如何迁移背景脚本功能？
A: 将背景脚本的逻辑移到工具的action方法中，或者在background.ts中实现共享功能。

### Q: 如何处理跨域请求？
A: 在manifest中添加相应的host_permissions，或使用chrome.runtime.sendMessage与background脚本通信。

### Q: 如何保持原有的数据？
A: 可以创建数据迁移脚本，将原扩展的数据导入到新的存储结构中。

### Q: 如何处理原扩展的设置？
A: 使用settingsManager来管理工具特定的设置，或在工具内部实现设置界面。

## 💡 最佳实践

1. **渐进式迁移**: 先迁移核心功能，再逐步添加高级特性
2. **保持简洁**: 每个工具专注于单一功能
3. **用户友好**: 提供清晰的错误信息和操作反馈
4. **性能优化**: 避免阻塞UI，使用异步操作
5. **测试充分**: 在不同浏览器和场景下测试功能

## 📚 参考资源

- [WXT框架文档](https://wxt.dev/)
- [Chrome扩展API文档](https://developer.chrome.com/docs/extensions/)
- [工具模板文件](../utils/tool-template.ts)
- [示例工具实现](../tools/)

## 🤝 获取帮助

如果在迁移过程中遇到问题：

1. 查看现有工具的实现作为参考
2. 检查浏览器控制台的错误信息
3. 参考WXT和Chrome扩展的官方文档
4. 在项目中创建issue描述问题

---

通过遵循本指南，您可以将现有扩展的功能成功迁移到服务运营工具集合中，为用户提供统一、高效的工具体验。
