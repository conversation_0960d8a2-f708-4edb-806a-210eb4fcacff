{"errNo": 0, "errMsg": "succ", "data": {"tree": {"edu-tools": {"product_line_id": 215, "product_line_name": "edu-tools", "services": [{"service_name": "fwyytool", "service_id": 20551, "owner": "<EMAIL>", "desc": "服务运营提效小工具"}, {"service_name": "dev-tools", "service_id": 22116, "owner": "<EMAIL>", "desc": "研发工具平台"}, {"service_name": "meshlog", "service_id": 20629, "owner": "<EMAIL>", "desc": "采集ingress日志用于流量分析"}]}, "lpc": {"product_line_id": 37, "product_line_name": "lpc", "services": [{"service_name": "coursetrans", "service_id": 1030, "owner": "<EMAIL>", "desc": "/lpc/coursetrans"}, {"service_name": "callcenterbrige", "service_id": 11024, "owner": "<EMAIL>", "desc": "外网接口转发"}, {"service_name": "touchmis", "service_id": 1093, "owner": "<EMAIL>", "desc": "/lpc/touchmis"}, {"service_name": "lpc-toolkit", "service_id": 623, "owner": "<EMAIL>", "desc": "lpc-toolkit"}, {"service_name": "laxin<PERSON>", "service_id": 627, "owner": "<EMAIL>", "desc": "人管laxinmis"}, {"service_name": "lpc-data", "service_id": 895, "owner": "<EMAIL>", "desc": "lpc-data"}, {"service_name": "fe-alita", "service_id": 896, "owner": "<EMAIL>", "desc": "fe-alita"}, {"service_name": "fe-lpc", "service_id": 897, "owner": "<EMAIL>", "desc": "fe-lpc"}, {"service_name": "static-file", "service_id": 18570, "owner": "<EMAIL>", "desc": "根路径部署静态文件"}, {"service_name": "lpcactive", "service_id": 666, "owner": "<EMAIL>", "desc": "人管lpcactive"}, {"service_name": "insight", "service_id": 679, "owner": "<EMAIL>", "desc": "/lpc/insight"}, {"service_name": "lpcmsg", "service_id": 683, "owner": "<EMAIL>", "desc": "lpcmsg"}, {"service_name": "lpcleads", "service_id": 430, "owner": "<EMAIL>", "desc": "lpcleads"}, {"service_name": "allocate", "service_id": 10430, "owner": "<EMAIL>", "desc": "排灌班服务"}, {"service_name": "duxueactive", "service_id": 11476, "owner": "<EMAIL>", "desc": "督学对外服务"}, {"service_name": "<PERSON><PERSON><PERSON><PERSON>", "service_id": 483, "owner": "<EMAIL>", "desc": "/lpc/duxuesc"}, {"service_name": "commrecord", "service_id": 489, "owner": "<EMAIL>", "desc": "/lpc/commrecord"}, {"service_name": "lpckid", "service_id": 494, "owner": "<EMAIL>", "desc": "/lpc/lpckid"}, {"service_name": "coursetransgo", "service_id": 10737, "owner": "<EMAIL>", "desc": "服务运营的转化模块，LPC和辅导老师给学生下单使用"}]}, "support": {"product_line_id": 24, "product_line_name": "support", "services": [{"service_name": "dataproxy", "service_id": 11272, "owner": "<EMAIL>", "desc": "dataproxy"}, {"service_name": "fe-fwyy-base-service", "service_id": 10509, "owner": "<EMAIL>", "desc": "fe-fwyy-base-service"}, {"service_name": "fe-ai-portrait", "service_id": 20237, "owner": "<EMAIL>", "desc": "柠咖AI证件照工具"}, {"service_name": "bzr-bzrgo", "service_id": 19478, "owner": "<EMAIL>", "desc": "辅导数据后台"}, {"service_name": "assistantcourse", "service_id": 791, "owner": "<EMAIL>", "desc": "/support/assistantcourse"}, {"service_name": "agg", "service_id": 10015, "owner": "<EMAIL>", "desc": "agg"}, {"service_name": "lowcode-manager-server", "service_id": 15907, "owner": "<EMAIL>", "desc": "低码管理后台server模块"}, {"service_name": "fe-lowcode-manager", "service_id": 15909, "owner": "<EMAIL>", "desc": "低码管理后台前端模块"}, {"service_name": "cms-server", "service_id": 15911, "owner": "<EMAIL>", "desc": "可视化配置server模块"}, {"service_name": "assistantdesk<PERSON>", "service_id": 18983, "owner": "<EMAIL>", "desc": "assistantdesk<PERSON>"}, {"service_name": "fe-cms", "service_id": 15913, "owner": "<EMAIL>", "desc": "可视化配置前端模块"}, {"service_name": "pcassistant", "service_id": 554, "owner": "<EMAIL>", "desc": "/support/pcassistant"}, {"service_name": "zlink-task-manager", "service_id": 20522, "owner": "<EMAIL>", "desc": "zlink任务管理，数据链路压测用"}, {"service_name": "fe-fwyy-scheduling", "service_id": 13612, "owner": "<EMAIL>", "desc": "通用化排灌班"}, {"service_name": "fwyy-contract", "service_id": 17197, "owner": "<EMAIL>", "desc": "服务运营合约领域模块"}, {"service_name": "contract-allocate", "service_id": 17199, "owner": "<EMAIL>", "desc": "服务运营合约排灌班模块"}, {"service_name": "assistant-correction", "service_id": 563, "owner": "<EMAIL>", "desc": "/support/assistant-correction"}, {"service_name": "correction-h5", "service_id": 564, "owner": "<EMAIL>", "desc": "/support/correction-h5"}, {"service_name": "fe-fwyy-performance", "service_id": 13625, "owner": "<EMAIL>", "desc": "绩效规则引擎"}, {"service_name": "fe-first-line-teacher-v2", "service_id": 19772, "owner": "<EMAIL>", "desc": "工作台vue3项目"}, {"service_name": "fe-fwyy-independent-page", "service_id": 19263, "owner": "<EMAIL>", "desc": "用于装载包含第三方sdk，或者和工作台核心链路无关，只有一些简单交互的页面。\t"}, {"service_name": "fwyy-mesh", "service_id": 1345, "owner": "<EMAIL>", "desc": "fwyy-mesh"}, {"service_name": "data-engine-databus", "service_id": 21315, "owner": "<EMAIL>", "desc": "在线数据引擎 数据回传及监控"}, {"service_name": "arkgo", "service_id": 19269, "owner": "<EMAIL>", "desc": "服务运营-方舟golang"}, {"service_name": "assistantcheck", "service_id": 588, "owner": "<EMAIL>", "desc": "assistantcheck"}, {"service_name": "assistant-inspection", "service_id": 591, "owner": "<EMAIL>", "desc": " assistant-inspection"}, {"service_name": "rewrite", "service_id": 10319, "owner": "<EMAIL>", "desc": "服务运营rewrite"}, {"service_name": "cs-heron", "service_id": 593, "owner": "<EMAIL>", "desc": "cs-heron"}, {"service_name": "cs-heron-fe", "service_id": 595, "owner": "<EMAIL>", "desc": "cs-heron-fe"}, {"service_name": "userprofile", "service_id": 596, "owner": "<EMAIL>", "desc": "人管userprofile"}, {"service_name": "usercommit", "service_id": 10328, "owner": "<EMAIL>", "desc": "usercommit"}, {"service_name": "cloudtraceindex", "service_id": 16731, "owner": "<EMAIL>", "desc": "云影写入模块重构成标准化项目"}, {"service_name": "fe-assistant-manage", "service_id": 1372, "owner": "<EMAIL>", "desc": "fe-assistant-manage"}, {"service_name": "tag", "service_id": 1121, "owner": "<EMAIL>", "desc": "/support/tag"}, {"service_name": "data-broadcast", "service_id": 21090, "owner": "<EMAIL>", "desc": "服务运营新增数据播报模块。"}, {"service_name": "data-engine-server", "service_id": 16997, "owner": "<EMAIL>", "desc": "服务运营在线数据引擎-数据指标计算"}, {"service_name": "assistanttool", "service_id": 616, "owner": "<EMAIL>", "desc": "/support/assistanttool"}, {"service_name": "tower", "service_id": 10348, "owner": "<EMAIL>", "desc": "tower"}, {"service_name": "assistant-ai", "service_id": 22381, "owner": "<EMAIL>", "desc": "工作台AI相关能力"}, {"service_name": "assistanttool-fe", "service_id": 622, "owner": "<EMAIL>", "desc": "assistanttool-fe"}, {"service_name": "cs-swan", "service_id": 119, "owner": "<EMAIL>", "desc": "客服swan"}, {"service_name": "cs-pigeon", "service_id": 120, "owner": "<EMAIL>", "desc": "客服pigeon"}, {"service_name": "cs-cuckoo", "service_id": 121, "owner": "<EMAIL>", "desc": "客服cuckoo"}, {"service_name": "cs-misservice", "service_id": 122, "owner": "<EMAIL>", "desc": "客服misservice"}, {"service_name": "cs-fe-cuckoo", "service_id": 123, "owner": "<EMAIL>", "desc": "客服fe-cuckoo"}, {"service_name": "cs-fe-lark", "service_id": 124, "owner": "<EMAIL>", "desc": "客服fe-lark"}, {"service_name": "cs-fe-swan", "service_id": 125, "owner": "<EMAIL>", "desc": "客服fe-swan"}, {"service_name": "fe-fwyy-genke", "service_id": 13438, "owner": "<EMAIL>", "desc": "跟课通用化页面"}, {"service_name": "deskcrm", "service_id": 22913, "owner": "<EMAIL>", "desc": "服务运营工作台 crm 核心业务层服务"}, {"service_name": "assistantdesk", "service_id": 642, "owner": "<EMAIL>", "desc": "/support/assistantdesk"}, {"service_name": "muse", "service_id": 1410, "owner": "<EMAIL>", "desc": "muse添加rmq边车"}, {"service_name": "assistant<PERSON>b", "service_id": 643, "owner": "<EMAIL>", "desc": "/support/assistantweb"}, {"service_name": "<PERSON><PERSON>x", "service_id": 644, "owner": "<EMAIL>", "desc": "/support/assistantwx"}, {"service_name": "fe-assistant-datamis", "service_id": 1412, "owner": "<EMAIL>", "desc": "fe-assistant-datamis"}, {"service_name": "assistant-first-line-teacher", "service_id": 645, "owner": "<EMAIL>", "desc": "assistant-first-line-teacher"}, {"service_name": "fe-assistant-scheduling", "service_id": 1413, "owner": "<EMAIL>", "desc": "fe-assistant-scheduling"}, {"service_name": "fe-assistant-kp-right-v2", "service_id": 13189, "owner": "<EMAIL>", "desc": "企微通用化右侧页面"}, {"service_name": "fwyy-app-main", "service_id": 646, "owner": "<EMAIL>", "desc": "fwyy-app-main"}, {"service_name": "assistant-kp-right", "service_id": 647, "owner": "<EMAIL>", "desc": "assistant-kp-right"}, {"service_name": "yike-assistant-question", "service_id": 648, "owner": "<EMAIL>", "desc": "yike-assistant-question"}, {"service_name": "fe-probereport", "service_id": 18825, "owner": "<EMAIL>", "desc": "学情&诊断报告前端模块"}, {"service_name": "assistant-h5", "service_id": 650, "owner": "<EMAIL>", "desc": "assistant-h5"}, {"service_name": "assistant-tima", "service_id": 651, "owner": "<EMAIL>", "desc": "assistant-tima"}, {"service_name": "assistant-zb-train", "service_id": 653, "owner": "<EMAIL>", "desc": "assistant-zb-train"}, {"service_name": "qiwei-course", "service_id": 18573, "owner": "<EMAIL>", "desc": "企微建课"}, {"service_name": "fe-assistant-cloud-trace", "service_id": 23182, "owner": "<EMAIL>", "desc": "云影vue3模块"}, {"service_name": "fe-survey-questionnaire", "service_id": 18577, "owner": "<EMAIL>", "desc": "调研问卷前端模块"}, {"service_name": "fe-interaction-management", "service_id": 16789, "owner": "<EMAIL>", "desc": "AI互动任务管理"}, {"service_name": "cs-fe-sparrow", "service_id": 151, "owner": "<EMAIL>", "desc": "客服cs-fe-sparrow"}, {"service_name": "touchmisgo", "service_id": 20381, "owner": "<EMAIL>", "desc": "触达接入层服务"}, {"service_name": "bzr-assignclass", "service_id": 159, "owner": "<EMAIL>", "desc": "班主任bzr-assignclass"}, {"service_name": "fe-fwyy-module-page", "service_id": 14755, "owner": "<EMAIL>", "desc": "编排模块化页面"}, {"service_name": "assistant<PERSON><PERSON>", "service_id": 20389, "owner": "<EMAIL>", "desc": "自主建课go模块"}, {"service_name": "training", "service_id": 10407, "owner": "<EMAIL>", "desc": "training"}, {"service_name": "plum", "service_id": 11432, "owner": "<EMAIL>", "desc": "服务研发-排灌班统一对外服务网关"}, {"service_name": "pc-work-station", "service_id": 688, "owner": "<EMAIL>", "desc": "pc-work-station"}, {"service_name": "internaltraining", "service_id": 689, "owner": "<EMAIL>", "desc": "/support/internaltraining"}, {"service_name": "proxy", "service_id": 179, "owner": "<EMAIL>", "desc": "nshead转发服务"}, {"service_name": "bzr-assignclassgo", "service_id": 182, "owner": "support", "desc": "班主任assignclassgo"}, {"service_name": "fe-datareport", "service_id": 21176, "owner": "<EMAIL>", "desc": "数据播报项目"}, {"service_name": "fe-assistant-correction-v2", "service_id": 22466, "owner": "<EMAIL>", "desc": "批改系统重构"}, {"service_name": "delayer", "service_id": 963, "owner": "<EMAIL>", "desc": "delayer"}, {"service_name": "fe-lark-assistant", "service_id": 21452, "owner": "<EMAIL>", "desc": "百灵助手"}, {"service_name": "assistant-common", "service_id": 740, "owner": "<EMAIL>", "desc": "assistant-common"}, {"service_name": "data-engine-dashboard", "service_id": 20455, "owner": "<EMAIL>", "desc": "在线数据引擎看板数据生产模块，蓝鲸看板筛选项接口"}, {"service_name": "genke", "service_id": 747, "owner": "admin", "desc": "genke"}, {"service_name": "requst-genie-server", "service_id": 22763, "owner": "<EMAIL>", "desc": "请求工作流后端，存储api发布信息和触发ci"}, {"service_name": "static-file", "service_id": 10733, "owner": "<EMAIL>", "desc": "static-file"}, {"service_name": "fwyy-evaluate", "service_id": 18672, "owner": "<EMAIL>", "desc": "服务运营满意度评价"}, {"service_name": "eduprobe", "service_id": 18674, "owner": "<EMAIL>", "desc": "学情&诊断报告后端模块"}, {"service_name": "bzrcs", "service_id": 10483, "owner": "<EMAIL>", "desc": "bzrcs"}, {"service_name": "fe-touch", "service_id": 11509, "owner": "<EMAIL>", "desc": "触达配置化前端模块"}]}}}}