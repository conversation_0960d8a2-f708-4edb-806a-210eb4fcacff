# WARP.md

This file provides guidance to WARP (warp.dev) when working with code in this repository.

## Project Overview

This is a modular browser extension built with WXT framework (wxt.dev) for internal service operations. It's a TypeScript-based tool collection focused on developer productivity for zuoyebang.cc services.

## Commands

### Development
```bash
npm run dev              # Chrome dev mode with hot reload
npm run dev:firefox      # Firefox dev mode
npm run create-tool      # Generate new tool from template (interactive)
```

### Testing
```bash
npm run test             # Run tests in watch mode
npm run test:all         # Run all tests (unit + e2e + integration)
npm run test:coverage    # Generate coverage report
npm run validate:build   # Verify build output integrity
```

### Building & Packaging
```bash
npm run build            # Production build for Chrome
npm run build:firefox    # Production build for Firefox  
npm run zip              # Create Chrome extension .zip
npm run zip:firefox      # Create Firefox extension .zip
```

### Setup
```bash
npm install              # Install deps + setup styles (automatic)
npm run setup-styles     # Manually fix style symlinks if needed
```

## Architecture

### Entry Points & Communication Flow

The extension operates through four entry points that communicate via Chrome runtime messaging:

```
background.ts ← → popup/main.ts ← → content.ts
                      ↓
                newtab/main.ts
```

- **Background** (`entrypoints/background.ts`): Manages extension lifecycle, handles cross-tab messaging, coordinates tool execution
- **Popup** (`entrypoints/popup/main.ts`): Main UI, registers all tools via `toolRegistry`, manages tool execution
- **Content** (`entrypoints/content.ts`): Page injection, scoped to `*.zuoyebang.cc/*`, provides DOM access for tools
- **NewTab** (`entrypoints/newtab/`): Full-screen tool execution environment, receives data via URL params from popup

### Core Systems

**Tool System** (`utils/tool-template.ts` + `utils/tool-registry.ts`)
- All tools extend `BaseTool` abstract class
- Registry pattern manages tool lifecycle and dependencies
- Tools declare `displayMode`: 'popup' | 'newtab' for execution context
- Support for version management, categories, and badges

**UI Components** (`utils/ui-components.ts`)
- `Modal`, `Notification`, `Loading`, `Confirm` classes
- Centralized component creation via `UIComponents.create*()`
- Style injection handled automatically

**Settings & Storage** (`utils/settings-manager.ts`)
- All data persists via Chrome storage APIs
- Settings are tool-scoped using prefixed keys
- Automatic migration when tool versions change

**Style Management** (`utils/style-manager.ts`)
- CSS module dependency system
- Design tokens in `styles/design-tokens.css`
- Modules registered with dependencies, loaded in correct order

### Tool Registration Pattern

Tools are registered in `entrypoints/popup/main.ts`:

```typescript
// 1. Import tool class
import { MyTool } from '../../tools/my-tool';

// 2. Register with settings preservation  
await registerToolWithSettings(new MyTool());
```

The `registerToolWithSettings()` function preserves user settings across tool updates.

## Tool Development

### Using Tool Generator (Recommended)

```bash
npm run create-tool
# Interactive prompts for:
# - Tool ID (kebab-case)
# - Name, description, icon
# - Category (productivity/development/utility)
# - Tool type (basic/ui/api/content)
```

### Manual Tool Creation

1. Create tool class extending `BaseTool` in `tools/`
2. Implement required properties and `action()` method
3. Register in `entrypoints/popup/main.ts`
4. Add styles to `styles/` if needed
5. Update permissions in `wxt.config.ts` if required

### Tool Lifecycle Hooks

- `action()`: Main execution (required)
- `onNewTabInit()`: NewTab environment setup
- `onNewTabDestroy()`: NewTab cleanup
- `validate()`: Pre-execution validation
- `getDependencies()`: Declare tool dependencies

## Key Files & Purposes

- `wxt.config.ts` - Extension manifest and permissions
- `entrypoints/popup/main.ts` - Tool registration and UI initialization
- `utils/tool-registry.ts` - Central tool management
- `utils/tool-template.ts` - Base class and interfaces
- `scripts/create-tool.js` - Tool generation script
- `styles/design-tokens.css` - CSS variables for theming

## Testing Strategy

- **Unit Tests**: Tool logic, utilities (`tests/`)
- **E2E Tests**: Full extension flows (`tests/e2e/`)
- **Integration Tests**: Build validation (`scripts/integration-test.js`)
- Test mocks for Chrome APIs in `tests/setup.ts`

## Extension Permissions

Current manifest permissions:
- **Core**: `activeTab`, `storage`, `tabs`, `scripting`
- **Features**: `notifications`, `downloads`, `clipboardWrite/Read`, `cookies`, `webRequest`
- **Hosts**: `<all_urls>`, `*.zuoyebang.cc/*`

Add new permissions in `wxt.config.ts` only when necessary.

## Development Tips

### Import Aliases
- `@/` → project root
- `@/tools` → tools directory
- `@/utils` → utils directory
- `@/types` → types directory

### Style Symlinks
The build requires style symlinks. If you see style loading errors:
```bash
npm run setup-styles
```

### Chrome Extension Debugging
1. Open `chrome://extensions/`
2. Enable Developer mode
3. Load unpacked from `.output/chrome-mv3/`
4. Use Chrome DevTools for popup/background debugging

### Hot Reload Behavior
- Popup changes: Instant reload
- Background changes: Extension restart
- Content script changes: Page refresh required
- Tool code changes: Popup refresh required

## Common Patterns

### Notifications
```typescript
await this.showNotification('success', 'Operation complete');
await notificationManager.show({ type: 'error', message: 'Failed' });
```

### Modals
```typescript
const modal = UIComponents.createModal({
  title: 'Confirm',
  content: 'Are you sure?',
  buttons: [/* ... */]
});
```

### Storage
```typescript
// Tool-scoped storage
await settingsManager.updateToolSetting('tool-id', 'key', value);
const value = settingsManager.getToolSetting('tool-id', 'key');
```

### Cross-Component Messaging
```typescript
// Send from content/popup to background
chrome.runtime.sendMessage({ type: 'ACTION', data });

// Listen in background
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  // Handle message
});
```

## Zuoyebang.cc Integration

The extension is specifically scoped for zuoyebang.cc services:
- Content scripts injected on `*.zuoyebang.cc/*`
- Host permissions for falcon.op and log-search-docker subdomains
- Cookie access for XUID management
- Network monitoring for service requests

## Troubleshooting

### Style Loading Issues
→ Run `npm run setup-styles` and restart dev server

### Tool Not Appearing
→ Check registration in `entrypoints/popup/main.ts`
→ Verify tool categories array includes 'all'

### Permission Errors
→ Add required permissions to `wxt.config.ts`
→ Reload extension after permission changes

### Build Failures
→ Run `npm run compile` to check TypeScript errors
→ Ensure all imports use correct aliases

### Test Failures
→ Check Chrome API mocks in `tests/setup.ts`
→ Run specific test file: `npx vitest run tests/path/to/test.ts`
