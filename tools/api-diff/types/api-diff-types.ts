/**
 * API Diff Tool - 核心类型定义
 * 定义接口 Diff 工具的所有数据类型和接口
 */

// HTTP 方法类型
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE' | 'HEAD' | 'OPTIONS';

// Body 类型
export type BodyType = 'raw' | 'json' | 'form-data' | 'x-www-form-urlencoded';

// 认证类型
export type AuthType = 'basic' | 'bearer' | 'custom';

// 导出格式类型
export type ExportFormat = 'patch' | 'text';

/**
 * 请求配置接口
 * 包含构建 HTTP 请求所需的所有参数
 */
export interface RequestConfig {
  /** HTTP 方法 */
  method: HttpMethod;
  
  /** 旧接口 URL */
  oldUrl: string;
  
  /** 新接口 URL */
  newUrl: string;
  
  /** 请求头 */
  headers: Record<string, string>;
  
  /** 查询参数 */
  queryParams: Record<string, string>;
  
  /** 请求体配置 */
  body: {
    /** 请求体类型 */
    type: BodyType;
    /** 请求体内容 */
    content: string;
  };
  
  /** 认证配置 */
  auth?: {
    /** 认证类型 */
    type: AuthType;
    /** 认证凭据 */
    credentials: Record<string, string>;
  };
  
  /** 请求超时时间（毫秒） */
  timeout: number;
  
  /** 忽略的字段列表（用于 Diff 计算） */
  ignoreFields: string[];
}

/**
 * API 响应接口
 * 包含单个 API 请求的完整响应信息
 */
export interface ApiResponse {
  /** HTTP 状态码 */
  status: number;
  
  /** HTTP 状态文本 */
  statusText: string;
  
  /** 响应头 */
  headers: Record<string, string>;
  
  /** 解析后的响应体（JSON 对象或其他类型） */
  body: any;
  
  /** 原始响应体文本 */
  bodyText: string;
  
  /** 是否为 JSON 响应 */
  isJson: boolean;
  
  /** 请求耗时（毫秒） */
  duration: number;
  
  /** 响应时间戳 */
  timestamp: number;
  
  /** 错误信息（如果请求失败） */
  error?: string;
}

/**
 * 双端响应接口
 * 包含对新旧两个接口的请求结果
 */
export interface DualResponse {
  /** 旧接口响应 */
  old: ApiResponse | null;

  /** 新接口响应 */
  new: ApiResponse | null;

  /** 请求开始时间 */
  startTime: number;

  /** 请求结束时间 */
  endTime: number;

  /** 整体请求是否成功 */
  success: boolean;

  /** 错误信息列表 */
  errors: string[];
}

/**
 * 双端执行结果接口
 * 扩展的双端响应结果，包含更多执行状态信息
 */
export interface DualExecutionResult {
  /** 旧接口响应 */
  oldResponse: ApiResponse | null;

  /** 新接口响应 */
  newResponse: ApiResponse | null;

  /** 旧接口错误信息 */
  oldError: string | null;

  /** 新接口错误信息 */
  newError: string | null;

  /** 旧接口请求耗时 */
  oldDuration: number;

  /** 新接口请求耗时 */
  newDuration: number;

  /** 总耗时 */
  totalDuration: number;

  /** 响应时间戳 */
  timestamp: number;

  /** 是否成功（至少一个接口成功） */
  success: boolean;

  /** 两个接口都成功 */
  bothSucceeded: boolean;

  /** 两个接口都失败 */
  bothFailed: boolean;
}

/**
 * Diff 配置选项
 * 控制差异计算和显示的参数
 */
export interface DiffOptions {
  /** 数组差异配置 */
  arrays: {
    /** 是否检测数组元素移动 */
    detectMove: boolean;
    /** 移动时是否包含值 */
    includeValueOnMove: boolean;
  };
  
  /** 文本差异配置 */
  textDiff: {
    /** 启用文本差异的最小长度 */
    minLength: number;
  };
  
  /** 忽略的字段列表 */
  ignoreFields: string[];
  
  /** 是否仅显示变更项 */
  showOnlyChanges: boolean;
}

/**
 * 验证结果接口
 * 表示表单验证的结果
 */
export interface ValidationResult {
  /** 是否验证通过 */
  isValid: boolean;
  
  /** 错误信息映射（字段名 -> 错误信息） */
  errors: Record<string, string>;
}

/**
 * cURL 解析结果接口
 * 表示 cURL 命令解析的结果
 */
export interface ParseResult {
  /** 是否解析成功 */
  success: boolean;
  
  /** 解析出的请求配置 */
  config?: Partial<RequestConfig>;
  
  /** 错误信息 */
  error?: string;
  
  /** 错误位置（如果有） */
  errorPosition?: number;
}

/**
 * 配置历史记录接口
 * 用于保存和管理历史配置
 */
export interface ConfigHistory {
  /** 配置 ID */
  id: string;
  
  /** 配置名称 */
  name: string;
  
  /** 请求配置 */
  config: RequestConfig;
  
  /** 创建时间 */
  createdAt: number;
  
  /** 最后使用时间 */
  lastUsedAt: number;
  
  /** 使用次数 */
  usageCount: number;
}

/**
 * 存储配置接口
 * 控制本地存储的行为
 */
export interface StorageConfig {
  /** 最大历史记录数量 */
  maxHistoryCount: number;
  
  /** 存储键前缀 */
  keyPrefix: string;
  
  /** 是否启用压缩 */
  enableCompression: boolean;
}

/**
 * 组件状态接口
 * 表示各个组件的状态
 */
export interface ComponentState {
  /** 是否正在加载 */
  loading: boolean;
  
  /** 错误信息 */
  error?: string;
  
  /** 是否已初始化 */
  initialized: boolean;
}

/**
 * 请求执行状态接口
 * 表示请求执行的当前状态
 */
export interface RequestExecutionState {
  /** 是否正在执行 */
  executing: boolean;
  
  /** 执行进度（0-100） */
  progress: number;
  
  /** 当前状态描述 */
  statusText: string;
  
  /** 是否可以取消 */
  cancellable: boolean;
}

/**
 * UI 主题配置接口
 * 控制界面主题和样式
 */
export interface ThemeConfig {
  /** 主题模式 */
  mode: 'light' | 'dark' | 'auto';
  
  /** 主色调 */
  primaryColor: string;
  
  /** 字体大小 */
  fontSize: 'small' | 'medium' | 'large';
  
  /** 是否启用动画 */
  enableAnimations: boolean;
}

/**
 * 工具配置接口
 * 包含工具的全局配置选项
 */
export interface ToolConfig {
  /** 默认超时时间 */
  defaultTimeout: number;
  
  /** 默认忽略字段 */
  defaultIgnoreFields: string[];
  
  /** 是否自动保存配置 */
  autoSaveConfig: boolean;
  
  /** 最大并发请求数 */
  maxConcurrentRequests: number;
  
  /** 主题配置 */
  theme: ThemeConfig;
  
  /** 存储配置 */
  storage: StorageConfig;
}

/**
 * 事件类型定义
 * 定义组件间通信的事件类型
 */
export interface EventMap {
  /** 配置变更事件 */
  'config-changed': RequestConfig;
  
  /** 请求开始事件 */
  'request-start': { oldUrl: string; newUrl: string };
  
  /** 请求完成事件 */
  'request-complete': DualResponse;
  
  /** 请求错误事件 */
  'request-error': { error: string; url?: string };
  
  /** 差异计算完成事件 */
  'diff-calculated': { hasChanges: boolean; changeCount: number };
  
  /** 配置导入事件 */
  'config-imported': RequestConfig;
  
  /** 配置导出事件 */
  'config-exported': { format: string; data: string };
}

/**
 * 常量定义
 */
export const CONSTANTS = {
  /** 默认超时时间（毫秒） */
  DEFAULT_TIMEOUT: 10000,
  
  /** 最大历史记录数量 */
  MAX_HISTORY_COUNT: 50,
  
  /** 存储键前缀 */
  STORAGE_KEY_PREFIX: 'api-diff-tool',
  
  /** 支持的 HTTP 方法 */
  HTTP_METHODS: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'HEAD', 'OPTIONS'] as const,
  
  /** 支持的 Body 类型 */
  BODY_TYPES: ['raw', 'json', 'form-data', 'x-www-form-urlencoded'] as const,
  
  /** 支持的认证类型 */
  AUTH_TYPES: ['basic', 'bearer', 'custom'] as const,
  
  /** 默认忽略字段 */
  DEFAULT_IGNORE_FIELDS: ['timestamp', 'traceId', 'requestId', '_timestamp', '__timestamp'],
  
  /** Diff 配置默认值 */
  DEFAULT_DIFF_OPTIONS: {
    arrays: {
      detectMove: true,
      includeValueOnMove: false
    },
    textDiff: {
      minLength: 60
    },
    ignoreFields: [],
    showOnlyChanges: false
  } as DiffOptions
} as const;
