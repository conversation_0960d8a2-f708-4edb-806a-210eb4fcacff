/**
 * Storage Utils
 * API Diff 工具的存储管理器，基于项目统一存储方案
 */

import type { 
  RequestConfig, 
  DualExecutionResult,
  ApiResponse 
} from '../types/api-diff-types';

/**
 * 存储的配置记录接口
 */
export interface StoredRequestConfig {
  id: string;
  name: string;
  config: RequestConfig;
  createdAt: number;
  lastUsed: number;
  usageCount: number;
  tags?: string[];
}

/**
 * 存储的执行历史记录接口
 */
export interface StoredExecutionHistory {
  id: string;
  configId: string;
  result: DualExecutionResult;
  timestamp: number;
  duration: number;
  success: boolean;
}

/**
 * 存储设置接口
 */
export interface ApiDiffSettings {
  maxConfigCount: number;
  maxHistoryCount: number;
  autoSaveConfigs: boolean;
  autoSaveHistory: boolean;
  defaultIgnoreFields: string[];
  defaultTimeout: number;
  theme: 'light' | 'dark';
}

/**
 * 存储数据结构接口
 */
export interface ApiDiffStorageData {
  configs: Record<string, StoredRequestConfig>;
  history: Record<string, StoredExecutionHistory>;
  settings: ApiDiffSettings;
  lastCleanup: number;
}

/**
 * API Diff 存储管理器
 */
export class ApiDiffStorageUtils {
  private static readonly STORAGE_KEY = 'api-diff-data';
  private static readonly STORAGE_VERSION = '1.0.0';
  
  private static readonly DEFAULT_SETTINGS: ApiDiffSettings = {
    maxConfigCount: 50,
    maxHistoryCount: 100,
    autoSaveConfigs: true,
    autoSaveHistory: true,
    defaultIgnoreFields: ['timestamp', 'traceId', 'requestId'],
    defaultTimeout: 10000,
    theme: 'light'
  };

  private static readonly DEFAULT_DATA: ApiDiffStorageData = {
    configs: {},
    history: {},
    settings: { ...this.DEFAULT_SETTINGS },
    lastCleanup: Date.now()
  };

  /**
   * 加载存储数据
   */
  static async loadData(): Promise<ApiDiffStorageData> {
    try {
      const result = await browser.storage.local.get(this.STORAGE_KEY);
      const data = result[this.STORAGE_KEY];
      
      if (!data) {
        return { ...this.DEFAULT_DATA };
      }

      // 合并默认设置，确保新增的设置项有默认值
      const mergedData: ApiDiffStorageData = {
        configs: data.configs || {},
        history: data.history || {},
        settings: { ...this.DEFAULT_SETTINGS, ...(data.settings || {}) },
        lastCleanup: data.lastCleanup || Date.now()
      };

      return mergedData;
    } catch (error) {
      console.error('加载 API Diff 数据失败:', error);
      return { ...this.DEFAULT_DATA };
    }
  }

  /**
   * 保存存储数据
   */
  static async saveData(data: ApiDiffStorageData): Promise<void> {
    try {
      await browser.storage.local.set({
        [this.STORAGE_KEY]: {
          ...data,
          version: this.STORAGE_VERSION,
          lastModified: Date.now()
        }
      });
    } catch (error) {
      console.error('保存 API Diff 数据失败:', error);
      throw error;
    }
  }

  /**
   * 保存请求配置
   */
  static async saveConfig(config: RequestConfig, name: string, tags?: string[]): Promise<string> {
    try {
      const data = await this.loadData();
      const configId = this.generateId();
      const now = Date.now();

      const storedConfig: StoredRequestConfig = {
        id: configId,
        name,
        config: this.serializeConfig(config),
        createdAt: now,
        lastUsed: now,
        usageCount: 1,
        tags: tags || []
      };

      data.configs[configId] = storedConfig;

      // 检查是否超过最大数量限制
      await this.cleanupConfigs(data);
      
      await this.saveData(data);
      return configId;
    } catch (error) {
      console.error('保存配置失败:', error);
      throw error;
    }
  }

  /**
   * 加载请求配置
   */
  static async loadConfig(configId: string): Promise<StoredRequestConfig | null> {
    try {
      const data = await this.loadData();
      const storedConfig = data.configs[configId];
      
      if (storedConfig) {
        // 更新使用统计
        storedConfig.lastUsed = Date.now();
        storedConfig.usageCount++;
        await this.saveData(data);
        
        return {
          ...storedConfig,
          config: this.deserializeConfig(storedConfig.config)
        };
      }
      
      return null;
    } catch (error) {
      console.error('加载配置失败:', error);
      return null;
    }
  }

  /**
   * 获取所有配置列表
   */
  static async getAllConfigs(): Promise<StoredRequestConfig[]> {
    try {
      const data = await this.loadData();
      return Object.values(data.configs)
        .map(config => ({
          ...config,
          config: this.deserializeConfig(config.config)
        }))
        .sort((a, b) => b.lastUsed - a.lastUsed);
    } catch (error) {
      console.error('获取配置列表失败:', error);
      return [];
    }
  }

  /**
   * 删除配置
   */
  static async deleteConfig(configId: string): Promise<void> {
    try {
      const data = await this.loadData();
      delete data.configs[configId];
      
      // 同时删除相关的历史记录
      Object.keys(data.history).forEach(historyId => {
        if (data.history[historyId].configId === configId) {
          delete data.history[historyId];
        }
      });
      
      await this.saveData(data);
    } catch (error) {
      console.error('删除配置失败:', error);
      throw error;
    }
  }

  /**
   * 保存执行历史
   */
  static async saveHistory(configId: string, result: DualExecutionResult): Promise<string> {
    try {
      const data = await this.loadData();
      
      if (!data.settings.autoSaveHistory) {
        return '';
      }

      const historyId = this.generateId();
      const history: StoredExecutionHistory = {
        id: historyId,
        configId,
        result: this.serializeExecutionResult(result),
        timestamp: Date.now(),
        duration: result.totalDuration,
        success: result.success
      };

      data.history[historyId] = history;

      // 检查是否超过最大数量限制
      await this.cleanupHistory(data);
      
      await this.saveData(data);
      return historyId;
    } catch (error) {
      console.error('保存历史记录失败:', error);
      throw error;
    }
  }

  /**
   * 获取执行历史
   */
  static async getHistory(configId?: string): Promise<StoredExecutionHistory[]> {
    try {
      const data = await this.loadData();
      let histories = Object.values(data.history);
      
      if (configId) {
        histories = histories.filter(h => h.configId === configId);
      }
      
      return histories
        .map(history => ({
          ...history,
          result: this.deserializeExecutionResult(history.result)
        }))
        .sort((a, b) => b.timestamp - a.timestamp);
    } catch (error) {
      console.error('获取历史记录失败:', error);
      return [];
    }
  }

  /**
   * 清理历史记录
   */
  static async clearHistory(configId?: string): Promise<void> {
    try {
      const data = await this.loadData();
      
      if (configId) {
        // 清理特定配置的历史
        Object.keys(data.history).forEach(historyId => {
          if (data.history[historyId].configId === configId) {
            delete data.history[historyId];
          }
        });
      } else {
        // 清理所有历史
        data.history = {};
      }
      
      await this.saveData(data);
    } catch (error) {
      console.error('清理历史记录失败:', error);
      throw error;
    }
  }

  /**
   * 获取设置
   */
  static async getSettings(): Promise<ApiDiffSettings> {
    try {
      const data = await this.loadData();
      return { ...data.settings };
    } catch (error) {
      console.error('获取设置失败:', error);
      return { ...this.DEFAULT_SETTINGS };
    }
  }

  /**
   * 更新设置
   */
  static async updateSettings(newSettings: Partial<ApiDiffSettings>): Promise<void> {
    try {
      const data = await this.loadData();
      data.settings = { ...data.settings, ...newSettings };
      await this.saveData(data);
    } catch (error) {
      console.error('更新设置失败:', error);
      throw error;
    }
  }

  /**
   * 重置设置
   */
  static async resetSettings(): Promise<void> {
    try {
      const data = await this.loadData();
      data.settings = { ...this.DEFAULT_SETTINGS };
      await this.saveData(data);
    } catch (error) {
      console.error('重置设置失败:', error);
      throw error;
    }
  }

  /**
   * 获取存储统计信息
   */
  static async getStorageStats(): Promise<{
    configCount: number;
    historyCount: number;
    totalSize: number;
    lastCleanup: number;
  }> {
    try {
      const data = await this.loadData();
      const serializedData = JSON.stringify(data);
      
      return {
        configCount: Object.keys(data.configs).length,
        historyCount: Object.keys(data.history).length,
        totalSize: new Blob([serializedData]).size,
        lastCleanup: data.lastCleanup
      };
    } catch (error) {
      console.error('获取存储统计失败:', error);
      return {
        configCount: 0,
        historyCount: 0,
        totalSize: 0,
        lastCleanup: 0
      };
    }
  }

  /**
   * 清理过期数据
   */
  static async cleanup(): Promise<void> {
    try {
      const data = await this.loadData();
      
      await this.cleanupConfigs(data);
      await this.cleanupHistory(data);
      
      data.lastCleanup = Date.now();
      await this.saveData(data);
    } catch (error) {
      console.error('清理数据失败:', error);
      throw error;
    }
  }

  /**
   * 导出数据
   */
  static async exportData(): Promise<string> {
    try {
      const data = await this.loadData();
      return JSON.stringify(data, null, 2);
    } catch (error) {
      console.error('导出数据失败:', error);
      throw error;
    }
  }

  /**
   * 导入数据
   */
  static async importData(jsonData: string): Promise<void> {
    try {
      const importedData = JSON.parse(jsonData) as ApiDiffStorageData;
      
      // 验证数据结构
      if (!this.validateImportData(importedData)) {
        throw new Error('Invalid data format');
      }
      
      // 合并设置
      const mergedData: ApiDiffStorageData = {
        configs: importedData.configs || {},
        history: importedData.history || {},
        settings: { ...this.DEFAULT_SETTINGS, ...(importedData.settings || {}) },
        lastCleanup: Date.now()
      };
      
      await this.saveData(mergedData);
    } catch (error) {
      console.error('导入数据失败:', error);
      throw error;
    }
  }

  // 私有方法

  /**
   * 生成唯一ID
   */
  private static generateId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 序列化配置
   */
  private static serializeConfig(config: RequestConfig): RequestConfig {
    // 深拷贝配置，移除可能的循环引用
    return JSON.parse(JSON.stringify(config));
  }

  /**
   * 反序列化配置
   */
  private static deserializeConfig(config: RequestConfig): RequestConfig {
    return JSON.parse(JSON.stringify(config));
  }

  /**
   * 序列化执行结果
   */
  private static serializeExecutionResult(result: DualExecutionResult): DualExecutionResult {
    return JSON.parse(JSON.stringify(result));
  }

  /**
   * 反序列化执行结果
   */
  private static deserializeExecutionResult(result: DualExecutionResult): DualExecutionResult {
    return JSON.parse(JSON.stringify(result));
  }

  /**
   * 清理配置（保留最近使用的）
   */
  private static async cleanupConfigs(data: ApiDiffStorageData): Promise<void> {
    const configs = Object.values(data.configs);
    const maxCount = data.settings.maxConfigCount;
    
    if (configs.length > maxCount) {
      // 按最后使用时间排序，保留最近的
      const sortedConfigs = configs.sort((a, b) => b.lastUsed - a.lastUsed);
      const toKeep = sortedConfigs.slice(0, maxCount);
      const toRemove = sortedConfigs.slice(maxCount);
      
      // 重建配置对象
      data.configs = {};
      toKeep.forEach(config => {
        data.configs[config.id] = config;
      });
      
      // 删除相关历史记录
      toRemove.forEach(config => {
        Object.keys(data.history).forEach(historyId => {
          if (data.history[historyId].configId === config.id) {
            delete data.history[historyId];
          }
        });
      });
    }
  }

  /**
   * 清理历史记录（保留最新的）
   */
  private static async cleanupHistory(data: ApiDiffStorageData): Promise<void> {
    const histories = Object.values(data.history);
    const maxCount = data.settings.maxHistoryCount;
    
    if (histories.length > maxCount) {
      // 按时间戳排序，保留最新的
      const sortedHistories = histories.sort((a, b) => b.timestamp - a.timestamp);
      const toKeep = sortedHistories.slice(0, maxCount);
      
      // 重建历史对象
      data.history = {};
      toKeep.forEach(history => {
        data.history[history.id] = history;
      });
    }
  }

  /**
   * 验证导入数据
   */
  private static validateImportData(data: any): data is ApiDiffStorageData {
    return (
      typeof data === 'object' &&
      data !== null &&
      (typeof data.configs === 'object' || data.configs === undefined) &&
      (typeof data.history === 'object' || data.history === undefined) &&
      (typeof data.settings === 'object' || data.settings === undefined)
    );
  }
}
