/**
 * Validation Utils
 * 表单验证工具，提供统一的验证接口和错误处理
 */

import type { 
  RequestConfig, 
  ValidationResult, 
  HttpMethod 
} from '../types/api-diff-types';

/**
 * 验证规则接口
 */
export interface ValidationRule {
  field: string;
  validator: (value: any, config?: RequestConfig) => string | null;
  message?: string;
}

/**
 * 验证器类
 */
export class ValidationUtils {
  
  /**
   * 验证 URL 格式
   */
  static validateUrl(url: string): string | null {
    if (!url || !url.trim()) {
      return 'URL is required';
    }

    const trimmedUrl = url.trim();
    
    try {
      const urlObj = new URL(trimmedUrl);
      
      // 检查协议
      if (!['http:', 'https:'].includes(urlObj.protocol)) {
        return 'URL must use HTTP or HTTPS protocol';
      }
      
      // 检查主机名
      if (!urlObj.hostname) {
        return 'URL must have a valid hostname';
      }
      
      return null; // 验证通过
    } catch (error) {
      return 'Invalid URL format';
    }
  }

  /**
   * 验证 JSON 格式
   */
  static validateJson(jsonString: string): string | null {
    if (!jsonString || !jsonString.trim()) {
      return null; // 空 JSON 是允许的
    }

    try {
      JSON.parse(jsonString);
      return null; // 验证通过
    } catch (error) {
      const err = error as SyntaxError;
      
      // 尝试提供更友好的错误信息
      if (err.message.includes('Unexpected token')) {
        const match = err.message.match(/position (\d+)/);
        if (match) {
          const position = parseInt(match[1]);
          const lines = jsonString.substring(0, position).split('\n');
          const line = lines.length;
          const column = lines[lines.length - 1].length + 1;
          return `Invalid JSON syntax at line ${line}, column ${column}`;
        }
      }
      
      return `Invalid JSON format: ${err.message}`;
    }
  }

  /**
   * 验证 HTTP 方法与 Body 的匹配性
   */
  static validateMethodBodyCompatibility(method: HttpMethod, hasBody: boolean): string | null {
    const methodsWithoutBody = ['GET', 'HEAD', 'OPTIONS'];
    
    if (methodsWithoutBody.includes(method) && hasBody) {
      return `${method} requests should not have a request body`;
    }
    
    return null; // 验证通过
  }

  /**
   * 验证请求头格式
   */
  static validateHeaders(headers: Record<string, string>): string | null {
    for (const [key, value] of Object.entries(headers)) {
      // 检查键名
      if (!key.trim()) {
        return 'Header name cannot be empty';
      }
      
      // 检查键名格式（HTTP 头名称规范）
      if (!/^[a-zA-Z0-9\-_]+$/.test(key)) {
        return `Invalid header name: "${key}". Header names can only contain letters, numbers, hyphens, and underscores`;
      }
      
      // 检查值（允许为空，但不能包含换行符）
      if (value.includes('\n') || value.includes('\r')) {
        return `Header value for "${key}" cannot contain line breaks`;
      }
    }
    
    return null; // 验证通过
  }

  /**
   * 验证查询参数
   */
  static validateQueryParams(params: Record<string, string>): string | null {
    for (const [key, value] of Object.entries(params)) {
      // 检查键名
      if (!key.trim()) {
        return 'Query parameter name cannot be empty';
      }
      
      // 检查特殊字符（基本的 URL 安全检查）
      if (key.includes('=') || key.includes('&')) {
        return `Query parameter name "${key}" contains invalid characters (= or &)`;
      }
    }
    
    return null; // 验证通过
  }

  /**
   * 验证认证配置
   */
  static validateAuth(auth: RequestConfig['auth']): string | null {
    if (!auth) {
      return null; // 无认证配置是允许的
    }

    const { type, credentials } = auth;

    switch (type) {
      case 'basic':
        if (!credentials.username || !credentials.password) {
          return 'Basic authentication requires both username and password';
        }
        break;
        
      case 'bearer':
        if (!credentials.token) {
          return 'Bearer authentication requires a token';
        }
        break;
        
      case 'custom':
        if (!credentials.headerName || !credentials.headerValue) {
          return 'Custom authentication requires both header name and value';
        }
        
        // 验证自定义头名称格式
        if (!/^[a-zA-Z0-9\-_]+$/.test(credentials.headerName)) {
          return 'Custom header name can only contain letters, numbers, hyphens, and underscores';
        }
        break;
        
      default:
        return `Unknown authentication type: ${type}`;
    }
    
    return null; // 验证通过
  }

  /**
   * 验证超时设置
   */
  static validateTimeout(timeout: number): string | null {
    if (timeout <= 0) {
      return 'Timeout must be greater than 0';
    }
    
    if (timeout > 300000) { // 5 分钟
      return 'Timeout cannot exceed 5 minutes (300000ms)';
    }
    
    return null; // 验证通过
  }

  /**
   * 验证完整的请求配置
   */
  static validateRequestConfig(config: RequestConfig): ValidationResult {
    const errors: Record<string, string> = {};

    // 验证 URL
    const oldUrlError = this.validateUrl(config.oldUrl);
    if (oldUrlError) {
      errors.oldUrl = oldUrlError;
    }

    const newUrlError = this.validateUrl(config.newUrl);
    if (newUrlError) {
      errors.newUrl = newUrlError;
    }

    // 验证请求头
    const headersError = this.validateHeaders(config.headers);
    if (headersError) {
      errors.headers = headersError;
    }

    // 验证查询参数
    const queryError = this.validateQueryParams(config.queryParams);
    if (queryError) {
      errors.queryParams = queryError;
    }

    // 验证 Body
    const hasBody = config.body.content.trim().length > 0;
    
    // 检查方法与 Body 的兼容性
    const methodBodyError = this.validateMethodBodyCompatibility(config.method, hasBody);
    if (methodBodyError) {
      errors.method = methodBodyError;
    }

    // 验证 JSON Body
    if (config.body.type === 'json' && hasBody) {
      const jsonError = this.validateJson(config.body.content);
      if (jsonError) {
        errors.body = jsonError;
      }
    }

    // 验证认证配置
    const authError = this.validateAuth(config.auth);
    if (authError) {
      errors.auth = authError;
    }

    // 验证超时设置
    const timeoutError = this.validateTimeout(config.timeout);
    if (timeoutError) {
      errors.timeout = timeoutError;
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  }

  /**
   * 实时验证单个字段
   */
  static validateField(fieldName: string, value: any, config?: RequestConfig): string | null {
    switch (fieldName) {
      case 'oldUrl':
      case 'newUrl':
        return this.validateUrl(value);
        
      case 'json':
        return this.validateJson(value);
        
      case 'headers':
        return this.validateHeaders(value);
        
      case 'queryParams':
        return this.validateQueryParams(value);
        
      case 'auth':
        return this.validateAuth(value);
        
      case 'timeout':
        return this.validateTimeout(value);
        
      case 'methodBody':
        if (config) {
          const hasBody = config.body.content.trim().length > 0;
          return this.validateMethodBodyCompatibility(config.method, hasBody);
        }
        return null;
        
      default:
        return null;
    }
  }

  /**
   * 格式化错误信息
   */
  static formatErrorMessage(field: string, error: string): string {
    const fieldLabels: Record<string, string> = {
      oldUrl: 'Old API URL',
      newUrl: 'New API URL',
      headers: 'Headers',
      queryParams: 'Query Parameters',
      body: 'Request Body',
      auth: 'Authentication',
      timeout: 'Timeout',
      method: 'HTTP Method'
    };

    const label = fieldLabels[field] || field;
    return `${label}: ${error}`;
  }

  /**
   * 获取字段验证状态的 CSS 类
   */
  static getValidationClass(isValid: boolean): string {
    return isValid ? 'is-valid' : 'is-invalid';
  }

  /**
   * 创建验证规则集
   */
  static createValidationRules(): ValidationRule[] {
    return [
      {
        field: 'oldUrl',
        validator: (value) => this.validateUrl(value)
      },
      {
        field: 'newUrl',
        validator: (value) => this.validateUrl(value)
      },
      {
        field: 'headers',
        validator: (value) => this.validateHeaders(value)
      },
      {
        field: 'queryParams',
        validator: (value) => this.validateQueryParams(value)
      },
      {
        field: 'auth',
        validator: (value) => this.validateAuth(value)
      },
      {
        field: 'timeout',
        validator: (value) => this.validateTimeout(value)
      },
      {
        field: 'json',
        validator: (value, config) => {
          if (config?.body.type === 'json') {
            return this.validateJson(value);
          }
          return null;
        }
      },
      {
        field: 'methodBody',
        validator: (value, config) => {
          if (config) {
            const hasBody = config.body.content.trim().length > 0;
            return this.validateMethodBodyCompatibility(config.method, hasBody);
          }
          return null;
        }
      }
    ];
  }

  /**
   * 批量验证多个字段
   */
  static validateFields(fields: Record<string, any>, config?: RequestConfig): ValidationResult {
    const errors: Record<string, string> = {};

    for (const [fieldName, value] of Object.entries(fields)) {
      const error = this.validateField(fieldName, value, config);
      if (error) {
        errors[fieldName] = error;
      }
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  }
}
