/**
 * JSO<PERSON> Highlighter
 * JSON 语法高亮和格式化工具，支持折叠展开和性能优化
 */

/**
 * JSON 节点类型
 */
export type JsonNodeType = 'object' | 'array' | 'string' | 'number' | 'boolean' | 'null';

/**
 * JSON 节点接口
 */
export interface JsonNode {
  type: JsonNodeType;
  value: any;
  key?: string;
  path: string;
  level: number;
  collapsed?: boolean;
  children?: JsonNode[];
}

/**
 * 高亮选项接口
 */
export interface HighlightOptions {
  maxDepth?: number;
  maxArrayItems?: number;
  maxStringLength?: number;
  enableCollapse?: boolean;
  enableLineNumbers?: boolean;
  theme?: 'light' | 'dark';
}

/**
 * JSON 高亮器类
 */
export class JsonHighlighter {
  private static readonly DEFAULT_OPTIONS: HighlightOptions = {
    maxDepth: 10,
    maxArrayItems: 100,
    maxStringLength: 1000,
    enableCollapse: true,
    enableLineNumbers: true,
    theme: 'light'
  };

  /**
   * 高亮 JSON 内容
   */
  static highlight(content: string, options: HighlightOptions = {}): string {
    const opts = { ...this.DEFAULT_OPTIONS, ...options };
    
    try {
      // 尝试解析 JSON
      const parsed = JSON.parse(content);
      return this.highlightJson(parsed, opts);
    } catch (error) {
      // 如果不是有效的 JSON，返回纯文本高亮
      return this.highlightText(content, opts);
    }
  }

  /**
   * 高亮 JSON 对象
   */
  private static highlightJson(data: any, options: HighlightOptions): string {
    const tree = this.buildJsonTree(data, '', 0, options);
    return this.renderJsonTree(tree, options);
  }

  /**
   * 构建 JSON 树结构
   */
  private static buildJsonTree(
    value: any, 
    key: string, 
    level: number, 
    options: HighlightOptions,
    path: string = ''
  ): JsonNode {
    const currentPath = path ? `${path}.${key}` : key;
    
    if (value === null) {
      return {
        type: 'null',
        value: null,
        key,
        path: currentPath,
        level
      };
    }

    if (typeof value === 'boolean') {
      return {
        type: 'boolean',
        value,
        key,
        path: currentPath,
        level
      };
    }

    if (typeof value === 'number') {
      return {
        type: 'number',
        value,
        key,
        path: currentPath,
        level
      };
    }

    if (typeof value === 'string') {
      return {
        type: 'string',
        value: this.truncateString(value, options.maxStringLength),
        key,
        path: currentPath,
        level
      };
    }

    if (Array.isArray(value)) {
      const children: JsonNode[] = [];
      const maxItems = options.maxArrayItems || 100;
      
      for (let i = 0; i < Math.min(value.length, maxItems); i++) {
        children.push(this.buildJsonTree(value[i], i.toString(), level + 1, options, currentPath));
      }
      
      // 如果数组被截断，添加省略号节点
      if (value.length > maxItems) {
        children.push({
          type: 'string',
          value: `... ${value.length - maxItems} more items`,
          key: '...',
          path: `${currentPath}[...]`,
          level: level + 1
        });
      }

      return {
        type: 'array',
        value,
        key,
        path: currentPath,
        level,
        collapsed: level >= (options.maxDepth || 10),
        children
      };
    }

    if (typeof value === 'object') {
      const children: JsonNode[] = [];
      const keys = Object.keys(value);
      
      for (const objKey of keys) {
        children.push(this.buildJsonTree(value[objKey], objKey, level + 1, options, currentPath));
      }

      return {
        type: 'object',
        value,
        key,
        path: currentPath,
        level,
        collapsed: level >= (options.maxDepth || 10),
        children
      };
    }

    // 未知类型，作为字符串处理
    return {
      type: 'string',
      value: String(value),
      key,
      path: currentPath,
      level
    };
  }

  /**
   * 渲染 JSON 树
   */
  private static renderJsonTree(node: JsonNode, options: HighlightOptions): string {
    const lines: string[] = [];
    this.renderNode(node, lines, options, true);
    
    let html = `<div class="json-viewer ${options.theme || 'light'}">`;
    
    if (options.enableLineNumbers) {
      html += '<div class="json-content with-line-numbers">';
      lines.forEach((line, index) => {
        html += `<div class="json-line">`;
        html += `<span class="line-number">${index + 1}</span>`;
        html += `<span class="line-content">${line}</span>`;
        html += `</div>`;
      });
    } else {
      html += '<div class="json-content">';
      lines.forEach(line => {
        html += `<div class="json-line">${line}</div>`;
      });
    }
    
    html += '</div></div>';
    
    return html;
  }

  /**
   * 渲染单个节点
   */
  private static renderNode(
    node: JsonNode, 
    lines: string[], 
    options: HighlightOptions, 
    isRoot: boolean = false
  ): void {
    const indent = '  '.repeat(node.level);
    
    switch (node.type) {
      case 'object':
        this.renderObject(node, lines, options, indent, isRoot);
        break;
      case 'array':
        this.renderArray(node, lines, options, indent, isRoot);
        break;
      case 'string':
        this.renderPrimitive(node, lines, indent, 'json-string');
        break;
      case 'number':
        this.renderPrimitive(node, lines, indent, 'json-number');
        break;
      case 'boolean':
        this.renderPrimitive(node, lines, indent, 'json-boolean');
        break;
      case 'null':
        this.renderPrimitive(node, lines, indent, 'json-null');
        break;
    }
  }

  /**
   * 渲染对象
   */
  private static renderObject(
    node: JsonNode, 
    lines: string[], 
    options: HighlightOptions, 
    indent: string, 
    isRoot: boolean
  ): void {
    const keyPart = node.key && !isRoot ? `<span class="json-key">"${node.key}"</span>: ` : '';
    const collapseButton = options.enableCollapse && node.children && node.children.length > 0 
      ? `<span class="collapse-button" data-path="${node.path}">${node.collapsed ? '▶' : '▼'}</span> `
      : '';
    
    lines.push(`${indent}${collapseButton}${keyPart}<span class="json-brace">{</span>`);
    
    if (!node.collapsed && node.children) {
      node.children.forEach((child, index) => {
        this.renderNode(child, lines, options);
        
        // 添加逗号（除了最后一个元素）
        if (index < node.children!.length - 1) {
          const lastLineIndex = lines.length - 1;
          lines[lastLineIndex] += '<span class="json-comma">,</span>';
        }
      });
    } else if (node.collapsed && node.children) {
      lines.push(`${indent}  <span class="json-ellipsis">... ${node.children.length} properties</span>`);
    }
    
    lines.push(`${indent}<span class="json-brace">}</span>`);
  }

  /**
   * 渲染数组
   */
  private static renderArray(
    node: JsonNode, 
    lines: string[], 
    options: HighlightOptions, 
    indent: string, 
    isRoot: boolean
  ): void {
    const keyPart = node.key && !isRoot ? `<span class="json-key">"${node.key}"</span>: ` : '';
    const collapseButton = options.enableCollapse && node.children && node.children.length > 0 
      ? `<span class="collapse-button" data-path="${node.path}">${node.collapsed ? '▶' : '▼'}</span> `
      : '';
    
    lines.push(`${indent}${collapseButton}${keyPart}<span class="json-bracket">[</span>`);
    
    if (!node.collapsed && node.children) {
      node.children.forEach((child, index) => {
        this.renderNode(child, lines, options);
        
        // 添加逗号（除了最后一个元素）
        if (index < node.children!.length - 1) {
          const lastLineIndex = lines.length - 1;
          lines[lastLineIndex] += '<span class="json-comma">,</span>';
        }
      });
    } else if (node.collapsed && node.children) {
      lines.push(`${indent}  <span class="json-ellipsis">... ${node.children.length} items</span>`);
    }
    
    lines.push(`${indent}<span class="json-bracket">]</span>`);
  }

  /**
   * 渲染基本类型
   */
  private static renderPrimitive(
    node: JsonNode, 
    lines: string[], 
    indent: string, 
    className: string
  ): void {
    const keyPart = node.key ? `<span class="json-key">"${node.key}"</span>: ` : '';
    let valuePart: string;
    
    switch (node.type) {
      case 'string':
        valuePart = `<span class="${className}">"${this.escapeHtml(node.value)}"</span>`;
        break;
      case 'null':
        valuePart = `<span class="${className}">null</span>`;
        break;
      default:
        valuePart = `<span class="${className}">${node.value}</span>`;
    }
    
    lines.push(`${indent}${keyPart}${valuePart}`);
  }

  /**
   * 高亮纯文本
   */
  private static highlightText(content: string, options: HighlightOptions): string {
    const lines = content.split('\n');
    let html = `<div class="text-viewer ${options.theme || 'light'}">`;
    
    if (options.enableLineNumbers) {
      html += '<div class="text-content with-line-numbers">';
      lines.forEach((line, index) => {
        html += `<div class="text-line">`;
        html += `<span class="line-number">${index + 1}</span>`;
        html += `<span class="line-content">${this.escapeHtml(line)}</span>`;
        html += `</div>`;
      });
    } else {
      html += '<div class="text-content">';
      lines.forEach(line => {
        html += `<div class="text-line">${this.escapeHtml(line)}</div>`;
      });
    }
    
    html += '</div></div>';
    return html;
  }

  /**
   * 截断字符串
   */
  private static truncateString(str: string, maxLength?: number): string {
    if (!maxLength || str.length <= maxLength) {
      return str;
    }
    
    return str.substring(0, maxLength) + '...';
  }

  /**
   * 转义 HTML
   */
  private static escapeHtml(text: string): string {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  /**
   * 绑定折叠展开事件
   */
  static bindCollapseEvents(container: HTMLElement): void {
    container.addEventListener('click', (event) => {
      const target = event.target as HTMLElement;
      
      if (target.classList.contains('collapse-button')) {
        const path = target.getAttribute('data-path');
        if (path) {
          this.toggleCollapse(container, path);
        }
      }
    });
  }

  /**
   * 切换折叠状态
   */
  private static toggleCollapse(container: HTMLElement, path: string): void {
    const button = container.querySelector(`[data-path="${path}"]`) as HTMLElement;
    if (!button) return;
    
    const isCollapsed = button.textContent === '▶';
    button.textContent = isCollapsed ? '▼' : '▶';
    
    // 找到对应的内容区域并切换显示状态
    let currentElement = button.parentElement?.nextElementSibling;
    let braceCount = 0;
    let foundClosingBrace = false;
    
    while (currentElement && !foundClosingBrace) {
      const content = currentElement.textContent || '';
      
      if (content.includes('{') || content.includes('[')) {
        braceCount++;
      }
      
      if (content.includes('}') || content.includes(']')) {
        braceCount--;
        if (braceCount === 0) {
          foundClosingBrace = true;
          break;
        }
      }
      
      // 切换显示状态
      const element = currentElement as HTMLElement;
      if (isCollapsed) {
        element.style.display = '';
      } else {
        element.style.display = 'none';
      }
      
      currentElement = currentElement.nextElementSibling;
    }
  }

  /**
   * 获取默认样式
   */
  static getDefaultStyles(): string {
    return `
      .json-viewer, .text-viewer {
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 14px;
        line-height: 1.4;
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 4px;
        overflow: auto;
        max-height: 500px;
      }
      
      .json-viewer.dark, .text-viewer.dark {
        background: #1e1e1e;
        border-color: #333;
        color: #d4d4d4;
      }
      
      .json-content, .text-content {
        padding: 12px;
      }
      
      .json-line, .text-line {
        display: flex;
        min-height: 20px;
      }
      
      .line-number {
        color: #999;
        margin-right: 12px;
        min-width: 30px;
        text-align: right;
        user-select: none;
      }
      
      .line-content {
        flex: 1;
      }
      
      .collapse-button {
        cursor: pointer;
        color: #666;
        margin-right: 4px;
        user-select: none;
      }
      
      .collapse-button:hover {
        color: #333;
      }
      
      .json-key {
        color: #0451a5;
        font-weight: 500;
      }
      
      .json-string {
        color: #0a8043;
      }
      
      .json-number {
        color: #098658;
      }
      
      .json-boolean {
        color: #0000ff;
      }
      
      .json-null {
        color: #0000ff;
      }
      
      .json-brace, .json-bracket {
        color: #000;
        font-weight: bold;
      }
      
      .json-comma {
        color: #000;
      }
      
      .json-ellipsis {
        color: #999;
        font-style: italic;
      }
      
      /* Dark theme colors */
      .json-viewer.dark .json-key {
        color: #9cdcfe;
      }
      
      .json-viewer.dark .json-string {
        color: #ce9178;
      }
      
      .json-viewer.dark .json-number {
        color: #b5cea8;
      }
      
      .json-viewer.dark .json-boolean {
        color: #569cd6;
      }
      
      .json-viewer.dark .json-null {
        color: #569cd6;
      }
      
      .json-viewer.dark .json-brace,
      .json-viewer.dark .json-bracket {
        color: #d4d4d4;
      }
      
      .json-viewer.dark .collapse-button {
        color: #ccc;
      }
      
      .json-viewer.dark .collapse-button:hover {
        color: #fff;
      }
    `;
  }
}
