/**
 * Dual Request Executor
 * 双端点并发请求执行器，同时向两个端点发送相同请求并处理结果
 */

import type { 
  RequestConfig, 
  ApiResponse, 
  DualExecutionResult 
} from '../types/api-diff-types';
import { HttpRequestUtils, RequestExecutionResult } from '../utils/http-request-utils';

/**
 * 执行状态枚举
 */
export enum ExecutionStatus {
  IDLE = 'idle',
  PREPARING = 'preparing',
  EXECUTING = 'executing',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  ERROR = 'error'
}

/**
 * 执行事件接口
 */
export interface ExecutionEvent {
  type: 'status-change' | 'progress' | 'old-response' | 'new-response' | 'completed' | 'error';
  data: any;
  timestamp: number;
}

/**
 * 双端点并发请求执行器类
 */
export class DualRequestExecutor {
  private config: RequestConfig | null = null;
  private status: ExecutionStatus = ExecutionStatus.IDLE;
  private oldRequestCanceller: (() => void) | null = null;
  private newRequestCanceller: (() => void) | null = null;
  
  // 执行结果
  private oldResult: RequestExecutionResult | null = null;
  private newResult: RequestExecutionResult | null = null;
  
  // 事件监听器
  private eventListeners: Map<string, ((event: ExecutionEvent) => void)[]> = new Map();

  constructor() {
    this.initializeEventListeners();
  }

  /**
   * 初始化事件监听器
   */
  private initializeEventListeners(): void {
    const eventTypes = ['status-change', 'progress', 'old-response', 'new-response', 'completed', 'error'];
    eventTypes.forEach(type => {
      this.eventListeners.set(type, []);
    });
  }

  /**
   * 添加事件监听器
   */
  public addEventListener(type: string, listener: (event: ExecutionEvent) => void): void {
    const listeners = this.eventListeners.get(type) || [];
    listeners.push(listener);
    this.eventListeners.set(type, listeners);
  }

  /**
   * 移除事件监听器
   */
  public removeEventListener(type: string, listener: (event: ExecutionEvent) => void): void {
    const listeners = this.eventListeners.get(type) || [];
    const index = listeners.indexOf(listener);
    if (index > -1) {
      listeners.splice(index, 1);
    }
  }

  /**
   * 触发事件
   */
  private emitEvent(type: string, data: any): void {
    const event: ExecutionEvent = {
      type: type as any,
      data,
      timestamp: Date.now()
    };

    const listeners = this.eventListeners.get(type) || [];
    listeners.forEach(listener => {
      try {
        listener(event);
      } catch (error) {
        console.error(`Event listener error for ${type}:`, error);
      }
    });
  }

  /**
   * 设置状态
   */
  private setStatus(newStatus: ExecutionStatus): void {
    if (this.status !== newStatus) {
      const oldStatus = this.status;
      this.status = newStatus;
      
      this.emitEvent('status-change', {
        oldStatus,
        newStatus,
        timestamp: Date.now()
      });
    }
  }

  /**
   * 执行双端点请求
   */
  public async execute(config: RequestConfig): Promise<DualExecutionResult> {
    this.config = config;
    this.oldResult = null;
    this.newResult = null;
    
    try {
      this.setStatus(ExecutionStatus.PREPARING);
      
      // 验证配置
      this.validateConfig(config);
      
      this.setStatus(ExecutionStatus.EXECUTING);
      
      // 创建请求选项
      const oldRequestOptions = HttpRequestUtils.createRequestOptions(config, config.oldUrl);
      const newRequestOptions = HttpRequestUtils.createRequestOptions(config, config.newUrl);
      
      // 创建可取消的请求
      const oldRequest = HttpRequestUtils.createCancellableRequest(oldRequestOptions);
      const newRequest = HttpRequestUtils.createCancellableRequest(newRequestOptions);
      
      // 保存取消函数
      this.oldRequestCanceller = oldRequest.cancel;
      this.newRequestCanceller = newRequest.cancel;
      
      // 并发执行请求
      const results = await Promise.allSettled([
        this.executeWithProgress(oldRequest.execute, 'old'),
        this.executeWithProgress(newRequest.execute, 'new')
      ]);
      
      // 处理结果
      const oldPromiseResult = results[0];
      const newPromiseResult = results[1];
      
      if (oldPromiseResult.status === 'fulfilled') {
        this.oldResult = oldPromiseResult.value;
        this.emitEvent('old-response', this.oldResult);
      }
      
      if (newPromiseResult.status === 'fulfilled') {
        this.newResult = newPromiseResult.value;
        this.emitEvent('new-response', this.newResult);
      }
      
      // 构建最终结果
      const dualResult = this.buildDualResult();
      
      this.setStatus(ExecutionStatus.COMPLETED);
      this.emitEvent('completed', dualResult);
      
      return dualResult;
      
    } catch (error) {
      this.setStatus(ExecutionStatus.ERROR);
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.emitEvent('error', { error: errorMessage });
      throw error;
    } finally {
      // 清理取消函数
      this.oldRequestCanceller = null;
      this.newRequestCanceller = null;
    }
  }

  /**
   * 带进度的请求执行
   */
  private async executeWithProgress(
    executeFunc: () => Promise<RequestExecutionResult>,
    endpoint: 'old' | 'new'
  ): Promise<RequestExecutionResult> {
    try {
      const result = await executeFunc();
      
      // 发送进度事件
      this.emitEvent('progress', {
        endpoint,
        status: result.error ? 'error' : 'success',
        duration: result.duration
      });
      
      return result;
    } catch (error) {
      // 发送错误进度事件
      this.emitEvent('progress', {
        endpoint,
        status: 'error',
        error: error instanceof Error ? error.message : String(error)
      });
      
      throw error;
    }
  }

  /**
   * 验证配置
   */
  private validateConfig(config: RequestConfig): void {
    if (!config.oldUrl || !config.newUrl) {
      throw new Error('Both old and new URLs are required');
    }
    
    if (!HttpRequestUtils.validateUrl(config.oldUrl)) {
      throw new Error('Invalid old URL format');
    }
    
    if (!HttpRequestUtils.validateUrl(config.newUrl)) {
      throw new Error('Invalid new URL format');
    }
  }

  /**
   * 构建双端点执行结果
   */
  private buildDualResult(): DualExecutionResult {
    const hasOldResponse = this.oldResult?.response !== null;
    const hasNewResponse = this.newResult?.response !== null;
    
    return {
      oldResponse: this.oldResult?.response || null,
      newResponse: this.newResult?.response || null,
      oldError: this.oldResult?.error || null,
      newError: this.newResult?.error || null,
      oldDuration: this.oldResult?.duration || 0,
      newDuration: this.newResult?.duration || 0,
      totalDuration: Math.max(
        this.oldResult?.duration || 0,
        this.newResult?.duration || 0
      ),
      timestamp: Date.now(),
      success: hasOldResponse || hasNewResponse, // 至少一个成功
      bothSucceeded: hasOldResponse && hasNewResponse,
      bothFailed: !hasOldResponse && !hasNewResponse
    };
  }

  /**
   * 取消执行
   */
  public cancel(): void {
    if (this.status === ExecutionStatus.EXECUTING) {
      // 取消两个请求
      if (this.oldRequestCanceller) {
        this.oldRequestCanceller();
      }
      
      if (this.newRequestCanceller) {
        this.newRequestCanceller();
      }
      
      this.setStatus(ExecutionStatus.CANCELLED);
      this.emitEvent('error', { error: 'Execution cancelled by user' });
    }
  }

  /**
   * 重试执行
   */
  public async retry(): Promise<DualExecutionResult> {
    if (!this.config) {
      throw new Error('No configuration available for retry');
    }
    
    return this.execute(this.config);
  }

  /**
   * 重试单个端点
   */
  public async retryEndpoint(endpoint: 'old' | 'new'): Promise<RequestExecutionResult> {
    if (!this.config) {
      throw new Error('No configuration available for retry');
    }
    
    const url = endpoint === 'old' ? this.config.oldUrl : this.config.newUrl;
    const requestOptions = HttpRequestUtils.createRequestOptions(this.config, url);
    
    this.setStatus(ExecutionStatus.EXECUTING);
    
    try {
      const result = await HttpRequestUtils.executeRequest(requestOptions);
      
      // 更新对应的结果
      if (endpoint === 'old') {
        this.oldResult = result;
        this.emitEvent('old-response', result);
      } else {
        this.newResult = result;
        this.emitEvent('new-response', result);
      }
      
      this.setStatus(ExecutionStatus.COMPLETED);
      return result;
      
    } catch (error) {
      this.setStatus(ExecutionStatus.ERROR);
      throw error;
    }
  }

  /**
   * 获取当前状态
   */
  public getStatus(): ExecutionStatus {
    return this.status;
  }

  /**
   * 获取当前配置
   */
  public getConfig(): RequestConfig | null {
    return this.config;
  }

  /**
   * 获取执行结果
   */
  public getResults(): {
    old: RequestExecutionResult | null;
    new: RequestExecutionResult | null;
  } {
    return {
      old: this.oldResult,
      new: this.newResult
    };
  }

  /**
   * 检查是否可以执行
   */
  public canExecute(): boolean {
    return this.status === ExecutionStatus.IDLE || 
           this.status === ExecutionStatus.COMPLETED || 
           this.status === ExecutionStatus.ERROR ||
           this.status === ExecutionStatus.CANCELLED;
  }

  /**
   * 检查是否可以取消
   */
  public canCancel(): boolean {
    return this.status === ExecutionStatus.EXECUTING;
  }

  /**
   * 检查是否可以重试
   */
  public canRetry(): boolean {
    return this.config !== null && (
      this.status === ExecutionStatus.COMPLETED ||
      this.status === ExecutionStatus.ERROR ||
      this.status === ExecutionStatus.CANCELLED
    );
  }

  /**
   * 重置执行器
   */
  public reset(): void {
    this.cancel(); // 如果正在执行，先取消
    
    this.config = null;
    this.oldResult = null;
    this.newResult = null;
    this.oldRequestCanceller = null;
    this.newRequestCanceller = null;
    
    this.setStatus(ExecutionStatus.IDLE);
  }

  /**
   * 销毁执行器
   */
  public destroy(): void {
    this.reset();
    this.eventListeners.clear();
  }
}
