/**
 * Diff Renderer
 * 差异渲染器，用于计算和渲染 JSON 响应的差异
 */

import type { 
  ApiResponse, 
  DualExecutionResult 
} from '../types/api-diff-types';

/**
 * 差异类型枚举
 */
export enum DiffType {
  ADDED = 'added',
  REMOVED = 'removed',
  MODIFIED = 'modified',
  UNCHANGED = 'unchanged'
}

/**
 * 差异节点接口
 */
export interface DiffNode {
  type: DiffType;
  path: string;
  key: string;
  oldValue?: any;
  newValue?: any;
  children?: DiffNode[];
  level: number;
  collapsed?: boolean;
}

/**
 * 差异选项接口
 */
export interface DiffOptions {
  ignoreFields?: string[];
  showOnlyChanges?: boolean;
  enableCollapse?: boolean;
  theme?: 'light' | 'dark';
  maxDepth?: number;
}

/**
 * 差异渲染器类
 */
export class DiffRenderer {
  private container: HTMLElement;
  private options: DiffOptions;
  private currentDiffNodes: DiffNode[] = [];

  constructor(container: HTMLElement, options: DiffOptions = {}) {
    this.container = container;
    this.options = {
      ignoreFields: [],
      showOnlyChanges: false,
      enableCollapse: true,
      theme: 'light',
      maxDepth: 10,
      ...options
    };
    
    this.injectStyles();
  }

  /**
   * 渲染双端响应差异
   */
  public renderDiff(result: DualExecutionResult): void {
    if (!result.oldResponse || !result.newResponse) {
      this.renderNoDiff(result);
      return;
    }

    try {
      const oldData = this.parseResponseData(result.oldResponse);
      const newData = this.parseResponseData(result.newResponse);
      
      const diffTree = this.computeDiff(oldData, newData, '');
      const filteredTree = this.filterDiff(diffTree);

      // 保存当前差异节点用于导出
      this.currentDiffNodes = filteredTree;

      this.container.innerHTML = `
        <div class="diff-container ${this.options.theme}">
          <div class="diff-header">
            ${this.renderDiffHeader(filteredTree)}
          </div>
          <div class="diff-content">
            ${this.renderDiffTree(filteredTree)}
          </div>
        </div>
      `;
      
      this.bindEvents();
      
    } catch (error) {
      this.renderError(error instanceof Error ? error.message : String(error));
    }
  }

  /**
   * 解析响应数据
   */
  private parseResponseData(response: ApiResponse): any {
    if (response.isJson && response.body) {
      return response.body;
    }
    
    // 尝试解析文本为 JSON
    try {
      return JSON.parse(response.bodyText);
    } catch {
      // 如果不是 JSON，返回文本
      return response.bodyText;
    }
  }

  /**
   * 计算差异
   */
  private computeDiff(oldData: any, newData: any, path: string, level: number = 0): DiffNode[] {
    const nodes: DiffNode[] = [];
    
    // 如果数据类型不同，直接标记为修改
    if (typeof oldData !== typeof newData) {
      nodes.push({
        type: DiffType.MODIFIED,
        path,
        key: this.getKeyFromPath(path),
        oldValue: oldData,
        newValue: newData,
        level
      });
      return nodes;
    }

    // 处理基本类型
    if (typeof oldData !== 'object' || oldData === null || newData === null) {
      if (oldData !== newData) {
        nodes.push({
          type: DiffType.MODIFIED,
          path,
          key: this.getKeyFromPath(path),
          oldValue: oldData,
          newValue: newData,
          level
        });
      } else {
        nodes.push({
          type: DiffType.UNCHANGED,
          path,
          key: this.getKeyFromPath(path),
          oldValue: oldData,
          newValue: newData,
          level
        });
      }
      return nodes;
    }

    // 处理数组
    if (Array.isArray(oldData) && Array.isArray(newData)) {
      return this.computeArrayDiff(oldData, newData, path, level);
    }

    // 处理对象
    if (typeof oldData === 'object' && typeof newData === 'object') {
      return this.computeObjectDiff(oldData, newData, path, level);
    }

    return nodes;
  }

  /**
   * 计算数组差异
   */
  private computeArrayDiff(oldArray: any[], newArray: any[], path: string, level: number): DiffNode[] {
    const nodes: DiffNode[] = [];
    const maxLength = Math.max(oldArray.length, newArray.length);

    for (let i = 0; i < maxLength; i++) {
      const itemPath = `${path}[${i}]`;
      const oldItem = i < oldArray.length ? oldArray[i] : undefined;
      const newItem = i < newArray.length ? newArray[i] : undefined;

      if (oldItem === undefined) {
        // 新增项
        nodes.push({
          type: DiffType.ADDED,
          path: itemPath,
          key: `[${i}]`,
          newValue: newItem,
          level
        });
      } else if (newItem === undefined) {
        // 删除项
        nodes.push({
          type: DiffType.REMOVED,
          path: itemPath,
          key: `[${i}]`,
          oldValue: oldItem,
          level
        });
      } else {
        // 比较项
        const childNodes = this.computeDiff(oldItem, newItem, itemPath, level + 1);
        if (childNodes.length > 0) {
          const hasChanges = childNodes.some(node => node.type !== DiffType.UNCHANGED);
          nodes.push({
            type: hasChanges ? DiffType.MODIFIED : DiffType.UNCHANGED,
            path: itemPath,
            key: `[${i}]`,
            oldValue: oldItem,
            newValue: newItem,
            children: childNodes,
            level,
            collapsed: level >= (this.options.maxDepth || 10)
          });
        }
      }
    }

    return nodes;
  }

  /**
   * 计算对象差异
   */
  private computeObjectDiff(oldObj: any, newObj: any, path: string, level: number): DiffNode[] {
    const nodes: DiffNode[] = [];
    const allKeys = new Set([...Object.keys(oldObj), ...Object.keys(newObj)]);

    for (const key of allKeys) {
      // 检查是否应该忽略此字段
      if (this.shouldIgnoreField(key)) {
        continue;
      }

      const keyPath = path ? `${path}.${key}` : key;
      const oldValue = oldObj[key];
      const newValue = newObj[key];

      if (!(key in oldObj)) {
        // 新增字段
        nodes.push({
          type: DiffType.ADDED,
          path: keyPath,
          key,
          newValue,
          level
        });
      } else if (!(key in newObj)) {
        // 删除字段
        nodes.push({
          type: DiffType.REMOVED,
          path: keyPath,
          key,
          oldValue,
          level
        });
      } else {
        // 比较字段
        const childNodes = this.computeDiff(oldValue, newValue, keyPath, level + 1);
        if (childNodes.length > 0) {
          const hasChanges = childNodes.some(node => node.type !== DiffType.UNCHANGED);
          nodes.push({
            type: hasChanges ? DiffType.MODIFIED : DiffType.UNCHANGED,
            path: keyPath,
            key,
            oldValue,
            newValue,
            children: childNodes,
            level,
            collapsed: level >= (this.options.maxDepth || 10)
          });
        }
      }
    }

    return nodes;
  }

  /**
   * 过滤差异树
   */
  private filterDiff(nodes: DiffNode[]): DiffNode[] {
    if (!this.options.showOnlyChanges) {
      return nodes;
    }

    return nodes.filter(node => {
      if (node.type !== DiffType.UNCHANGED) {
        return true;
      }
      
      // 如果有子节点，递归过滤
      if (node.children) {
        const filteredChildren = this.filterDiff(node.children);
        if (filteredChildren.length > 0) {
          node.children = filteredChildren;
          return true;
        }
      }
      
      return false;
    });
  }

  /**
   * 渲染差异头部
   */
  private renderDiffHeader(nodes: DiffNode[]): string {
    const stats = this.calculateStats(nodes);
    
    return `
      <div class="diff-stats">
        <div class="stat-item added">
          <span class="stat-icon">+</span>
          <span class="stat-count">${stats.added}</span>
          <span class="stat-label">Added</span>
        </div>
        <div class="stat-item removed">
          <span class="stat-icon">-</span>
          <span class="stat-count">${stats.removed}</span>
          <span class="stat-label">Removed</span>
        </div>
        <div class="stat-item modified">
          <span class="stat-icon">~</span>
          <span class="stat-count">${stats.modified}</span>
          <span class="stat-label">Modified</span>
        </div>
      </div>
      
      <div class="diff-controls">
        <button class="control-btn" data-action="toggle-changes">
          ${this.options.showOnlyChanges ? 'Show All' : 'Show Changes Only'}
        </button>
        <button class="control-btn" data-action="expand-all">
          Expand All
        </button>
        <button class="control-btn" data-action="collapse-all">
          Collapse All
        </button>
        <div class="export-controls">
          <button class="control-btn export-btn" data-action="export-text">
            📄 Export Text
          </button>
          <button class="control-btn export-btn" data-action="export-json-patch">
            📋 Export JSON Patch
          </button>
          <button class="control-btn export-btn" data-action="copy-summary">
            📋 Copy Summary
          </button>
        </div>
      </div>
    `;
  }

  /**
   * 渲染差异树
   */
  private renderDiffTree(nodes: DiffNode[]): string {
    if (nodes.length === 0) {
      return '<div class="no-diff">No differences found</div>';
    }

    return `
      <div class="diff-tree">
        ${nodes.map(node => this.renderDiffNode(node)).join('')}
      </div>
    `;
  }

  /**
   * 渲染差异节点
   */
  private renderDiffNode(node: DiffNode): string {
    const indent = '  '.repeat(node.level);
    const typeClass = `diff-${node.type}`;
    
    let content = '';
    
    if (node.children && node.children.length > 0) {
      // 容器节点
      const collapseButton = this.options.enableCollapse 
        ? `<span class="collapse-button" data-path="${node.path}">${node.collapsed ? '▶' : '▼'}</span>`
        : '';
      
      content = `
        <div class="diff-node ${typeClass}" data-path="${node.path}">
          <div class="node-header">
            ${collapseButton}
            <span class="node-key">${node.key}</span>
            <span class="node-type">${this.getNodeTypeDisplay(node)}</span>
          </div>
          <div class="node-children" ${node.collapsed ? 'style="display: none;"' : ''}>
            ${node.children.map(child => this.renderDiffNode(child)).join('')}
          </div>
        </div>
      `;
    } else {
      // 叶子节点
      content = `
        <div class="diff-node ${typeClass}" data-path="${node.path}">
          <div class="node-content">
            <span class="node-key">${node.key}:</span>
            ${this.renderNodeValue(node)}
          </div>
        </div>
      `;
    }
    
    return content;
  }

  /**
   * 渲染节点值
   */
  private renderNodeValue(node: DiffNode): string {
    switch (node.type) {
      case DiffType.ADDED:
        return `<span class="value-new">${this.formatValue(node.newValue)}</span>`;
      
      case DiffType.REMOVED:
        return `<span class="value-old">${this.formatValue(node.oldValue)}</span>`;
      
      case DiffType.MODIFIED:
        return `
          <span class="value-old">${this.formatValue(node.oldValue)}</span>
          <span class="value-arrow">→</span>
          <span class="value-new">${this.formatValue(node.newValue)}</span>
        `;
      
      case DiffType.UNCHANGED:
        return `<span class="value-unchanged">${this.formatValue(node.oldValue)}</span>`;
      
      default:
        return '';
    }
  }

  /**
   * 格式化值
   */
  private formatValue(value: any): string {
    if (value === null) return 'null';
    if (value === undefined) return 'undefined';
    if (typeof value === 'string') return `"${value}"`;
    if (typeof value === 'object') return Array.isArray(value) ? '[...]' : '{...}';
    return String(value);
  }

  /**
   * 获取节点类型显示
   */
  private getNodeTypeDisplay(node: DiffNode): string {
    const typeMap = {
      [DiffType.ADDED]: '+',
      [DiffType.REMOVED]: '-',
      [DiffType.MODIFIED]: '~',
      [DiffType.UNCHANGED]: ''
    };
    
    return typeMap[node.type] || '';
  }

  /**
   * 计算统计信息
   */
  private calculateStats(nodes: DiffNode[]): { added: number; removed: number; modified: number } {
    const stats = { added: 0, removed: 0, modified: 0 };
    
    const countNode = (node: DiffNode) => {
      switch (node.type) {
        case DiffType.ADDED:
          stats.added++;
          break;
        case DiffType.REMOVED:
          stats.removed++;
          break;
        case DiffType.MODIFIED:
          stats.modified++;
          break;
      }
      
      if (node.children) {
        node.children.forEach(countNode);
      }
    };
    
    nodes.forEach(countNode);
    return stats;
  }

  /**
   * 检查是否应该忽略字段
   */
  private shouldIgnoreField(fieldName: string): boolean {
    return this.options.ignoreFields?.includes(fieldName) || false;
  }

  /**
   * 从路径获取键名
   */
  private getKeyFromPath(path: string): string {
    if (!path) return '';
    const parts = path.split('.');
    return parts[parts.length - 1] || '';
  }

  /**
   * 渲染无差异情况
   */
  private renderNoDiff(result: DualExecutionResult): void {
    let message = '';
    
    if (!result.oldResponse && !result.newResponse) {
      message = 'Both requests failed - no data to compare';
    } else if (!result.oldResponse) {
      message = 'Old API request failed - cannot compare';
    } else if (!result.newResponse) {
      message = 'New API request failed - cannot compare';
    } else {
      message = 'Responses are identical';
    }
    
    this.container.innerHTML = `
      <div class="no-diff-container">
        <div class="no-diff-icon">🔍</div>
        <div class="no-diff-message">${message}</div>
      </div>
    `;
  }

  /**
   * 渲染错误
   */
  private renderError(error: string): void {
    this.container.innerHTML = `
      <div class="diff-error">
        <div class="error-icon">❌</div>
        <div class="error-message">
          <h4>Diff Calculation Failed</h4>
          <p>${error}</p>
        </div>
      </div>
    `;
  }

  /**
   * 绑定事件
   */
  private bindEvents(): void {
    this.container.addEventListener('click', (event) => {
      const target = event.target as HTMLElement;
      
      if (target.classList.contains('collapse-button')) {
        const path = target.getAttribute('data-path');
        if (path) {
          this.toggleCollapse(path);
        }
      }
      
      const action = target.getAttribute('data-action');
      if (action) {
        this.handleAction(action);
      }
    });
  }

  /**
   * 切换折叠状态
   */
  private toggleCollapse(path: string): void {
    const button = this.container.querySelector(`[data-path="${path}"] .collapse-button`) as HTMLElement;
    const children = this.container.querySelector(`[data-path="${path}"] .node-children`) as HTMLElement;
    
    if (button && children) {
      const isCollapsed = children.style.display === 'none';
      button.textContent = isCollapsed ? '▼' : '▶';
      children.style.display = isCollapsed ? '' : 'none';
    }
  }

  /**
   * 处理操作
   */
  private handleAction(action: string): void {
    switch (action) {
      case 'toggle-changes':
        this.options.showOnlyChanges = !this.options.showOnlyChanges;
        // 重新渲染需要外部触发
        break;
      case 'expand-all':
        this.expandAll();
        break;
      case 'collapse-all':
        this.collapseAll();
        break;
      case 'export-text':
        this.exportAsText();
        break;
      case 'export-json-patch':
        this.exportAsJsonPatch();
        break;
      case 'copy-summary':
        this.copySummary();
        break;
    }
  }

  /**
   * 展开所有节点
   */
  private expandAll(): void {
    const buttons = this.container.querySelectorAll('.collapse-button');
    const childrenElements = this.container.querySelectorAll('.node-children');
    
    buttons.forEach(button => {
      (button as HTMLElement).textContent = '▼';
    });
    
    childrenElements.forEach(children => {
      (children as HTMLElement).style.display = '';
    });
  }

  /**
   * 折叠所有节点
   */
  private collapseAll(): void {
    const buttons = this.container.querySelectorAll('.collapse-button');
    const childrenElements = this.container.querySelectorAll('.node-children');
    
    buttons.forEach(button => {
      (button as HTMLElement).textContent = '▶';
    });
    
    childrenElements.forEach(children => {
      (children as HTMLElement).style.display = 'none';
    });
  }

  /**
   * 注入样式
   */
  private injectStyles(): void {
    if (document.querySelector('#diff-renderer-styles')) return;

    const style = document.createElement('style');
    style.id = 'diff-renderer-styles';
    style.textContent = this.getDiffStyles();
    document.head.appendChild(style);
  }

  /**
   * 获取差异样式
   */
  private getDiffStyles(): string {
    return `
      .diff-container {
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 14px;
        line-height: 1.4;
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        overflow: hidden;
      }
      
      .diff-container.dark {
        background: #1e1e1e;
        border-color: #333;
        color: #d4d4d4;
      }
      
      .diff-header {
        background: #fff;
        border-bottom: 1px solid #e9ecef;
        padding: 16px;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      
      .diff-container.dark .diff-header {
        background: #2d2d2d;
        border-color: #333;
      }
      
      .diff-stats {
        display: flex;
        gap: 16px;
      }
      
      .stat-item {
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 12px;
      }
      
      .stat-item.added { color: #28a745; }
      .stat-item.removed { color: #dc3545; }
      .stat-item.modified { color: #ffc107; }
      
      .stat-icon {
        font-weight: bold;
        font-size: 14px;
      }
      
      .diff-controls {
        display: flex;
        gap: 8px;
        align-items: center;
      }

      .export-controls {
        display: flex;
        gap: 8px;
        margin-left: 16px;
        padding-left: 16px;
        border-left: 1px solid #ddd;
      }
      
      .control-btn {
        padding: 4px 8px;
        font-size: 12px;
        border: 1px solid #ddd;
        background: white;
        border-radius: 4px;
        cursor: pointer;
      }
      
      .control-btn:hover {
        background: #f8f9fa;
      }
      
      .diff-content {
        padding: 16px;
        max-height: 500px;
        overflow-y: auto;
      }
      
      .diff-tree {
        font-family: monospace;
      }
      
      .diff-node {
        margin: 2px 0;
      }
      
      .diff-added { background: #d4edda; border-left: 3px solid #28a745; }
      .diff-removed { background: #f8d7da; border-left: 3px solid #dc3545; }
      .diff-modified { background: #fff3cd; border-left: 3px solid #ffc107; }
      .diff-unchanged { background: transparent; }
      
      .diff-container.dark .diff-added { background: #1e3a1e; }
      .diff-container.dark .diff-removed { background: #3a1e1e; }
      .diff-container.dark .diff-modified { background: #3a3a1e; }
      
      .node-header,
      .node-content {
        padding: 4px 8px;
        display: flex;
        align-items: center;
        gap: 8px;
      }
      
      .collapse-button {
        cursor: pointer;
        color: #666;
        user-select: none;
        min-width: 12px;
      }
      
      .collapse-button:hover {
        color: #333;
      }
      
      .node-key {
        font-weight: 500;
        color: #0451a5;
      }
      
      .diff-container.dark .node-key {
        color: #9cdcfe;
      }
      
      .node-type {
        font-weight: bold;
        margin-left: auto;
      }
      
      .value-old {
        color: #dc3545;
        text-decoration: line-through;
      }
      
      .value-new {
        color: #28a745;
      }
      
      .value-arrow {
        color: #666;
        margin: 0 4px;
      }
      
      .value-unchanged {
        color: #666;
      }
      
      .node-children {
        margin-left: 20px;
      }
      
      .no-diff,
      .no-diff-container,
      .diff-error {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 48px 24px;
        text-align: center;
        color: #666;
      }
      
      .no-diff-icon,
      .error-icon {
        font-size: 48px;
        margin-bottom: 16px;
      }
      
      .no-diff-message,
      .error-message {
        font-size: 16px;
      }
      
      .error-message h4 {
        margin: 0 0 8px 0;
        color: #dc3545;
      }

      .diff-toast {
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 16px;
        border-radius: 4px;
        color: white;
        font-size: 14px;
        z-index: 10000;
        transform: translateX(100%);
        transition: transform 0.3s ease;
        box-shadow: 0 2px 8px rgba(0,0,0,0.15);
      }

      .diff-toast.show {
        transform: translateX(0);
      }

      .diff-toast-success {
        background: #28a745;
      }

      .diff-toast-error {
        background: #dc3545;
      }
    `;
  }

  /**
   * 导出为可读文本
   */
  private async exportAsText(): Promise<void> {
    try {
      const diffNodes = this.getCurrentDiffNodes();
      const textContent = this.generateTextReport(diffNodes);

      await this.copyToClipboard(textContent);
      this.showToast('Diff exported as text to clipboard');
    } catch (error) {
      this.showToast('Failed to export diff as text', 'error');
    }
  }

  /**
   * 导出为 JSON Patch 格式
   */
  private async exportAsJsonPatch(): Promise<void> {
    try {
      const diffNodes = this.getCurrentDiffNodes();
      const jsonPatch = this.generateJsonPatch(diffNodes);

      await this.copyToClipboard(JSON.stringify(jsonPatch, null, 2));
      this.showToast('Diff exported as JSON Patch to clipboard');
    } catch (error) {
      this.showToast('Failed to export diff as JSON Patch', 'error');
    }
  }

  /**
   * 复制摘要信息
   */
  private async copySummary(): Promise<void> {
    try {
      const diffNodes = this.getCurrentDiffNodes();
      const stats = this.calculateStats(diffNodes);
      const summary = this.generateSummaryText(stats);

      await this.copyToClipboard(summary);
      this.showToast('Diff summary copied to clipboard');
    } catch (error) {
      this.showToast('Failed to copy diff summary', 'error');
    }
  }

  /**
   * 获取当前差异节点
   */
  private getCurrentDiffNodes(): DiffNode[] {
    return this.currentDiffNodes;
  }

  /**
   * 生成文本报告
   */
  private generateTextReport(nodes: DiffNode[]): string {
    const lines: string[] = [];
    const stats = this.calculateStats(nodes);

    // 添加标题和统计信息
    lines.push('API Diff Report');
    lines.push('================');
    lines.push('');
    lines.push(`Summary: ${stats.added} added, ${stats.removed} removed, ${stats.modified} modified`);
    lines.push('');

    // 添加详细差异
    if (nodes.length > 0) {
      lines.push('Changes:');
      lines.push('--------');
      this.generateTextForNodes(nodes, lines, 0);
    } else {
      lines.push('No differences found.');
    }

    lines.push('');
    lines.push(`Generated at: ${new Date().toLocaleString()}`);

    return lines.join('\n');
  }

  /**
   * 为节点生成文本
   */
  private generateTextForNodes(nodes: DiffNode[], lines: string[], level: number): void {
    const indent = '  '.repeat(level);

    nodes.forEach(node => {
      if (node.type === DiffType.UNCHANGED && this.options.showOnlyChanges) {
        return; // 跳过未变更的节点
      }

      const typeSymbol = this.getTypeSymbol(node.type);
      const path = node.path || node.key;

      switch (node.type) {
        case DiffType.ADDED:
          lines.push(`${indent}${typeSymbol} ${path}: ${this.formatValue(node.newValue)}`);
          break;
        case DiffType.REMOVED:
          lines.push(`${indent}${typeSymbol} ${path}: ${this.formatValue(node.oldValue)}`);
          break;
        case DiffType.MODIFIED:
          lines.push(`${indent}${typeSymbol} ${path}: ${this.formatValue(node.oldValue)} → ${this.formatValue(node.newValue)}`);
          break;
        case DiffType.UNCHANGED:
          lines.push(`${indent}  ${path}: ${this.formatValue(node.oldValue)}`);
          break;
      }

      if (node.children && node.children.length > 0) {
        this.generateTextForNodes(node.children, lines, level + 1);
      }
    });
  }

  /**
   * 生成 JSON Patch
   */
  private generateJsonPatch(nodes: DiffNode[]): any[] {
    const patches: any[] = [];

    const generatePatchForNodes = (nodeList: DiffNode[]) => {
      nodeList.forEach(node => {
        const path = this.convertToJsonPointer(node.path);

        switch (node.type) {
          case DiffType.ADDED:
            patches.push({
              op: 'add',
              path: path,
              value: node.newValue
            });
            break;
          case DiffType.REMOVED:
            patches.push({
              op: 'remove',
              path: path
            });
            break;
          case DiffType.MODIFIED:
            patches.push({
              op: 'replace',
              path: path,
              value: node.newValue
            });
            break;
        }

        if (node.children && node.children.length > 0) {
          generatePatchForNodes(node.children);
        }
      });
    };

    generatePatchForNodes(nodes);
    return patches;
  }

  /**
   * 生成摘要文本
   */
  private generateSummaryText(stats: { added: number; removed: number; modified: number }): string {
    const total = stats.added + stats.removed + stats.modified;

    if (total === 0) {
      return 'No differences found between the API responses.';
    }

    const parts: string[] = [];

    if (stats.added > 0) {
      parts.push(`${stats.added} field${stats.added > 1 ? 's' : ''} added`);
    }

    if (stats.removed > 0) {
      parts.push(`${stats.removed} field${stats.removed > 1 ? 's' : ''} removed`);
    }

    if (stats.modified > 0) {
      parts.push(`${stats.modified} field${stats.modified > 1 ? 's' : ''} modified`);
    }

    return `API Diff Summary: ${parts.join(', ')} (${total} total changes)`;
  }

  /**
   * 获取类型符号
   */
  private getTypeSymbol(type: DiffType): string {
    switch (type) {
      case DiffType.ADDED: return '+';
      case DiffType.REMOVED: return '-';
      case DiffType.MODIFIED: return '~';
      case DiffType.UNCHANGED: return ' ';
      default: return '?';
    }
  }

  /**
   * 转换为 JSON Pointer 格式
   */
  private convertToJsonPointer(path: string): string {
    if (!path) return '';

    // 将路径转换为 JSON Pointer 格式 (RFC 6901)
    return '/' + path
      .replace(/\./g, '/')
      .replace(/\[(\d+)\]/g, '/$1')
      .replace(/~/g, '~0')
      .replace(/\//g, '~1');
  }

  /**
   * 复制到剪贴板
   */
  private async copyToClipboard(text: string): Promise<void> {
    if (navigator.clipboard && navigator.clipboard.writeText) {
      await navigator.clipboard.writeText(text);
    } else {
      // 降级方案
      const textArea = document.createElement('textarea');
      textArea.value = text;
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
    }
  }

  /**
   * 显示提示消息
   */
  private showToast(message: string, type: 'success' | 'error' = 'success'): void {
    const toast = document.createElement('div');
    toast.className = `diff-toast diff-toast-${type}`;
    toast.textContent = message;

    document.body.appendChild(toast);

    setTimeout(() => {
      toast.classList.add('show');
    }, 10);

    setTimeout(() => {
      toast.classList.remove('show');
      setTimeout(() => {
        if (toast.parentNode) {
          toast.parentNode.removeChild(toast);
        }
      }, 300);
    }, 3000);
  }

  /**
   * 更新选项
   */
  public updateOptions(newOptions: Partial<DiffOptions>): void {
    this.options = { ...this.options, ...newOptions };
  }
}
