/**
 * Request Builder Component
 * 请求构建器组件，提供 Postman 风格的请求配置界面
 */

import type {
  RequestConfig,
  HttpMethod,
  BodyType,
  AuthType,
  CONSTANTS
} from '../types/api-diff-types';
import { ValidationUtils } from '../utils/validation-utils';
import { CurlParser } from './curl-parser';
import { CurlParsingUtils } from '../utils/curl-parsing-utils';
import { UIUtils } from '../../../utils/ui-components';

export class RequestBuilder {
  private container: HTMLElement;
  private currentConfig: RequestConfig;
  private activeTab: string = 'headers';
  
  // DOM 元素引用
  private methodSelect: HTMLSelectElement | null = null;
  private oldUrlInput: HTMLInputElement | null = null;
  private newUrlInput: HTMLInputElement | null = null;
  private tabButtons: Map<string, HTMLButtonElement> = new Map();
  private tabContents: Map<string, HTMLElement> = new Map();

  // 子组件
  private curlParser: CurlParser | null = null;
  
  // 事件回调
  private onConfigChange?: (config: RequestConfig) => void;
  private onValidationChange?: (isValid: boolean, errors: Record<string, string>) => void;

  constructor(container: HTMLElement) {
    this.container = container;
    this.currentConfig = this.getDefaultConfig();
    this.init();
  }

  /**
   * 初始化组件
   */
  private init(): void {
    console.log('🔧 RequestBuilder 开始初始化...');
    try {
      this.render();
      console.log('✅ RequestBuilder 渲染完成');

      this.bindEvents();
      console.log('✅ RequestBuilder 事件绑定完成');

      this.switchTab('headers'); // 默认显示 Headers tab
      console.log('✅ RequestBuilder 初始化完成');
    } catch (error) {
      console.error('❌ RequestBuilder 初始化失败:', error);
      throw error;
    }
  }

  /**
   * 渲染组件界面
   */
  private render(): void {
    this.container.innerHTML = `
      <div class="request-builder">
        <!-- 顶部行：Method、URLs、Actions -->
        <div class="request-top-row">
          <div class="method-selector">
            <label class="form-label">HTTP Method</label>
            <select id="method-select" class="form-control">
              ${this.renderMethodOptions()}
            </select>
          </div>

          <div class="url-input-group">
            <div class="url-input">
              <label class="form-label">Old API URL</label>
              <input
                id="old-url-input"
                type="text"
                class="form-control"
                placeholder="https://api-old.example.com/endpoint"
                value="${this.currentConfig.oldUrl}"
                autocomplete="url"
                spellcheck="false"
              />
            </div>
            <div class="url-input">
              <label class="form-label">New API URL</label>
              <input
                id="new-url-input"
                type="text"
                class="form-control"
                placeholder="https://api-new.example.com/endpoint"
                value="${this.currentConfig.newUrl}"
                autocomplete="url"
                spellcheck="false"
              />
            </div>
          </div>

          <div class="action-buttons">
            <button id="compare-btn" class="compare-btn" disabled aria-label="开始对比API响应">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M1 4v6h6"/>
                <path d="M23 20v-6h-6"/>
                <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15"/>
              </svg>
              对比
            </button>
            <button id="settings-btn" class="btn btn-secondary" aria-label="设置">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="12" cy="12" r="3"/>
                <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1"/>
              </svg>
            </button>
          </div>
        </div>

        <!-- Tab 导航 -->
        <div class="request-tabs" role="tablist" aria-label="请求配置选项">
          <button
            class="request-tab"
            data-tab="headers"
            role="tab"
            aria-selected="true"
            aria-controls="tab-headers"
            id="tab-button-headers"
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
              <polyline points="14,2 14,8 20,8"/>
              <line x1="16" y1="13" x2="8" y2="13"/>
              <line x1="16" y1="17" x2="8" y2="17"/>
              <polyline points="10,9 9,9 8,9"/>
            </svg>
            Headers
          </button>
          <button
            class="request-tab"
            data-tab="query"
            role="tab"
            aria-selected="false"
            aria-controls="tab-query"
            id="tab-button-query"
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="11" cy="11" r="8"/>
              <path d="M21 21l-4.35-4.35"/>
            </svg>
            Query
          </button>
          <button
            class="request-tab"
            data-tab="body"
            role="tab"
            aria-selected="false"
            aria-controls="tab-body"
            id="tab-button-body"
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
              <polyline points="14,2 14,8 20,8"/>
              <line x1="12" y1="18" x2="12" y2="12"/>
              <line x1="9" y1="15" x2="15" y2="15"/>
            </svg>
            Body
          </button>
          <button
            class="request-tab"
            data-tab="auth"
            role="tab"
            aria-selected="false"
            aria-controls="tab-auth"
            id="tab-button-auth"
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <rect x="3" y="11" width="18" height="11" rx="2" ry="2"/>
              <circle cx="12" cy="16" r="1"/>
              <path d="M7 11V7a5 5 0 0 1 10 0v4"/>
            </svg>
            Auth
          </button>
          <button
            class="request-tab"
            data-tab="curl"
            role="tab"
            aria-selected="false"
            aria-controls="tab-curl"
            id="tab-button-curl"
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <polyline points="4,17 10,11 4,5"/>
              <line x1="12" y1="19" x2="20" y2="19"/>
            </svg>
            cURL Import
          </button>
        </div>

        <!-- Tab 内容区域 -->
        <div class="request-tab-content">
          <div
            id="tab-headers"
            class="tab-pane active"
            role="tabpanel"
            aria-labelledby="tab-button-headers"
            tabindex="0"
          >
            ${this.renderHeadersTab()}
          </div>
          <div
            id="tab-query"
            class="tab-pane"
            role="tabpanel"
            aria-labelledby="tab-button-query"
            tabindex="0"
            hidden
          >
            ${this.renderQueryTab()}
          </div>
          <div
            id="tab-body"
            class="tab-pane"
            role="tabpanel"
            aria-labelledby="tab-button-body"
            tabindex="0"
            hidden
          >
            ${this.renderBodyTab()}
          </div>
          <div
            id="tab-auth"
            class="tab-pane"
            role="tabpanel"
            aria-labelledby="tab-button-auth"
            tabindex="0"
            hidden
          >
            ${this.renderAuthTab()}
          </div>
          <div
            id="tab-curl"
            class="tab-pane"
            role="tabpanel"
            aria-labelledby="tab-button-curl"
            tabindex="0"
            hidden
          >
            ${this.renderCurlTab()}
          </div>
        </div>
      </div>
    `;

    // 获取 DOM 元素引用
    this.methodSelect = this.container.querySelector('#method-select');
    this.oldUrlInput = this.container.querySelector('#old-url-input');
    this.newUrlInput = this.container.querySelector('#new-url-input');
    
    // 获取 Tab 按钮和内容引用
    const tabButtons = this.container.querySelectorAll('.request-tab');
    const tabPanes = this.container.querySelectorAll('.tab-pane');
    
    tabButtons.forEach(button => {
      const tabName = button.getAttribute('data-tab');
      if (tabName) {
        this.tabButtons.set(tabName, button as HTMLButtonElement);
      }
    });
    
    tabPanes.forEach(pane => {
      const tabName = pane.id.replace('tab-', '');
      this.tabContents.set(tabName, pane as HTMLElement);
    });
  }

  /**
   * 绑定事件监听器
   */
  private bindEvents(): void {
    // Method 选择器变化
    this.methodSelect?.addEventListener('change', () => {
      this.currentConfig.method = this.methodSelect!.value as HttpMethod;
      this.validateAndNotify();
    });

    // URL 输入框变化
    this.oldUrlInput?.addEventListener('input', () => {
      this.currentConfig.oldUrl = this.oldUrlInput!.value;
      this.validateField('oldUrl', this.oldUrlInput!);
      this.validateAndNotify();
    });

    this.newUrlInput?.addEventListener('input', () => {
      this.currentConfig.newUrl = this.newUrlInput!.value;
      this.validateField('newUrl', this.newUrlInput!);
      this.validateAndNotify();
    });

    // Tab 切换 - 鼠标点击
    this.tabButtons.forEach((button, tabName) => {
      button.addEventListener('click', () => {
        this.switchTab(tabName);
      });

      // 键盘导航支持
      button.addEventListener('keydown', (e) => {
        this.handleTabKeyNavigation(e, tabName);
      });
    });

    // 对比按钮
    const compareBtn = this.container.querySelector('#compare-btn');
    compareBtn?.addEventListener('click', () => {
      this.handleCompareClick();
    });

    // 绑定各 Tab 的事件
    this.bindTabEvents();
  }

  /**
   * 绑定各 Tab 的事件
   */
  private bindTabEvents(): void {
    // Headers Tab 事件
    this.bindKeyValueEvents('headers');

    // Query Tab 事件
    this.bindKeyValueEvents('query');

    // Body Tab 事件
    this.bindBodyEvents();

    // Auth Tab 事件
    this.bindAuthEvents();

    // cURL Tab 事件
    this.bindCurlEvents();
  }

  /**
   * 绑定键值对编辑器事件
   */
  private bindKeyValueEvents(type: 'headers' | 'query'): void {
    const containerSelector = type === 'headers' ? '#headers-container' : '#query-params-container';
    const container = this.container.querySelector(containerSelector);
    if (!container) return;

    // 使用事件委托处理动态添加的行
    container.addEventListener('input', (e) => {
      const target = e.target as HTMLInputElement;
      if (target.classList.contains('key-input') || target.classList.contains('value-input')) {
        this.updateKeyValueData(type);
        this.validateField(type === 'headers' ? 'headers' : 'queryParams', container);
      }
    });

    container.addEventListener('click', (e) => {
      const target = e.target as HTMLElement;
      if (target.classList.contains('remove-row-btn') || target.closest('.remove-row-btn')) {
        const row = target.closest('.key-value-row');
        if (row && container.children.length > 1) {
          row.remove();
          this.updateKeyValueData(type);
          this.validateField(type === 'headers' ? 'headers' : 'queryParams', container);
        }
      }
    });

    // 添加行按钮
    const addBtn = this.container.querySelector(`#tab-${type} .add-row-btn`);
    addBtn?.addEventListener('click', () => {
      this.addKeyValueRow(type);
    });
  }

  /**
   * 绑定 Body Tab 事件
   */
  private bindBodyEvents(): void {
    const bodyTypeSelect = this.container.querySelector('#body-type-select');
    bodyTypeSelect?.addEventListener('change', () => {
      this.currentConfig.body.type = (bodyTypeSelect as HTMLSelectElement).value as BodyType;
      this.updateBodyContent();
      this.validateAndNotify();
    });

    const bodyTextarea = this.container.querySelector('#body-textarea');
    bodyTextarea?.addEventListener('input', () => {
      this.currentConfig.body.content = (bodyTextarea as HTMLTextAreaElement).value;
      this.validateField('json', bodyTextarea as HTMLTextAreaElement);
      this.validateAndNotify();
    });
  }

  /**
   * 绑定 Auth Tab 事件
   */
  private bindAuthEvents(): void {
    const authTypeSelect = this.container.querySelector('#auth-type-select');
    authTypeSelect?.addEventListener('change', () => {
      const authType = (authTypeSelect as HTMLSelectElement).value;
      if (authType === 'none') {
        this.currentConfig.auth = undefined;
      } else {
        this.currentConfig.auth = {
          type: authType as AuthType,
          credentials: {}
        };
      }
      this.updateAuthContent();
      this.validateAndNotify();
    });
  }

  /**
   * 绑定 cURL Tab 事件
   */
  private bindCurlEvents(): void {
    const curlTextarea = this.container.querySelector('#curl-input') as HTMLTextAreaElement;
    const parseButton = this.container.querySelector('#parse-curl-btn') as HTMLButtonElement;
    const clearButton = this.container.querySelector('#clear-curl-btn') as HTMLButtonElement;
    const exampleButton = this.container.querySelector('#example-btn') as HTMLButtonElement;
    const resultContainer = this.container.querySelector('#curl-result') as HTMLElement;
    const highlightLayer = this.container.querySelector('#curl-highlight') as HTMLElement;

    if (!curlTextarea || !parseButton || !clearButton || !resultContainer) return;

    // 初始化语法高亮
    this.initializeCurlSyntaxHighlight(curlTextarea, highlightLayer);

    // 输入事件
    curlTextarea.addEventListener('input', () => {
      this.updateCurlSyntaxHighlight(curlTextarea, highlightLayer);
      this.updateCurlParseButtonState(curlTextarea, parseButton);
    });

    // 滚动同步
    curlTextarea.addEventListener('scroll', () => {
      if (highlightLayer) {
        highlightLayer.scrollTop = curlTextarea.scrollTop;
        highlightLayer.scrollLeft = curlTextarea.scrollLeft;
      }
    });

    // 解析按钮
    parseButton.addEventListener('click', () => {
      this.handleCurlParse(curlTextarea.value, resultContainer);
    });

    // 清空按钮
    clearButton.addEventListener('click', () => {
      curlTextarea.value = '';
      resultContainer.innerHTML = '';
      this.updateCurlSyntaxHighlight(curlTextarea, highlightLayer);
      this.updateCurlParseButtonState(curlTextarea, parseButton);
    });

    // 示例按钮
    exampleButton?.addEventListener('click', () => {
      this.showCurlExamples(curlTextarea, highlightLayer, parseButton);
    });
  }

  /**
   * 渲染 HTTP 方法选项
   */
  private renderMethodOptions(): string {
    const methods: HttpMethod[] = ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'HEAD', 'OPTIONS'];
    return methods.map(method => 
      `<option value="${method}" ${method === this.currentConfig.method ? 'selected' : ''}>${method}</option>`
    ).join('');
  }

  /**
   * 渲染 Headers Tab
   */
  private renderHeadersTab(): string {
    return `
      <div class="key-value-editor" id="headers-editor">
        <div class="editor-header">
          <h4>Request Headers</h4>
          <p class="text-secondary">设置请求头信息</p>
        </div>
        <div class="key-value-rows" id="headers-rows">
          ${this.renderKeyValueRows(this.currentConfig.headers)}
        </div>
        <button class="add-row-btn" onclick="this.addHeaderRow()">
          ➕ Add Header
        </button>
      </div>
    `;
  }

  /**
   * 渲染 Query Tab
   */
  private renderQueryTab(): string {
    return `
      <div class="key-value-editor" id="query-editor">
        <div class="editor-header">
          <h4>Query Parameters</h4>
          <p class="text-secondary">设置 URL 查询参数</p>
        </div>
        <div class="key-value-rows" id="query-rows">
          ${this.renderKeyValueRows(this.currentConfig.queryParams)}
        </div>
        <button class="add-row-btn" onclick="this.addQueryRow()">
          ➕ Add Parameter
        </button>
      </div>
    `;
  }

  /**
   * 渲染 Body Tab
   */
  private renderBodyTab(): string {
    return `
      <div class="body-editor" id="body-editor">
        <div class="editor-header">
          <h4>Request Body</h4>
          <div class="body-type-selector">
            <select id="body-type-select" class="form-control">
              <option value="raw" ${this.currentConfig.body.type === 'raw' ? 'selected' : ''}>Raw</option>
              <option value="json" ${this.currentConfig.body.type === 'json' ? 'selected' : ''}>JSON</option>
              <option value="form-data" ${this.currentConfig.body.type === 'form-data' ? 'selected' : ''}>Form Data</option>
              <option value="x-www-form-urlencoded" ${this.currentConfig.body.type === 'x-www-form-urlencoded' ? 'selected' : ''}>URL Encoded</option>
            </select>
          </div>
        </div>
        
        <div class="body-content" id="body-content">
          ${this.renderBodyContent()}
        </div>
      </div>
    `;
  }

  /**
   * 渲染 Auth Tab
   */
  private renderAuthTab(): string {
    const authType = this.currentConfig.auth?.type || 'none';
    return `
      <div class="auth-editor" id="auth-editor">
        <div class="editor-header">
          <h4>Authentication</h4>
          <div class="auth-type-selector">
            <select id="auth-type-select" class="form-control">
              <option value="none" ${authType === 'none' ? 'selected' : ''}>No Auth</option>
              <option value="basic" ${authType === 'basic' ? 'selected' : ''}>Basic Auth</option>
              <option value="bearer" ${authType === 'bearer' ? 'selected' : ''}>Bearer Token</option>
              <option value="custom" ${authType === 'custom' ? 'selected' : ''}>Custom Header</option>
            </select>
          </div>
        </div>
        
        <div class="auth-content" id="auth-content">
          ${this.renderAuthContent()}
        </div>
      </div>
    `;
  }

  /**
   * 渲染 cURL Tab
   */
  private renderCurlTab(): string {
    return `
      <div class="curl-parser">
        <div class="curl-input-section">
          <div class="input-header">
            <h4>Import from cURL</h4>
            <p class="text-secondary">粘贴 cURL 命令以自动填充请求配置</p>
          </div>

          <div class="input-area">
            <div class="curl-editor-container">
              <textarea
                id="curl-input"
                class="curl-textarea"
                placeholder="curl -X POST https://api.example.com/users \\
  -H 'Content-Type: application/json' \\
  -H 'Authorization: Bearer token123' \\
  -d '{\"name\":\"John\",\"email\":\"<EMAIL>\"}'"
                rows="8"
                spellcheck="false"
                autocomplete="off"
                autocorrect="off"
                autocapitalize="off"
              ></textarea>
              <div id="curl-highlight" class="curl-highlight" aria-hidden="true"></div>
            </div>
          </div>

          <div class="input-actions">
            <button id="parse-curl-btn" class="btn btn-primary" disabled>
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                <polyline points="7,10 12,15 17,10"/>
                <line x1="12" y1="15" x2="12" y2="3"/>
              </svg>
              Parse cURL
            </button>
            <button id="clear-curl-btn" class="btn btn-secondary">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polyline points="3,6 5,6 21,6"/>
                <path d="M19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"/>
                <line x1="10" y1="11" x2="10" y2="17"/>
                <line x1="14" y1="11" x2="14" y2="17"/>
              </svg>
              Clear
            </button>
            <button id="example-btn" class="btn btn-outline">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                <polyline points="14,2 14,8 20,8"/>
                <line x1="16" y1="13" x2="8" y2="13"/>
                <line x1="16" y1="17" x2="8" y2="17"/>
                <polyline points="10,9 9,9 8,9"/>
              </svg>
              Examples
            </button>
          </div>
        </div>

        <div class="curl-result-section">
          <div id="curl-result" class="curl-result">
            <!-- 解析结果将在这里显示 -->
          </div>
        </div>
      </div>
    `;
  }

  /**
   * 渲染键值对行
   */
  private renderKeyValueRows(data: Record<string, string>): string {
    const rows = Object.entries(data).map(([key, value]) => 
      this.renderKeyValueRow(key, value)
    ).join('');
    
    // 添加一个空行用于新增
    return rows + this.renderKeyValueRow('', '');
  }

  /**
   * 渲染单个键值对行
   */
  private renderKeyValueRow(key: string = '', value: string = ''): string {
    return `
      <div class="key-value-row">
        <input 
          type="text" 
          class="form-control key-value-input" 
          placeholder="Key" 
          value="${key}"
          data-type="key"
        />
        <input 
          type="text" 
          class="form-control key-value-input" 
          placeholder="Value" 
          value="${value}"
          data-type="value"
        />
        <button class="remove-btn" title="Remove">
          🗑️
        </button>
      </div>
    `;
  }

  /**
   * 渲染 Body 内容
   */
  private renderBodyContent(): string {
    const bodyType = this.currentConfig.body.type;
    
    if (bodyType === 'raw' || bodyType === 'json') {
      return `
        <div class="json-editor">
          <textarea 
            id="body-textarea" 
            class="json-textarea" 
            placeholder="${bodyType === 'json' ? 'Enter JSON data...' : 'Enter raw data...'}"
            rows="8"
          >${this.currentConfig.body.content}</textarea>
          <div id="body-error" class="json-error" style="display: none;"></div>
        </div>
      `;
    } else {
      return `
        <div class="key-value-editor" id="body-form-editor">
          <div class="key-value-rows" id="body-form-rows">
            ${this.renderFormDataRows()}
          </div>
          <button class="add-row-btn" onclick="this.addBodyFormRow()">
            ➕ Add Field
          </button>
        </div>
      `;
    }
  }

  /**
   * 渲染 Auth 内容
   */
  private renderAuthContent(): string {
    const authType = this.currentConfig.auth?.type || 'none';
    
    if (authType === 'none') {
      return '<p class="text-secondary">No authentication required.</p>';
    }
    
    const credentials = this.currentConfig.auth?.credentials || {};
    
    switch (authType) {
      case 'basic':
        return `
          <div class="auth-basic">
            <div class="form-group">
              <label class="form-label">Username</label>
              <input 
                type="text" 
                class="form-control" 
                id="auth-username"
                value="${credentials.username || ''}"
                placeholder="Enter username"
              />
            </div>
            <div class="form-group">
              <label class="form-label">Password</label>
              <input 
                type="password" 
                class="form-control" 
                id="auth-password"
                value="${credentials.password || ''}"
                placeholder="Enter password"
              />
            </div>
          </div>
        `;
      
      case 'bearer':
        return `
          <div class="auth-bearer">
            <div class="form-group">
              <label class="form-label">Token</label>
              <input 
                type="text" 
                class="form-control" 
                id="auth-token"
                value="${credentials.token || ''}"
                placeholder="Enter bearer token"
              />
            </div>
          </div>
        `;
      
      case 'custom':
        return `
          <div class="auth-custom">
            <div class="form-group">
              <label class="form-label">Header Name</label>
              <input 
                type="text" 
                class="form-control" 
                id="auth-header-name"
                value="${credentials.headerName || ''}"
                placeholder="e.g., X-API-Key"
              />
            </div>
            <div class="form-group">
              <label class="form-label">Header Value</label>
              <input 
                type="text" 
                class="form-control" 
                id="auth-header-value"
                value="${credentials.headerValue || ''}"
                placeholder="Enter header value"
              />
            </div>
          </div>
        `;
      
      default:
        return '<p class="text-secondary">Unknown authentication type.</p>';
    }
  }

  /**
   * 渲染表单数据行
   */
  private renderFormDataRows(): string {
    // 这里需要解析 body.content 中的表单数据
    // 暂时返回空行
    return this.renderKeyValueRow('', '');
  }

  /**
   * 切换 Tab
   */
  private switchTab(tabName: string): void {
    // 更新按钮状态和可访问性属性
    this.tabButtons.forEach((button, name) => {
      const isActive = name === tabName;

      if (isActive) {
        button.classList.add('active');
        button.setAttribute('aria-selected', 'true');
        button.setAttribute('tabindex', '0');
      } else {
        button.classList.remove('active');
        button.setAttribute('aria-selected', 'false');
        button.setAttribute('tabindex', '-1');
      }
    });

    // 更新内容显示和可访问性属性
    this.tabContents.forEach((content, name) => {
      const isActive = name === tabName;

      if (isActive) {
        content.classList.add('active');
        content.removeAttribute('hidden');
        content.setAttribute('tabindex', '0');
        // 触发重新渲染动画
        content.style.animation = 'none';
        content.offsetHeight; // 强制重排
        content.style.animation = '';
      } else {
        content.classList.remove('active');
        content.setAttribute('hidden', '');
        content.setAttribute('tabindex', '-1');
      }
    });

    this.activeTab = tabName;

    // 触发自定义事件
    this.container.dispatchEvent(new CustomEvent('tab-changed', {
      detail: { activeTab: tabName },
      bubbles: true
    }));
  }

  /**
   * 处理Tab键盘导航
   */
  private handleTabKeyNavigation(event: KeyboardEvent, currentTab: string): void {
    const tabNames = Array.from(this.tabButtons.keys());
    const currentIndex = tabNames.indexOf(currentTab);
    let targetIndex = currentIndex;

    switch (event.key) {
      case 'ArrowLeft':
      case 'ArrowUp':
        event.preventDefault();
        targetIndex = currentIndex > 0 ? currentIndex - 1 : tabNames.length - 1;
        break;

      case 'ArrowRight':
      case 'ArrowDown':
        event.preventDefault();
        targetIndex = currentIndex < tabNames.length - 1 ? currentIndex + 1 : 0;
        break;

      case 'Home':
        event.preventDefault();
        targetIndex = 0;
        break;

      case 'End':
        event.preventDefault();
        targetIndex = tabNames.length - 1;
        break;

      case 'Enter':
      case ' ':
        event.preventDefault();
        this.switchTab(currentTab);
        return;

      default:
        return;
    }

    const targetTab = tabNames[targetIndex];
    const targetButton = this.tabButtons.get(targetTab);
    if (targetButton) {
      targetButton.focus();
      this.switchTab(targetTab);
    }
  }

  /**
   * 验证配置并通知
   */
  private validateAndNotify(): void {
    const validation = this.validate();
    
    // 更新对比按钮状态
    const compareBtn = this.container.querySelector('#compare-btn') as HTMLButtonElement;
    if (compareBtn) {
      compareBtn.disabled = !validation.isValid;
    }

    // 通知配置变化
    this.onConfigChange?.(this.currentConfig);
    this.onValidationChange?.(validation.isValid, validation.errors);
  }

  /**
   * 验证当前配置
   */
  private validate(): ValidationResult {
    return ValidationUtils.validateRequestConfig(this.currentConfig);
  }

  /**
   * 验证单个字段并显示错误
   */
  private validateField(fieldName: string, element: HTMLElement): void {
    let value: any;
    let error: string | null = null;

    // 获取字段值
    switch (fieldName) {
      case 'oldUrl':
      case 'newUrl':
        value = (element as HTMLInputElement).value;
        error = ValidationUtils.validateField(fieldName, value);
        break;
      case 'json':
        value = (element as HTMLTextAreaElement).value;
        error = ValidationUtils.validateField('json', value);
        break;
      case 'headers':
        // 这里需要收集所有 headers 数据
        value = this.collectKeyValueData('headers');
        error = ValidationUtils.validateField('headers', value);
        break;
      case 'queryParams':
        // 这里需要收集所有 query 数据
        value = this.collectKeyValueData('query');
        error = ValidationUtils.validateField('queryParams', value);
        break;
    }

    // 更新元素的验证状态
    this.updateFieldValidationState(element, error);
  }

  /**
   * 更新字段的验证状态
   */
  private updateFieldValidationState(element: HTMLElement, error: string | null): void {
    // 移除之前的验证类
    element.classList.remove('is-valid', 'is-invalid');

    // 添加新的验证类
    if (error) {
      element.classList.add('is-invalid');
      this.showFieldError(element, error);
    } else {
      element.classList.add('is-valid');
      this.hideFieldError(element);
    }
  }

  /**
   * 显示字段错误
   */
  private showFieldError(element: HTMLElement, error: string): void {
    // 查找或创建错误提示元素
    let errorElement = element.parentElement?.querySelector('.invalid-feedback') as HTMLElement;

    if (!errorElement) {
      errorElement = document.createElement('div');
      errorElement.className = 'invalid-feedback';
      element.parentElement?.appendChild(errorElement);
    }

    errorElement.textContent = error;
    errorElement.style.display = 'block';
  }

  /**
   * 隐藏字段错误
   */
  private hideFieldError(element: HTMLElement): void {
    const errorElement = element.parentElement?.querySelector('.invalid-feedback') as HTMLElement;
    if (errorElement) {
      errorElement.style.display = 'none';
    }
  }

  /**
   * 收集键值对数据
   */
  private collectKeyValueData(type: 'headers' | 'query'): Record<string, string> {
    const containerSelector = type === 'headers' ? '#headers-container' : '#query-params-container';
    const container = this.container.querySelector(containerSelector);
    const data: Record<string, string> = {};

    if (container) {
      const rows = container.querySelectorAll('.key-value-row');
      rows.forEach(row => {
        const keyInput = row.querySelector('.key-input') as HTMLInputElement;
        const valueInput = row.querySelector('.value-input') as HTMLInputElement;

        if (keyInput && valueInput && keyInput.value.trim()) {
          data[keyInput.value.trim()] = valueInput.value;
        }
      });
    }

    return data;
  }

  /**
   * 更新键值对数据
   */
  private updateKeyValueData(type: 'headers' | 'query'): void {
    const data = this.collectKeyValueData(type);

    if (type === 'headers') {
      this.currentConfig.headers = data;
    } else {
      this.currentConfig.queryParams = data;
    }
  }

  /**
   * 添加键值对行
   */
  private addKeyValueRow(type: 'headers' | 'query'): void {
    const containerSelector = type === 'headers' ? '#headers-container' : '#query-params-container';
    const container = this.container.querySelector(containerSelector);
    if (container) {
      const newRow = this.createKeyValueRow('', '');
      container.appendChild(newRow);
    }
  }

  /**
   * 更新 Body 内容
   */
  private updateBodyContent(): void {
    const bodyContent = this.container.querySelector('#body-content');
    if (bodyContent) {
      bodyContent.innerHTML = this.renderBodyContent();
      this.bindBodyEvents(); // 重新绑定事件
    }
  }

  /**
   * 更新 Auth 内容
   */
  private updateAuthContent(): void {
    const authContent = this.container.querySelector('#auth-content');
    if (authContent) {
      authContent.innerHTML = this.renderAuthContent();
      this.bindAuthEvents(); // 重新绑定事件
    }
  }

  /**
   * 处理对比按钮点击
   */
  private handleCompareClick(): void {
    const validation = this.validate();
    if (validation.isValid) {
      // 触发对比事件
      document.dispatchEvent(new CustomEvent('request-compare', {
        detail: this.currentConfig
      }));
    } else {
      // 显示验证错误
      console.warn('Configuration validation failed:', validation.errors);
    }
  }

  /**
   * 处理 cURL 配置解析完成
   */
  private handleCurlConfigParsed(parsedConfig: RequestConfig): void {
    // 合并解析的配置到当前配置
    this.mergeConfig(parsedConfig);

    // 更新界面表单字段以反映新配置
    this.updateFormFieldsFromConfig();

    // 切换到 Headers tab 以显示导入的配置
    this.switchTab('headers');

    // 验证并通知配置变化
    this.validateAndNotify();

    // 显示成功提示
    this.showImportSuccessNotification();
  }

  /**
   * 合并配置
   */
  private mergeConfig(parsedConfig: Partial<RequestConfig>): void {
    // 更新基本配置
    if (parsedConfig.method) {
      this.currentConfig.method = parsedConfig.method;
    }

    if (parsedConfig.oldUrl) {
      this.currentConfig.oldUrl = parsedConfig.oldUrl;
      // 如果新 URL 为空，也设置为相同的 URL
      if (!this.currentConfig.newUrl) {
        this.currentConfig.newUrl = parsedConfig.oldUrl;
      }
    }

    // 合并请求头
    if (parsedConfig.headers) {
      this.currentConfig.headers = {
        ...this.currentConfig.headers,
        ...parsedConfig.headers
      };
    }

    // 合并查询参数
    if (parsedConfig.queryParams) {
      this.currentConfig.queryParams = {
        ...this.currentConfig.queryParams,
        ...parsedConfig.queryParams
      };
    }

    // 更新请求体
    if (parsedConfig.body) {
      this.currentConfig.body = {
        ...this.currentConfig.body,
        ...parsedConfig.body
      };
    }

    // 更新认证信息
    if (parsedConfig.auth) {
      this.currentConfig.auth = parsedConfig.auth;
    }
  }

  /**
   * 从配置更新表单字段
   */
  private updateFormFieldsFromConfig(): void {
    // 更新HTTP方法选择器
    const methodSelect = this.container.querySelector('#method-select') as HTMLSelectElement;
    if (methodSelect && this.currentConfig.method) {
      methodSelect.value = this.currentConfig.method;
    }

    // 更新URL输入框
    const oldUrlInput = this.container.querySelector('#old-url') as HTMLInputElement;
    const newUrlInput = this.container.querySelector('#new-url') as HTMLInputElement;
    if (oldUrlInput && this.currentConfig.oldUrl) {
      oldUrlInput.value = this.currentConfig.oldUrl;
    }
    if (newUrlInput && this.currentConfig.newUrl) {
      newUrlInput.value = this.currentConfig.newUrl;
    }

    // 更新Headers表单
    this.updateHeadersForm();

    // 更新查询参数表单
    this.updateQueryParamsForm();

    // 更新请求体
    this.updateBodyForm();

    // 更新认证表单
    this.updateAuthForm();

    // 重新绑定键值对编辑器事件
    this.bindKeyValueEvents('headers');
    this.bindKeyValueEvents('query');
  }

  /**
   * 更新Headers表单
   */
  private updateHeadersForm(): void {
    const headersContainer = this.container.querySelector('#headers-container');
    if (!headersContainer) return;

    // 清空现有的headers行
    headersContainer.innerHTML = '';

    // 添加配置中的headers
    if (this.currentConfig.headers && Object.keys(this.currentConfig.headers).length > 0) {
      Object.entries(this.currentConfig.headers).forEach(([key, value]) => {
        if (key && value) {
          const row = this.createKeyValueRow(key, value);
          headersContainer.appendChild(row);
        }
      });
    }

    // 添加一个空行用于新增
    const emptyRow = this.createKeyValueRow('', '');
    headersContainer.appendChild(emptyRow);
  }

  /**
   * 更新查询参数表单
   */
  private updateQueryParamsForm(): void {
    const paramsContainer = this.container.querySelector('#query-params-container');
    if (!paramsContainer) return;

    // 清空现有的参数行
    paramsContainer.innerHTML = '';

    // 添加配置中的查询参数
    if (this.currentConfig.queryParams && Object.keys(this.currentConfig.queryParams).length > 0) {
      Object.entries(this.currentConfig.queryParams).forEach(([key, value]) => {
        if (key && value) {
          const row = this.createKeyValueRow(key, value);
          paramsContainer.appendChild(row);
        }
      });
    }

    // 添加一个空行用于新增
    const emptyRow = this.createKeyValueRow('', '');
    paramsContainer.appendChild(emptyRow);
  }

  /**
   * 更新请求体表单
   */
  private updateBodyForm(): void {
    if (!this.currentConfig.body) return;

    // 更新body类型选择器
    const bodyTypeSelect = this.container.querySelector('#body-type-select') as HTMLSelectElement;
    if (bodyTypeSelect && this.currentConfig.body.type) {
      bodyTypeSelect.value = this.currentConfig.body.type;

      // 触发change事件以更新UI
      bodyTypeSelect.dispatchEvent(new Event('change'));
    }

    // 更新body内容
    const bodyTextarea = this.container.querySelector('#body-content') as HTMLTextAreaElement;
    if (bodyTextarea && this.currentConfig.body.content) {
      bodyTextarea.value = this.currentConfig.body.content;
    }
  }

  /**
   * 更新认证表单
   */
  private updateAuthForm(): void {
    if (!this.currentConfig.auth) return;

    // 更新认证类型选择器
    const authTypeSelect = this.container.querySelector('#auth-type-select') as HTMLSelectElement;
    if (authTypeSelect && this.currentConfig.auth.type) {
      authTypeSelect.value = this.currentConfig.auth.type;

      // 触发change事件以更新UI
      authTypeSelect.dispatchEvent(new Event('change'));
    }

    // 更新认证凭据
    if (this.currentConfig.auth.credentials) {
      const usernameInput = this.container.querySelector('#auth-username') as HTMLInputElement;
      const passwordInput = this.container.querySelector('#auth-password') as HTMLInputElement;

      if (usernameInput && this.currentConfig.auth.credentials.username) {
        usernameInput.value = this.currentConfig.auth.credentials.username;
      }
      if (passwordInput && this.currentConfig.auth.credentials.password) {
        passwordInput.value = this.currentConfig.auth.credentials.password;
      }
    }
  }

  /**
   * 创建键值对行
   */
  private createKeyValueRow(key: string, value: string): HTMLElement {
    const row = document.createElement('div');
    row.className = 'key-value-row';
    row.innerHTML = `
      <input type="text" class="form-control key-input" placeholder="Key" value="${key}">
      <input type="text" class="form-control value-input" placeholder="Value" value="${value}">
      <button type="button" class="btn btn-outline remove-row-btn" title="Remove">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <line x1="18" y1="6" x2="6" y2="18"/>
          <line x1="6" y1="6" x2="18" y2="18"/>
        </svg>
      </button>
    `;
    return row;
  }

  /**
   * 显示导入成功通知
   */
  private showImportSuccessNotification(): void {
    // 创建临时通知元素
    const notification = document.createElement('div');
    notification.className = 'import-success-notification';
    notification.innerHTML = `
      <div class="notification-content">
        <span class="notification-icon">✅</span>
        <span class="notification-text">cURL configuration imported successfully!</span>
      </div>
    `;

    // 添加到页面
    this.container.appendChild(notification);

    // 显示动画
    setTimeout(() => {
      notification.classList.add('show');
    }, 10);

    // 自动移除
    setTimeout(() => {
      notification.classList.remove('show');
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
      }, 300);
    }, 3000);
  }



  /**
   * 获取默认配置
   */
  private getDefaultConfig(): RequestConfig {
    return {
      method: 'GET',
      oldUrl: '',
      newUrl: '',
      headers: {
        'Content-Type': 'application/json'
      },
      queryParams: {},
      body: {
        type: 'json',
        content: ''
      },
      timeout: 10000,
      ignoreFields: ['timestamp', 'traceId']
    };
  }

  /**
   * 公共方法：获取当前配置
   */
  public getRequestConfig(): RequestConfig {
    return { ...this.currentConfig };
  }

  /**
   * 公共方法：设置配置
   */
  public setRequestConfig(config: RequestConfig): void {
    this.currentConfig = { ...config };
    this.render();
    this.bindEvents();
    this.validateAndNotify();
  }

  /**
   * 公共方法：设置事件回调
   */
  public setCallbacks(callbacks: {
    onConfigChange?: (config: RequestConfig) => void;
    onValidationChange?: (isValid: boolean, errors: Record<string, string>) => void;
  }): void {
    this.onConfigChange = callbacks.onConfigChange;
    this.onValidationChange = callbacks.onValidationChange;
  }

  /**
   * 初始化cURL语法高亮
   */
  private initializeCurlSyntaxHighlight(textarea: HTMLTextAreaElement, highlightLayer: HTMLElement | null): void {
    if (!highlightLayer) return;
    this.updateCurlSyntaxHighlight(textarea, highlightLayer);
  }

  /**
   * 更新cURL语法高亮
   */
  private updateCurlSyntaxHighlight(textarea: HTMLTextAreaElement, highlightLayer: HTMLElement | null): void {
    if (!highlightLayer) return;

    const text = textarea.value;
    const highlightedText = this.highlightCurlSyntax(text);
    highlightLayer.innerHTML = highlightedText;
  }

  /**
   * cURL语法高亮
   */
  private highlightCurlSyntax(text: string): string {
    if (!text.trim()) return '';

    // 转义HTML
    let highlighted = this.escapeHtml(text);

    // 高亮curl命令
    highlighted = highlighted.replace(/^(\s*)(curl)\b/gm, '$1<span class="curl-command">$2</span>');

    // 高亮选项
    highlighted = highlighted.replace(/(\s)(-[a-zA-Z]|--[a-zA-Z-]+)/g, '$1<span class="curl-option">$2</span>');

    // 高亮HTTP方法
    highlighted = highlighted.replace(/(-X\s+)([A-Z]+)/g, '$1<span class="curl-method">$2</span>');

    // 高亮URL
    highlighted = highlighted.replace(/(https?:\/\/[^\s'"]+)/g, '<span class="curl-url">$1</span>');

    // 高亮头部
    highlighted = highlighted.replace(/(-H\s+)(['"])(.*?)\2/g, '$1$2<span class="curl-header">$3</span>$2');

    // 高亮数据
    highlighted = highlighted.replace(/(-d\s+)(['"])(.*?)\2/g, '$1$2<span class="curl-data">$3</span>$2');

    // 高亮字符串
    highlighted = highlighted.replace(/(['"])((?:(?!\1)[^\\]|\\.)*)(\1)/g, '<span class="curl-string">$1$2$3</span>');

    // 高亮行继续符
    highlighted = highlighted.replace(/(\\)(\s*$)/gm, '<span class="curl-continuation">$1</span>$2');

    return highlighted;
  }

  /**
   * 更新cURL解析按钮状态
   */
  private updateCurlParseButtonState(textarea: HTMLTextAreaElement, button: HTMLButtonElement): void {
    const hasContent = textarea.value.trim().length > 0;
    button.disabled = !hasContent;
  }

  /**
   * 处理cURL解析
   */
  private handleCurlParse(curlCommand: string, resultContainer: HTMLElement): void {
    try {
      // 显示解析中状态
      this.showCurlParsingState(resultContainer);

      // 延迟执行解析，给用户反馈
      setTimeout(() => {
        try {
          // 使用CurlParsingUtils进行解析
          const parseResult = CurlParsingUtils.parse(curlCommand);

          if (parseResult.success && parseResult.config) {
            // 解析成功，显示结果并应用配置
            this.showCurlParseSuccess(parseResult.config, resultContainer);

            // 自动应用解析的配置
            this.handleCurlConfigParsed(parseResult.config as RequestConfig);
          } else {
            // 解析失败，显示错误信息
            this.showCurlParseError(parseResult.error || 'Unknown parsing error', resultContainer, parseResult.errorPosition);
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unexpected error occurred';
          this.showCurlParseError(errorMessage, resultContainer);
        }
      }, 100);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unexpected error occurred';
      this.showCurlParseError(errorMessage, resultContainer);
    }
  }

  /**
   * 显示cURL示例
   */
  private showCurlExamples(textarea: HTMLTextAreaElement, highlightLayer: HTMLElement | null, button: HTMLButtonElement): void {
    const examples = [
      `curl -X GET https://api.example.com/users`,
      `curl -X POST https://api.example.com/users \\
  -H 'Content-Type: application/json' \\
  -d '{"name":"John","email":"<EMAIL>"}'`,
      `curl -X PUT https://api.example.com/users/123 \\
  -H 'Authorization: Bearer token123' \\
  -H 'Content-Type: application/json' \\
  -d '{"name":"John Updated"}'`
    ];

    const randomExample = examples[Math.floor(Math.random() * examples.length)];
    textarea.value = randomExample;
    this.updateCurlSyntaxHighlight(textarea, highlightLayer);
    this.updateCurlParseButtonState(textarea, button);
  }

  /**
   * HTML转义
   */
  private escapeHtml(text: string): string {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  /**
   * 显示cURL解析中状态
   */
  private showCurlParsingState(resultContainer: HTMLElement): void {
    resultContainer.innerHTML = `
      <div class="parse-result loading">
        <div class="result-header">
          <div class="result-icon">
            <div class="loading-spinner"></div>
          </div>
          <div class="result-title">Parsing...</div>
        </div>
        <div class="result-content">
          <p>正在解析cURL命令...</p>
        </div>
      </div>
    `;
  }

  /**
   * 显示cURL解析成功结果
   */
  private showCurlParseSuccess(config: Partial<RequestConfig>, resultContainer: HTMLElement): void {
    const configPreview = this.generateCurlConfigPreview(config);

    resultContainer.innerHTML = `
      <div class="parse-result success">
        <div class="result-header">
          <div class="result-icon">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <polyline points="20,6 9,17 4,12"/>
            </svg>
          </div>
          <div class="result-title">Parse Successful</div>
        </div>
        <div class="result-content">
          <div class="config-preview">
            <h5>解析的配置:</h5>
            <div class="config-details">
              ${configPreview}
            </div>
          </div>
          <div class="result-actions">
            <button id="apply-curl-config-btn" class="btn btn-success">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polyline points="20,6 9,17 4,12"/>
              </svg>
              应用配置
            </button>
            <button id="copy-curl-config-btn" class="btn btn-outline">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                <path d="M5,15H4a2,2 0 0,1 -2,-2V4a2,2 0 0,1 2,-2H13a2,2 0 0,1 2,2v1"/>
              </svg>
              复制为JSON
            </button>
          </div>
        </div>
      </div>
    `;

    // 绑定按钮事件
    this.bindCurlResultActions(config, resultContainer);
  }

  /**
   * 显示cURL解析错误
   */
  private showCurlParseError(error: string, resultContainer: HTMLElement, position?: number): void {
    const formattedError = position !== undefined
      ? CurlParsingUtils.formatParseError(error, position)
      : error;

    resultContainer.innerHTML = `
      <div class="parse-result error">
        <div class="result-header">
          <div class="result-icon">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="12" cy="12" r="10"/>
              <line x1="15" y1="9" x2="9" y2="15"/>
              <line x1="9" y1="9" x2="15" y2="15"/>
            </svg>
          </div>
          <div class="result-title">Parse Failed</div>
        </div>
        <div class="result-content">
          <div class="error-message">
            <p>${formattedError}</p>
          </div>
          <div class="error-help">
            <p><strong>常见问题:</strong></p>
            <ul>
              <li>确保命令以 "curl" 开头</li>
              <li>检查引号是否匹配</li>
              <li>确保URL格式正确</li>
              <li>验证请求头格式: "Name: Value"</li>
              <li>正确转义特殊字符</li>
            </ul>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * 生成cURL配置预览
   */
  private generateCurlConfigPreview(config: Partial<RequestConfig>): string {
    const items: string[] = [];

    if (config.method) {
      items.push(`<div class="config-item"><strong>方法:</strong> ${config.method}</div>`);
    }

    if (config.oldUrl) {
      items.push(`<div class="config-item"><strong>URL:</strong> <code>${config.oldUrl}</code></div>`);
    }

    if (config.headers && Object.keys(config.headers).length > 0) {
      const headerCount = Object.keys(config.headers).length;
      items.push(`<div class="config-item"><strong>请求头:</strong> ${headerCount} 个</div>`);
    }

    if (config.queryParams && Object.keys(config.queryParams).length > 0) {
      const paramCount = Object.keys(config.queryParams).length;
      items.push(`<div class="config-item"><strong>查询参数:</strong> ${paramCount} 个</div>`);
    }

    if (config.body && config.body.content) {
      const contentPreview = config.body.content.length > 50
        ? config.body.content.substring(0, 50) + '...'
        : config.body.content;
      items.push(`<div class="config-item"><strong>请求体:</strong> <code>${contentPreview}</code></div>`);
    }

    if (config.auth) {
      items.push(`<div class="config-item"><strong>认证:</strong> ${config.auth.type}</div>`);
    }

    return items.length > 0 ? items.join('') : '<div class="config-item">无配置项</div>';
  }

  /**
   * 绑定cURL结果操作按钮
   */
  private bindCurlResultActions(config: Partial<RequestConfig>, resultContainer: HTMLElement): void {
    const applyButton = resultContainer.querySelector('#apply-curl-config-btn');
    const copyButton = resultContainer.querySelector('#copy-curl-config-btn');

    applyButton?.addEventListener('click', () => {
      // 配置已经在showCurlParseSuccess调用前自动应用了
      this.showCurlAppliedFeedback(applyButton as HTMLElement);
    });

    copyButton?.addEventListener('click', () => {
      this.copyCurlConfigToClipboard(config, copyButton as HTMLElement);
    });
  }

  /**
   * 显示应用成功反馈
   */
  private showCurlAppliedFeedback(button: HTMLElement): void {
    const originalText = button.textContent;
    button.textContent = '✅ 已应用!';
    button.setAttribute('disabled', 'true');

    setTimeout(() => {
      button.textContent = originalText;
      button.removeAttribute('disabled');
    }, 2000);
  }

  /**
   * 复制配置到剪贴板
   */
  private async copyCurlConfigToClipboard(config: Partial<RequestConfig>, button: HTMLElement): Promise<void> {
    try {
      const jsonString = JSON.stringify(config, null, 2);
      await navigator.clipboard.writeText(jsonString);

      const originalText = button.textContent;
      button.textContent = '✅ 已复制!';

      setTimeout(() => {
        button.textContent = originalText;
      }, 2000);
    } catch (error) {
      console.error('复制到剪贴板失败:', error);
      button.textContent = '❌ 复制失败';
      setTimeout(() => {
        button.textContent = '复制为JSON';
      }, 2000);
    }
  }
}
