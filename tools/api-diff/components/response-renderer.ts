/**
 * Response Renderer
 * 响应渲染器，用于渲染单个和双端 API 响应结果
 */

import type { 
  ApiResponse, 
  DualExecutionResult 
} from '../types/api-diff-types';
import { JsonHighlighter } from '../utils/json-highlighter';
import { HttpRequestUtils } from '../utils/http-request-utils';

/**
 * 渲染选项接口
 */
export interface RenderOptions {
  showHeaders?: boolean;
  showTiming?: boolean;
  showRawResponse?: boolean;
  enableJsonCollapse?: boolean;
  theme?: 'light' | 'dark';
  maxContentHeight?: number;
}

/**
 * 响应渲染器类
 */
export class ResponseRenderer {
  private container: HTMLElement;
  private options: RenderOptions;
  private syncScrollEnabled: boolean = true;
  private scrollSyncHandler: ((event: Event) => void) | null = null;

  constructor(container: HTMLElement, options: RenderOptions = {}) {
    this.container = container;
    this.options = {
      showHeaders: true,
      showTiming: true,
      showRawResponse: false,
      enableJsonCollapse: true,
      theme: 'light',
      maxContentHeight: 500,
      ...options
    };

    this.injectStyles();
  }

  /**
   * 渲染双端响应结果
   */
  public renderDualResponse(result: DualExecutionResult): void {
    this.container.innerHTML = `
      <div class="dual-response-container">
        <div class="response-summary">
          ${this.renderSummary(result)}
        </div>

        <div class="response-panels" data-sync-scroll="true">
          <div class="response-panel old-response">
            <div class="panel-header">
              <h3>
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                  <polyline points="14,2 14,8 20,8"/>
                </svg>
                Old API Response
              </h3>
              ${this.renderPanelActions('old')}
            </div>
            <div class="panel-content" data-scroll-group="response" data-panel="old">
              ${this.renderSingleResponse(result.oldResponse, result.oldError)}
            </div>
          </div>

          <div class="response-panel new-response">
            <div class="panel-header">
              <h3>
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                  <polyline points="14,2 14,8 20,8"/>
                  <circle cx="12" cy="15" r="1"/>
                </svg>
                New API Response
              </h3>
              ${this.renderPanelActions('new')}
            </div>
            <div class="panel-content" data-scroll-group="response" data-panel="new">
              ${this.renderSingleResponse(result.newResponse, result.newError)}
            </div>
          </div>
        </div>

        <div class="sync-scroll-controls">
          <label class="sync-scroll-toggle">
            <input type="checkbox" id="sync-scroll-checkbox" checked>
            <span class="toggle-slider"></span>
            <span class="toggle-label">同步滚动</span>
          </label>
        </div>
      </div>
    `;

    this.bindEvents();
    this.initializeSyncScroll();
  }

  /**
   * 初始化同步滚动功能
   */
  private initializeSyncScroll(): void {
    const scrollElements = this.container.querySelectorAll('[data-scroll-group="response"]');
    const syncToggle = this.container.querySelector('#sync-scroll-checkbox') as HTMLInputElement;

    if (scrollElements.length !== 2 || !syncToggle) return;

    const [oldPanel, newPanel] = Array.from(scrollElements) as HTMLElement[];

    // 创建同步滚动处理器
    this.scrollSyncHandler = this.createScrollSyncHandler(oldPanel, newPanel);

    // 绑定滚动事件
    oldPanel.addEventListener('scroll', this.scrollSyncHandler);
    newPanel.addEventListener('scroll', this.scrollSyncHandler);

    // 绑定同步开关
    syncToggle.addEventListener('change', (e) => {
      this.syncScrollEnabled = (e.target as HTMLInputElement).checked;
    });
  }

  /**
   * 创建同步滚动处理器
   */
  private createScrollSyncHandler(panel1: HTMLElement, panel2: HTMLElement): (event: Event) => void {
    let isScrolling = false;

    return (event: Event) => {
      if (!this.syncScrollEnabled || isScrolling) return;

      const sourcePanel = event.target as HTMLElement;
      const targetPanel = sourcePanel === panel1 ? panel2 : panel1;

      isScrolling = true;

      // 计算滚动比例
      const sourceScrollRatio = sourcePanel.scrollTop / (sourcePanel.scrollHeight - sourcePanel.clientHeight);
      const targetScrollTop = sourceScrollRatio * (targetPanel.scrollHeight - targetPanel.clientHeight);

      // 同步滚动位置
      targetPanel.scrollTop = targetScrollTop;

      // 防止递归调用
      requestAnimationFrame(() => {
        isScrolling = false;
      });
    };
  }

  /**
   * 渲染单个响应
   */
  public renderSingleResponse(response: ApiResponse | null, error: string | null = null): string {
    if (error) {
      return this.renderError(error);
    }

    if (!response) {
      return this.renderEmpty();
    }

    return `
      <div class="single-response">
        ${this.renderStatusLine(response)}
        ${this.options.showTiming ? this.renderTiming(response) : ''}
        ${this.options.showHeaders ? this.renderHeaders(response) : ''}
        ${this.renderResponseBody(response)}
      </div>
    `;
  }

  /**
   * 渲染响应体
   */
  private renderResponseBody(response: ApiResponse): string {
    if (!response.data) {
      return '<div class="response-body-empty">No response body</div>';
    }

    try {
      const jsonData = typeof response.data === 'string' ? JSON.parse(response.data) : response.data;

      if (this.options.enableJsonCollapse) {
        return `
          <div class="response-body">
            <div class="response-body-header">
              <h4>Response Body</h4>
              <div class="body-actions">
                <button class="btn btn-outline btn-sm" onclick="this.expandAll()">Expand All</button>
                <button class="btn btn-outline btn-sm" onclick="this.collapseAll()">Collapse All</button>
                <button class="btn btn-outline btn-sm" onclick="this.copyJson()">Copy JSON</button>
              </div>
            </div>
            <div class="json-tree" data-json='${JSON.stringify(jsonData)}'>
              ${this.renderJsonTree(jsonData, '', 0)}
            </div>
          </div>
        `;
      } else {
        return `
          <div class="response-body">
            <div class="response-body-header">
              <h4>Response Body</h4>
              <div class="body-actions">
                <button class="btn btn-outline btn-sm" onclick="this.copyJson()">Copy JSON</button>
              </div>
            </div>
            <pre class="json-raw">${JSON.stringify(jsonData, null, 2)}</pre>
          </div>
        `;
      }
    } catch (e) {
      return `
        <div class="response-body">
          <div class="response-body-header">
            <h4>Response Body (Raw)</h4>
          </div>
          <pre class="response-raw">${response.data}</pre>
        </div>
      `;
    }
  }

  /**
   * 渲染JSON树
   */
  private renderJsonTree(data: any, key: string = '', level: number = 0): string {
    const indent = '  '.repeat(level);
    const isRoot = level === 0;

    if (data === null) {
      return `${indent}<span class="json-null">null</span>`;
    }

    if (typeof data === 'string') {
      return `${indent}<span class="json-string">"${this.escapeHtml(data)}"</span>`;
    }

    if (typeof data === 'number') {
      return `${indent}<span class="json-number">${data}</span>`;
    }

    if (typeof data === 'boolean') {
      return `${indent}<span class="json-boolean">${data}</span>`;
    }

    if (Array.isArray(data)) {
      if (data.length === 0) {
        return `${indent}[]`;
      }

      const nodeId = `node-${Math.random().toString(36).substr(2, 9)}`;
      const isCollapsed = level > 2; // 默认折叠深层级

      return `
        <div class="json-node">
          <button class="json-toggle ${isCollapsed ? 'collapsed' : 'expanded'}" data-target="${nodeId}"></button>
          <span class="json-bracket">[</span>
          <span class="json-ellipsis" style="display: ${isCollapsed ? 'inline' : 'none'}">... ${data.length} items</span>
          <div class="json-children ${isCollapsed ? 'collapsed' : ''}" id="${nodeId}">
            ${data.map((item, index) => `
              <div class="json-array-item">
                ${this.renderJsonTree(item, '', level + 1)}${index < data.length - 1 ? ',' : ''}
              </div>
            `).join('')}
          </div>
          <span class="json-bracket">]</span>
        </div>
      `;
    }

    if (typeof data === 'object') {
      const keys = Object.keys(data);
      if (keys.length === 0) {
        return `${indent}{}`;
      }

      const nodeId = `node-${Math.random().toString(36).substr(2, 9)}`;
      const isCollapsed = level > 1; // 默认折叠深层级

      return `
        <div class="json-node">
          <button class="json-toggle ${isCollapsed ? 'collapsed' : 'expanded'}" data-target="${nodeId}"></button>
          <span class="json-bracket">{</span>
          <span class="json-ellipsis" style="display: ${isCollapsed ? 'inline' : 'none'}">... ${keys.length} properties</span>
          <div class="json-children ${isCollapsed ? 'collapsed' : ''}" id="${nodeId}">
            ${keys.map((objKey, index) => `
              <div class="json-property">
                <span class="json-key">"${this.escapeHtml(objKey)}"</span>: ${this.renderJsonTree(data[objKey], objKey, level + 1)}${index < keys.length - 1 ? ',' : ''}
              </div>
            `).join('')}
          </div>
          <span class="json-bracket">}</span>
        </div>
      `;
    }

    return `${indent}<span class="json-unknown">${String(data)}</span>`;
  }

  /**
   * 绑定事件
   */
  private bindEvents(): void {
    // 绑定JSON折叠/展开事件
    this.container.addEventListener('click', (e) => {
      const target = e.target as HTMLElement;

      if (target.classList.contains('json-toggle')) {
        this.toggleJsonNode(target);
      }

      if (target.classList.contains('json-ellipsis')) {
        const toggle = target.parentElement?.querySelector('.json-toggle') as HTMLElement;
        if (toggle) {
          this.toggleJsonNode(toggle);
        }
      }
    });

    // 绑定工具按钮事件
    this.container.addEventListener('click', (e) => {
      const target = e.target as HTMLElement;

      if (target.textContent?.includes('Expand All')) {
        this.expandAll();
      } else if (target.textContent?.includes('Collapse All')) {
        this.collapseAll();
      } else if (target.textContent?.includes('Copy JSON')) {
        this.copyJson(target);
      }
    });
  }

  /**
   * 切换JSON节点的折叠状态
   */
  private toggleJsonNode(toggle: HTMLElement): void {
    const targetId = toggle.getAttribute('data-target');
    if (!targetId) return;

    const targetElement = document.getElementById(targetId);
    const ellipsis = toggle.parentElement?.querySelector('.json-ellipsis') as HTMLElement;

    if (!targetElement || !ellipsis) return;

    const isCollapsed = toggle.classList.contains('collapsed');

    if (isCollapsed) {
      // 展开
      toggle.classList.remove('collapsed');
      toggle.classList.add('expanded');
      targetElement.classList.remove('collapsed');
      ellipsis.style.display = 'none';
    } else {
      // 折叠
      toggle.classList.remove('expanded');
      toggle.classList.add('collapsed');
      targetElement.classList.add('collapsed');
      ellipsis.style.display = 'inline';
    }
  }

  /**
   * 展开所有节点
   */
  private expandAll(): void {
    const toggles = this.container.querySelectorAll('.json-toggle');
    toggles.forEach(toggle => {
      const targetId = toggle.getAttribute('data-target');
      if (!targetId) return;

      const targetElement = document.getElementById(targetId);
      const ellipsis = toggle.parentElement?.querySelector('.json-ellipsis') as HTMLElement;

      if (targetElement && ellipsis) {
        toggle.classList.remove('collapsed');
        toggle.classList.add('expanded');
        targetElement.classList.remove('collapsed');
        ellipsis.style.display = 'none';
      }
    });
  }

  /**
   * 折叠所有节点
   */
  private collapseAll(): void {
    const toggles = this.container.querySelectorAll('.json-toggle');
    toggles.forEach(toggle => {
      const targetId = toggle.getAttribute('data-target');
      if (!targetId) return;

      const targetElement = document.getElementById(targetId);
      const ellipsis = toggle.parentElement?.querySelector('.json-ellipsis') as HTMLElement;

      if (targetElement && ellipsis) {
        toggle.classList.remove('expanded');
        toggle.classList.add('collapsed');
        targetElement.classList.add('collapsed');
        ellipsis.style.display = 'inline';
      }
    });
  }

  /**
   * 复制JSON到剪贴板
   */
  private async copyJson(button?: HTMLElement): Promise<void> {
    try {
      const jsonTree = this.container.querySelector('.json-tree') as HTMLElement;
      if (!jsonTree) return;

      const jsonData = jsonTree.getAttribute('data-json');
      if (!jsonData) return;

      await navigator.clipboard.writeText(jsonData);

      // 显示复制成功反馈
      if (button) {
        const originalText = button.textContent;
        button.textContent = '✓ Copied!';
        button.classList.add('btn-success');

        setTimeout(() => {
          button.textContent = originalText;
          button.classList.remove('btn-success');
        }, 2000);
      }
    } catch (error) {
      console.error('Failed to copy JSON:', error);
    }
  }

  /**
   * HTML转义
   */
  private escapeHtml(text: string): string {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  /**
   * 渲染摘要信息
   */
  private renderSummary(result: DualExecutionResult): string {
    const oldStatus = result.oldResponse?.status || 'Error';
    const newStatus = result.newResponse?.status || 'Error';
    const statusMatch = oldStatus === newStatus;
    
    return `
      <div class="summary-grid">
        <div class="summary-item">
          <label>Status Comparison</label>
          <div class="status-comparison ${statusMatch ? 'match' : 'mismatch'}">
            <span class="old-status">${oldStatus}</span>
            <span class="comparison-arrow">${statusMatch ? '=' : '≠'}</span>
            <span class="new-status">${newStatus}</span>
          </div>
        </div>
        
        <div class="summary-item">
          <label>Response Time</label>
          <div class="timing-comparison">
            <span class="old-timing">${result.oldDuration}ms</span>
            <span class="timing-separator">vs</span>
            <span class="new-timing">${result.newDuration}ms</span>
          </div>
        </div>
        
        <div class="summary-item">
          <label>Execution Status</label>
          <div class="execution-status ${this.getExecutionStatusClass(result)}">
            ${this.getExecutionStatusText(result)}
          </div>
        </div>
      </div>
    `;
  }

  /**
   * 渲染面板操作按钮
   */
  private renderPanelActions(type: 'old' | 'new'): string {
    return `
      <div class="panel-actions">
        <button class="action-btn copy-btn" data-action="copy" data-type="${type}">
          📋 Copy
        </button>
        <button class="action-btn download-btn" data-action="download" data-type="${type}">
          💾 Download
        </button>
        <button class="action-btn toggle-raw-btn" data-action="toggle-raw" data-type="${type}">
          🔍 Raw
        </button>
      </div>
    `;
  }

  /**
   * 渲染状态行
   */
  private renderStatusLine(response: ApiResponse): string {
    const statusClass = this.getStatusClass(response.status);
    const statusDescription = HttpRequestUtils.getStatusDescription(response.status);
    
    return `
      <div class="status-line">
        <div class="status-info">
          <span class="status-code ${statusClass}">${response.status}</span>
          <span class="status-text">${response.statusText}</span>
          <span class="status-description">(${statusDescription})</span>
        </div>
        <div class="response-size">
          ${HttpRequestUtils.formatResponseSize(response.bodyText)}
        </div>
      </div>
    `;
  }

  /**
   * 渲染时间信息
   */
  private renderTiming(response: ApiResponse): string {
    return `
      <div class="timing-info">
        <div class="timing-item">
          <label>Duration:</label>
          <span class="timing-value">${response.duration}ms</span>
        </div>
        <div class="timing-item">
          <label>Timestamp:</label>
          <span class="timing-value">${new Date(response.timestamp).toLocaleString()}</span>
        </div>
      </div>
    `;
  }

  /**
   * 渲染响应头
   */
  private renderHeaders(response: ApiResponse): string {
    const headers = Object.entries(response.headers);
    
    if (headers.length === 0) {
      return '<div class="headers-section"><h4>Headers</h4><p class="no-data">No headers</p></div>';
    }

    const headerRows = headers.map(([key, value]) => `
      <tr>
        <td class="header-name">${key}</td>
        <td class="header-value">${value}</td>
      </tr>
    `).join('');

    return `
      <div class="headers-section">
        <h4>Response Headers (${headers.length})</h4>
        <div class="headers-table-container">
          <table class="headers-table">
            <thead>
              <tr>
                <th>Name</th>
                <th>Value</th>
              </tr>
            </thead>
            <tbody>
              ${headerRows}
            </tbody>
          </table>
        </div>
      </div>
    `;
  }

  /**
   * 渲染响应体
   */
  private renderBody(response: ApiResponse): string {
    if (!response.bodyText.trim()) {
      return '<div class="body-section"><h4>Response Body</h4><p class="no-data">Empty response body</p></div>';
    }

    const highlightedContent = JsonHighlighter.highlight(response.bodyText, {
      enableCollapse: this.options.enableJsonCollapse,
      theme: this.options.theme,
      maxDepth: 5
    });

    return `
      <div class="body-section">
        <h4>Response Body</h4>
        <div class="body-content" style="max-height: ${this.options.maxContentHeight}px;">
          ${highlightedContent}
        </div>
      </div>
    `;
  }

  /**
   * 渲染错误信息
   */
  private renderError(error: string): string {
    return `
      <div class="error-response">
        <div class="error-icon">❌</div>
        <div class="error-content">
          <h4>Request Failed</h4>
          <p class="error-message">${error}</p>
        </div>
      </div>
    `;
  }

  /**
   * 渲染空响应
   */
  private renderEmpty(): string {
    return `
      <div class="empty-response">
        <div class="empty-icon">⏳</div>
        <div class="empty-content">
          <h4>No Response</h4>
          <p>Request not executed yet</p>
        </div>
      </div>
    `;
  }

  /**
   * 获取状态码样式类
   */
  private getStatusClass(status: number): string {
    if (status >= 200 && status < 300) return 'status-success';
    if (status >= 300 && status < 400) return 'status-redirect';
    if (status >= 400 && status < 500) return 'status-client-error';
    if (status >= 500) return 'status-server-error';
    return 'status-unknown';
  }

  /**
   * 获取执行状态样式类
   */
  private getExecutionStatusClass(result: DualExecutionResult): string {
    if (result.bothSucceeded) return 'status-success';
    if (result.bothFailed) return 'status-error';
    return 'status-partial';
  }

  /**
   * 获取执行状态文本
   */
  private getExecutionStatusText(result: DualExecutionResult): string {
    if (result.bothSucceeded) return 'Both APIs succeeded';
    if (result.bothFailed) return 'Both APIs failed';
    return 'Partial success';
  }

  /**
   * 绑定事件
   */
  private bindEvents(): void {
    this.container.addEventListener('click', (event) => {
      const target = event.target as HTMLElement;
      const action = target.getAttribute('data-action');
      const type = target.getAttribute('data-type');

      if (action && type) {
        this.handleAction(action, type);
      }
    });

    // 绑定 JSON 折叠事件
    const jsonViewers = this.container.querySelectorAll('.json-viewer');
    jsonViewers.forEach(viewer => {
      JsonHighlighter.bindCollapseEvents(viewer as HTMLElement);
    });
  }

  /**
   * 处理操作
   */
  private handleAction(action: string, type: string): void {
    switch (action) {
      case 'copy':
        this.copyResponse(type);
        break;
      case 'download':
        this.downloadResponse(type);
        break;
      case 'toggle-raw':
        this.toggleRawView(type);
        break;
    }
  }

  /**
   * 复制响应
   */
  private async copyResponse(type: string): Promise<void> {
    const panel = this.container.querySelector(`.${type}-response`);
    const bodyText = panel?.querySelector('.body-content')?.textContent || '';
    
    try {
      await navigator.clipboard.writeText(bodyText);
      this.showToast('Response copied to clipboard');
    } catch (error) {
      this.showToast('Failed to copy response', 'error');
    }
  }

  /**
   * 下载响应
   */
  private downloadResponse(type: string): void {
    const panel = this.container.querySelector(`.${type}-response`);
    const bodyText = panel?.querySelector('.body-content')?.textContent || '';
    
    const blob = new Blob([bodyText], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = `${type}-api-response.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    
    URL.revokeObjectURL(url);
    this.showToast('Response downloaded');
  }

  /**
   * 切换原始视图
   */
  private toggleRawView(type: string): void {
    // 这里可以实现原始响应视图的切换
    this.showToast('Raw view toggle (to be implemented)');
  }

  /**
   * 显示提示消息
   */
  private showToast(message: string, type: 'success' | 'error' = 'success'): void {
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.textContent = message;
    
    document.body.appendChild(toast);
    
    setTimeout(() => {
      toast.classList.add('show');
    }, 10);
    
    setTimeout(() => {
      toast.classList.remove('show');
      setTimeout(() => {
        if (toast.parentNode) {
          toast.parentNode.removeChild(toast);
        }
      }, 300);
    }, 3000);
  }

  /**
   * 注入样式
   */
  private injectStyles(): void {
    if (document.querySelector('#response-renderer-styles')) return;

    const style = document.createElement('style');
    style.id = 'response-renderer-styles';
    style.textContent = this.getStyles();
    document.head.appendChild(style);
  }

  /**
   * 获取样式
   */
  private getStyles(): string {
    return `
      ${JsonHighlighter.getDefaultStyles()}
      
      .dual-response-container {
        display: flex;
        flex-direction: column;
        gap: 16px;
      }
      
      .response-summary {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 16px;
      }
      
      .summary-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 16px;
      }
      
      .summary-item label {
        display: block;
        font-size: 12px;
        color: #666;
        margin-bottom: 4px;
        text-transform: uppercase;
        font-weight: 500;
      }
      
      .status-comparison {
        display: flex;
        align-items: center;
        gap: 8px;
        font-weight: 500;
      }
      
      .status-comparison.match {
        color: #28a745;
      }
      
      .status-comparison.mismatch {
        color: #dc3545;
      }
      
      .response-panels {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 16px;
      }
      
      .response-panel {
        border: 1px solid #e9ecef;
        border-radius: 8px;
        overflow: hidden;
      }
      
      .panel-header {
        background: #f8f9fa;
        padding: 12px 16px;
        border-bottom: 1px solid #e9ecef;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      
      .panel-header h3 {
        margin: 0;
        font-size: 14px;
        font-weight: 600;
      }
      
      .panel-actions {
        display: flex;
        gap: 8px;
      }
      
      .action-btn {
        padding: 4px 8px;
        font-size: 12px;
        border: 1px solid #ddd;
        background: white;
        border-radius: 4px;
        cursor: pointer;
      }
      
      .action-btn:hover {
        background: #f8f9fa;
      }
      
      .panel-content {
        padding: 16px;
      }
      
      .status-line {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
        padding: 8px 12px;
        background: #f8f9fa;
        border-radius: 4px;
      }
      
      .status-code {
        font-weight: bold;
        padding: 2px 6px;
        border-radius: 3px;
        color: white;
      }
      
      .status-success { background: #28a745; }
      .status-redirect { background: #17a2b8; }
      .status-client-error { background: #ffc107; color: #000; }
      .status-server-error { background: #dc3545; }
      .status-unknown { background: #6c757d; }
      
      .timing-info {
        display: flex;
        gap: 16px;
        margin-bottom: 12px;
        font-size: 12px;
        color: #666;
      }
      
      .headers-table-container {
        max-height: 200px;
        overflow-y: auto;
        border: 1px solid #e9ecef;
        border-radius: 4px;
      }
      
      .headers-table {
        width: 100%;
        border-collapse: collapse;
        font-size: 12px;
      }
      
      .headers-table th,
      .headers-table td {
        padding: 8px 12px;
        text-align: left;
        border-bottom: 1px solid #e9ecef;
      }
      
      .headers-table th {
        background: #f8f9fa;
        font-weight: 600;
      }
      
      .header-name {
        font-weight: 500;
        color: #495057;
      }
      
      .body-content {
        overflow: auto;
        border: 1px solid #e9ecef;
        border-radius: 4px;
      }
      
      .error-response,
      .empty-response {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 24px;
        text-align: center;
        color: #666;
      }
      
      .error-response {
        background: #fff5f5;
        border: 1px solid #fed7d7;
        border-radius: 8px;
      }
      
      .error-icon,
      .empty-icon {
        font-size: 24px;
      }
      
      .no-data {
        color: #999;
        font-style: italic;
        margin: 8px 0;
      }
      
      .toast {
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 16px;
        border-radius: 4px;
        color: white;
        font-size: 14px;
        z-index: 10000;
        transform: translateX(100%);
        transition: transform 0.3s ease;
      }
      
      .toast.show {
        transform: translateX(0);
      }
      
      .toast-success {
        background: #28a745;
      }
      
      .toast-error {
        background: #dc3545;
      }
      
      @media (max-width: 768px) {
        .response-panels {
          grid-template-columns: 1fr;
        }
        
        .summary-grid {
          grid-template-columns: 1fr;
        }
      }
    `;
  }

  /**
   * 更新选项
   */
  public updateOptions(newOptions: Partial<RenderOptions>): void {
    this.options = { ...this.options, ...newOptions };
  }
}
