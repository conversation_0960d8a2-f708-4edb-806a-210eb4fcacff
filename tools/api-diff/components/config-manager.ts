/**
 * Config Manager
 * 配置管理器，处理配置的保存、加载、导入、导出和历史记录管理
 */

import type { 
  RequestConfig, 
  DualExecutionResult 
} from '../types/api-diff-types';
import { 
  ApiDiffStorageUtils, 
  StoredRequestConfig, 
  StoredExecutionHistory,
  ApiDiffSettings 
} from '../utils/storage-utils';
import { ValidationUtils } from '../utils/validation-utils';

/**
 * 配置管理事件接口
 */
export interface ConfigManagerEvent {
  type: 'config-saved' | 'config-loaded' | 'config-deleted' | 'history-saved' | 'settings-updated';
  data: any;
  timestamp: number;
}

/**
 * 配置管理器类
 */
export class ConfigManager {
  private eventListeners: Map<string, ((event: ConfigManagerEvent) => void)[]> = new Map();
  private recentConfigs: StoredRequestConfig[] = [];
  private settings: ApiDiffSettings | null = null;

  constructor() {
    this.initializeEventListeners();
    this.loadSettings();
  }

  /**
   * 初始化事件监听器
   */
  private initializeEventListeners(): void {
    const eventTypes = ['config-saved', 'config-loaded', 'config-deleted', 'history-saved', 'settings-updated'];
    eventTypes.forEach(type => {
      this.eventListeners.set(type, []);
    });
  }

  /**
   * 加载设置
   */
  private async loadSettings(): Promise<void> {
    try {
      this.settings = await ApiDiffStorageUtils.getSettings();
    } catch (error) {
      console.error('加载设置失败:', error);
    }
  }

  /**
   * 添加事件监听器
   */
  public addEventListener(type: string, listener: (event: ConfigManagerEvent) => void): void {
    const listeners = this.eventListeners.get(type) || [];
    listeners.push(listener);
    this.eventListeners.set(type, listeners);
  }

  /**
   * 移除事件监听器
   */
  public removeEventListener(type: string, listener: (event: ConfigManagerEvent) => void): void {
    const listeners = this.eventListeners.get(type) || [];
    const index = listeners.indexOf(listener);
    if (index > -1) {
      listeners.splice(index, 1);
    }
  }

  /**
   * 触发事件
   */
  private emitEvent(type: string, data: any): void {
    const event: ConfigManagerEvent = {
      type: type as any,
      data,
      timestamp: Date.now()
    };

    const listeners = this.eventListeners.get(type) || [];
    listeners.forEach(listener => {
      try {
        listener(event);
      } catch (error) {
        console.error(`配置管理器事件监听器错误 (${type}):`, error);
      }
    });
  }

  /**
   * 保存配置
   */
  public async saveConfig(
    config: RequestConfig, 
    name: string, 
    tags?: string[]
  ): Promise<{ success: boolean; configId?: string; error?: string }> {
    try {
      // 验证配置
      const validation = ValidationUtils.validateRequestConfig(config);
      if (!validation.isValid) {
        return {
          success: false,
          error: `配置验证失败: ${Object.values(validation.errors).join(', ')}`
        };
      }

      // 保存配置
      const configId = await ApiDiffStorageUtils.saveConfig(config, name, tags);
      
      // 更新最近配置缓存
      await this.refreshRecentConfigs();
      
      this.emitEvent('config-saved', { configId, name, config });
      
      return { success: true, configId };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      return { success: false, error: errorMessage };
    }
  }

  /**
   * 加载配置
   */
  public async loadConfig(configId: string): Promise<{ success: boolean; config?: RequestConfig; error?: string }> {
    try {
      const storedConfig = await ApiDiffStorageUtils.loadConfig(configId);
      
      if (!storedConfig) {
        return { success: false, error: '配置不存在' };
      }

      // 更新最近配置缓存
      await this.refreshRecentConfigs();
      
      this.emitEvent('config-loaded', { configId, config: storedConfig.config });
      
      return { success: true, config: storedConfig.config };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      return { success: false, error: errorMessage };
    }
  }

  /**
   * 删除配置
   */
  public async deleteConfig(configId: string): Promise<{ success: boolean; error?: string }> {
    try {
      await ApiDiffStorageUtils.deleteConfig(configId);
      
      // 更新最近配置缓存
      await this.refreshRecentConfigs();
      
      this.emitEvent('config-deleted', { configId });
      
      return { success: true };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      return { success: false, error: errorMessage };
    }
  }

  /**
   * 获取所有配置
   */
  public async getAllConfigs(): Promise<StoredRequestConfig[]> {
    try {
      return await ApiDiffStorageUtils.getAllConfigs();
    } catch (error) {
      console.error('获取配置列表失败:', error);
      return [];
    }
  }

  /**
   * 获取最近使用的配置
   */
  public async getRecentConfigs(limit: number = 10): Promise<StoredRequestConfig[]> {
    if (this.recentConfigs.length === 0) {
      await this.refreshRecentConfigs();
    }
    
    return this.recentConfigs.slice(0, limit);
  }

  /**
   * 搜索配置
   */
  public async searchConfigs(query: string, tags?: string[]): Promise<StoredRequestConfig[]> {
    try {
      const allConfigs = await this.getAllConfigs();
      const lowerQuery = query.toLowerCase();
      
      return allConfigs.filter(config => {
        // 名称匹配
        const nameMatch = config.name.toLowerCase().includes(lowerQuery);
        
        // URL 匹配
        const urlMatch = config.config.oldUrl.toLowerCase().includes(lowerQuery) ||
                        config.config.newUrl.toLowerCase().includes(lowerQuery);
        
        // 标签匹配
        const tagMatch = tags ? tags.some(tag => config.tags?.includes(tag)) : true;
        
        return (nameMatch || urlMatch) && tagMatch;
      });
    } catch (error) {
      console.error('搜索配置失败:', error);
      return [];
    }
  }

  /**
   * 保存执行历史
   */
  public async saveHistory(configId: string, result: DualExecutionResult): Promise<{ success: boolean; historyId?: string; error?: string }> {
    try {
      const historyId = await ApiDiffStorageUtils.saveHistory(configId, result);
      
      this.emitEvent('history-saved', { configId, historyId, result });
      
      return { success: true, historyId };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      return { success: false, error: errorMessage };
    }
  }

  /**
   * 获取执行历史
   */
  public async getHistory(configId?: string): Promise<StoredExecutionHistory[]> {
    try {
      return await ApiDiffStorageUtils.getHistory(configId);
    } catch (error) {
      console.error('获取历史记录失败:', error);
      return [];
    }
  }

  /**
   * 清理历史记录
   */
  public async clearHistory(configId?: string): Promise<{ success: boolean; error?: string }> {
    try {
      await ApiDiffStorageUtils.clearHistory(configId);
      return { success: true };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      return { success: false, error: errorMessage };
    }
  }

  /**
   * 导出配置
   */
  public async exportConfig(configId: string): Promise<{ success: boolean; data?: string; error?: string }> {
    try {
      const storedConfig = await ApiDiffStorageUtils.loadConfig(configId);
      
      if (!storedConfig) {
        return { success: false, error: '配置不存在' };
      }

      const exportData = {
        version: '1.0.0',
        type: 'api-diff-config',
        config: storedConfig,
        exportedAt: new Date().toISOString()
      };

      return { success: true, data: JSON.stringify(exportData, null, 2) };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      return { success: false, error: errorMessage };
    }
  }

  /**
   * 导入配置
   */
  public async importConfig(jsonData: string): Promise<{ success: boolean; configId?: string; error?: string }> {
    try {
      const importData = JSON.parse(jsonData);
      
      // 验证导入数据格式
      if (!this.validateImportData(importData)) {
        return { success: false, error: '无效的导入数据格式' };
      }

      const config = importData.config.config;
      const name = `${importData.config.name} (导入)`;
      const tags = [...(importData.config.tags || []), '导入'];

      return await this.saveConfig(config, name, tags);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      return { success: false, error: errorMessage };
    }
  }

  /**
   * 导出所有数据
   */
  public async exportAllData(): Promise<{ success: boolean; data?: string; error?: string }> {
    try {
      const data = await ApiDiffStorageUtils.exportData();
      return { success: true, data };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      return { success: false, error: errorMessage };
    }
  }

  /**
   * 导入所有数据
   */
  public async importAllData(jsonData: string): Promise<{ success: boolean; error?: string }> {
    try {
      await ApiDiffStorageUtils.importData(jsonData);
      
      // 刷新缓存
      await this.refreshRecentConfigs();
      await this.loadSettings();
      
      return { success: true };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      return { success: false, error: errorMessage };
    }
  }

  /**
   * 获取设置
   */
  public async getSettings(): Promise<ApiDiffSettings> {
    if (!this.settings) {
      await this.loadSettings();
    }
    return this.settings!;
  }

  /**
   * 更新设置
   */
  public async updateSettings(newSettings: Partial<ApiDiffSettings>): Promise<{ success: boolean; error?: string }> {
    try {
      await ApiDiffStorageUtils.updateSettings(newSettings);
      await this.loadSettings();
      
      this.emitEvent('settings-updated', newSettings);
      
      return { success: true };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      return { success: false, error: errorMessage };
    }
  }

  /**
   * 重置设置
   */
  public async resetSettings(): Promise<{ success: boolean; error?: string }> {
    try {
      await ApiDiffStorageUtils.resetSettings();
      await this.loadSettings();
      
      this.emitEvent('settings-updated', this.settings);
      
      return { success: true };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      return { success: false, error: errorMessage };
    }
  }

  /**
   * 获取存储统计
   */
  public async getStorageStats(): Promise<{
    configCount: number;
    historyCount: number;
    totalSize: number;
    lastCleanup: number;
  }> {
    try {
      return await ApiDiffStorageUtils.getStorageStats();
    } catch (error) {
      console.error('获取存储统计失败:', error);
      return {
        configCount: 0,
        historyCount: 0,
        totalSize: 0,
        lastCleanup: 0
      };
    }
  }

  /**
   * 清理存储
   */
  public async cleanupStorage(): Promise<{ success: boolean; error?: string }> {
    try {
      await ApiDiffStorageUtils.cleanup();
      await this.refreshRecentConfigs();
      return { success: true };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      return { success: false, error: errorMessage };
    }
  }

  /**
   * 复制配置
   */
  public async duplicateConfig(configId: string, newName?: string): Promise<{ success: boolean; configId?: string; error?: string }> {
    try {
      const storedConfig = await ApiDiffStorageUtils.loadConfig(configId);
      
      if (!storedConfig) {
        return { success: false, error: '配置不存在' };
      }

      const name = newName || `${storedConfig.name} (副本)`;
      const tags = [...(storedConfig.tags || []), '副本'];

      return await this.saveConfig(storedConfig.config, name, tags);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      return { success: false, error: errorMessage };
    }
  }

  // 私有方法

  /**
   * 刷新最近配置缓存
   */
  private async refreshRecentConfigs(): Promise<void> {
    try {
      this.recentConfigs = await ApiDiffStorageUtils.getAllConfigs();
    } catch (error) {
      console.error('刷新最近配置缓存失败:', error);
    }
  }

  /**
   * 验证导入数据
   */
  private validateImportData(data: any): boolean {
    return (
      typeof data === 'object' &&
      data !== null &&
      data.type === 'api-diff-config' &&
      data.config &&
      typeof data.config === 'object' &&
      data.config.config &&
      typeof data.config.config === 'object'
    );
  }

  /**
   * 销毁管理器
   */
  public destroy(): void {
    this.eventListeners.clear();
    this.recentConfigs = [];
    this.settings = null;
  }
}
