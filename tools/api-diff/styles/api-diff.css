/**
 * API Diff Tool Styles
 * 接口差异对比工具的样式定义
 */

/* 设计系统变量 */
:root {
  /* 颜色系统 - 浅色主题 */
  --color-primary: #007bff;
  --color-primary-hover: #0056b3;
  --color-primary-light: #e3f2fd;
  --color-success: #28a745;
  --color-warning: #ffc107;
  --color-error: #dc3545;
  --color-info: #17a2b8;

  /* 背景色 */
  --color-background-primary: #f8f9fa;
  --color-background-secondary: #ffffff;
  --color-background-tertiary: #f1f3f4;
  --color-background-hover: #f8f9fa;
  --color-background-active: #e9ecef;

  /* 文本色 */
  --color-text-primary: #212529;
  --color-text-secondary: #6c757d;
  --color-text-tertiary: #adb5bd;
  --color-text-inverse: #ffffff;

  /* 边框色 */
  --color-border-primary: #dee2e6;
  --color-border-secondary: #e9ecef;
  --color-border-hover: #adb5bd;
  --color-border-focus: var(--color-primary);

  /* 阴影 */
  --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07), 0 2px 4px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1), 0 10px 10px rgba(0, 0, 0, 0.04);

  /* 字体系统 */
  --font-family-base: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  --font-family-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;

  /* 字体大小 */
  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1rem;     /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-2xl: 1.5rem;    /* 24px */
  --font-size-3xl: 1.875rem;  /* 30px */

  /* 字重 */
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* 行高 */
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;

  /* 间距系统 */
  --spacing-xs: 0.25rem;   /* 4px */
  --spacing-sm: 0.5rem;    /* 8px */
  --spacing-md: 0.75rem;   /* 12px */
  --spacing-lg: 1rem;      /* 16px */
  --spacing-xl: 1.5rem;    /* 24px */
  --spacing-2xl: 2rem;     /* 32px */
  --spacing-3xl: 3rem;     /* 48px */
  --spacing-4xl: 4rem;     /* 64px */

  /* 圆角 */
  --border-radius-sm: 0.25rem;  /* 4px */
  --border-radius-md: 0.375rem; /* 6px */
  --border-radius-lg: 0.5rem;   /* 8px */
  --border-radius-xl: 0.75rem;  /* 12px */
  --border-radius-full: 9999px;

  /* 过渡动画 */
  --transition-fast: 150ms ease;
  --transition-normal: 250ms ease;
  --transition-slow: 350ms ease;

  /* Z-index层级 */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}

/* 深色主题变量 */
@media (prefers-color-scheme: dark) {
  :root {
    --color-background-primary: #0d1117;
    --color-background-secondary: #161b22;
    --color-background-tertiary: #21262d;
    --color-background-hover: #30363d;
    --color-background-active: #21262d;

    --color-text-primary: #f0f6fc;
    --color-text-secondary: #8b949e;
    --color-text-tertiary: #6e7681;

    --color-border-primary: #30363d;
    --color-border-secondary: #21262d;
    --color-border-hover: #8b949e;
  }
}

/* 全局重置和基础样式 */
.api-diff-tool * {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.api-diff-tool {
  font-family: var(--font-family-base);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  background: var(--color-background-primary);
  color: var(--color-text-primary);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 工具头部 */
.tool-header {
  background: var(--color-background-secondary);
  border-bottom: 1px solid var(--color-border-primary);
  padding: var(--spacing-lg) var(--spacing-xl);
  box-shadow: var(--shadow-sm);
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
  gap: var(--spacing-lg);
}

.header-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.tool-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.tool-icon {
  font-size: var(--font-size-2xl);
  line-height: 1;
}

.tool-name {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin: 0;
}

.header-right {
  display: flex;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.header-btn {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--color-border-secondary);
  background: var(--color-background-secondary);
  border-radius: var(--border-radius-md);
  cursor: pointer;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
  text-decoration: none;
  transition: var(--transition-fast);
  white-space: nowrap;
  min-height: 36px;
}

.header-btn:hover {
  background: var(--color-background-hover);
  border-color: var(--color-border-hover);
  color: var(--color-text-primary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.header-btn:active {
  transform: translateY(0);
  box-shadow: var(--shadow-xs);
}

.header-btn:focus {
  outline: 2px solid var(--color-border-focus);
  outline-offset: 2px;
}

/* 请求构建器区域 */
.request-builder-area {
  background: var(--color-background-secondary);
  border-radius: var(--border-radius-lg);
  margin: var(--spacing-xl);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--color-border-primary);
  overflow: hidden;
}

.request-builder {
  display: flex;
  flex-direction: column;
  min-height: 400px;
}

/* 请求顶部行 */
.request-top-row {
  display: grid;
  grid-template-columns: auto 1fr auto;
  gap: var(--spacing-lg);
  padding: var(--spacing-xl);
  background: var(--color-background-tertiary);
  border-bottom: 1px solid var(--color-border-primary);
  align-items: end;
}

.method-selector {
  min-width: 120px;
}

.url-input-group {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-lg);
  min-width: 0; /* 防止grid溢出 */
}

.url-input {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  min-width: 0;
}

.action-buttons {
  display: flex;
  gap: var(--spacing-sm);
  align-items: end;
}

/* 表单控件 */
.form-label {
  display: block;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
  margin-bottom: var(--spacing-xs);
}

.form-control {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--color-border-secondary);
  border-radius: var(--border-radius-md);
  background: var(--color-background-secondary);
  color: var(--color-text-primary);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  transition: var(--transition-fast);
}

.form-control:focus {
  outline: none;
  border-color: var(--color-border-focus);
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.form-control:hover:not(:focus) {
  border-color: var(--color-border-hover);
}

.form-control.is-invalid {
  border-color: var(--color-error);
  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

.form-control.is-valid {
  border-color: var(--color-success);
  box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);
}

.invalid-feedback {
  display: none;
  font-size: var(--font-size-sm);
  color: var(--color-error);
  margin-top: var(--spacing-xs);
}

.form-control.is-invalid + .invalid-feedback {
  display: block;
}

/* Tab 系统 */
.request-tabs {
  display: flex;
  background: var(--color-background-secondary);
  border-bottom: 1px solid var(--color-border-primary);
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.request-tabs::-webkit-scrollbar {
  display: none;
}

.request-tab {
  position: relative;
  padding: var(--spacing-md) var(--spacing-xl);
  border: none;
  background: transparent;
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: var(--transition-fast);
  white-space: nowrap;
  min-height: 48px;
  display: flex;
  align-items: center;
}

.request-tab:hover:not(.active) {
  color: var(--color-text-primary);
  background: var(--color-background-hover);
}

.request-tab.active {
  color: var(--color-primary);
  background: var(--color-background-secondary);
}

.request-tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--color-primary);
  border-radius: var(--border-radius-sm) var(--border-radius-sm) 0 0;
}

.request-tab-content {
  flex: 1;
  position: relative;
}

.tab-pane {
  display: none;
  padding: var(--spacing-xl);
  animation: fadeInUp var(--transition-normal);
}

.tab-pane.active {
  display: block;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 按钮系统 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-lg);
  border: 1px solid transparent;
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-tight);
  text-decoration: none;
  cursor: pointer;
  transition: var(--transition-fast);
  white-space: nowrap;
  min-height: 36px;
  user-select: none;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

.btn-primary {
  background: var(--color-primary);
  border-color: var(--color-primary);
  color: var(--color-text-inverse);
}

.btn-primary:hover:not(:disabled) {
  background: var(--color-primary-hover);
  border-color: var(--color-primary-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background: var(--color-background-secondary);
  border-color: var(--color-border-secondary);
  color: var(--color-text-secondary);
}

.btn-secondary:hover:not(:disabled) {
  background: var(--color-background-hover);
  border-color: var(--color-border-hover);
  color: var(--color-text-primary);
}

.btn-success {
  background: var(--color-success);
  border-color: var(--color-success);
  color: var(--color-text-inverse);
}

.btn-outline {
  background: transparent;
  border-color: var(--color-border-secondary);
  color: var(--color-text-secondary);
}

.btn-outline:hover:not(:disabled) {
  background: var(--color-background-hover);
  color: var(--color-text-primary);
}

.compare-btn {
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-hover));
  border: none;
  color: var(--color-text-inverse);
  font-weight: var(--font-weight-semibold);
  padding: var(--spacing-md) var(--spacing-xl);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
}

.compare-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.compare-btn:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: var(--shadow-md);
}

/* 结果区域 */
.results-area {
  background: var(--color-background-secondary);
  border-radius: var(--border-radius-lg);
  margin: 0 var(--spacing-xl) var(--spacing-xl);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--color-border-primary);
  overflow: hidden;
  min-height: 500px;
}

.results-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: var(--spacing-3xl);
}

.placeholder-content {
  text-align: center;
  color: var(--color-text-tertiary);
}

.placeholder-icon {
  font-size: var(--font-size-3xl);
  margin-bottom: var(--spacing-lg);
}

.placeholder-content h3 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
  margin-bottom: var(--spacing-sm);
}

.placeholder-content p {
  font-size: var(--font-size-sm);
  color: var(--color-text-tertiary);
}

/* 结果标签 */
.results-tabs {
  display: flex;
  background: var(--color-background-tertiary);
  border-bottom: 1px solid var(--color-border-primary);
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.results-tabs::-webkit-scrollbar {
  display: none;
}

.result-tab {
  position: relative;
  padding: var(--spacing-md) var(--spacing-xl);
  border: none;
  background: transparent;
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: var(--transition-fast);
  white-space: nowrap;
  min-height: 48px;
  display: flex;
  align-items: center;
  min-width: 120px;
  justify-content: center;
}

.result-tab:hover:not(.active) {
  color: var(--color-text-primary);
  background: var(--color-background-hover);
}

.result-tab.active {
  color: var(--color-primary);
  background: var(--color-background-secondary);
}

.result-tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--color-primary);
  border-radius: var(--border-radius-sm) var(--border-radius-sm) 0 0;
}

/* 结果内容 */
.results-content {
  position: relative;
  min-height: 400px;
  flex: 1;
}

.result-panel {
  display: none;
  height: 100%;
  animation: fadeInUp var(--transition-normal);
}

.result-panel.active {
  display: block;
}

/* 工具底部 */
.tool-footer {
  background: var(--color-background-secondary, white);
  border-top: 1px solid var(--color-border-primary, #e0e0e0);
  padding: var(--spacing-sm, 12px) var(--spacing-lg, 24px);
  margin-top: auto;
}

.status-bar {
  max-width: 1400px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: var(--font-size-sm, 14px);
  color: var(--color-text-secondary, #666);
}

/* 通知样式 */
.notification {
  position: fixed;
  top: var(--spacing-xl);
  right: var(--spacing-xl);
  min-width: 320px;
  max-width: 480px;
  padding: var(--spacing-lg);
  border-radius: var(--border-radius-lg);
  color: var(--color-text-inverse);
  font-size: var(--font-size-sm);
  z-index: var(--z-toast);
  transform: translateX(calc(100% + var(--spacing-xl)));
  transition: var(--transition-normal);
  box-shadow: var(--shadow-xl);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.notification.show {
  transform: translateX(0);
}

.notification-content {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-md);
}

.notification-icon {
  flex-shrink: 0;
  margin-top: 2px;
}

.notification-text {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.notification-title {
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-base);
  line-height: var(--line-height-tight);
  margin: 0;
}

.notification-message {
  opacity: 0.9;
  line-height: var(--line-height-normal);
  margin: 0;
}

.notification-close {
  position: absolute;
  top: var(--spacing-sm);
  right: var(--spacing-sm);
  background: none;
  border: none;
  color: var(--color-text-inverse);
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--border-radius-sm);
  opacity: 0.7;
  transition: var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
}

.notification-close:hover {
  opacity: 1;
  background: rgba(255, 255, 255, 0.1);
  transform: scale(1.1);
}

.notification-close:focus {
  outline: 2px solid rgba(255, 255, 255, 0.5);
  outline-offset: 2px;
}

.notification-success {
  background: linear-gradient(135deg, var(--color-success), #20c997);
  border-color: rgba(255, 255, 255, 0.2);
}

.notification-error {
  background: linear-gradient(135deg, var(--color-error), #e74c3c);
  border-color: rgba(255, 255, 255, 0.2);
}

.notification-info {
  background: linear-gradient(135deg, var(--color-info), #3498db);
  border-color: rgba(255, 255, 255, 0.2);
}

/* 通知响应式设计 */
@media (max-width: 768px) {
  .notification {
    left: var(--spacing-md);
    right: var(--spacing-md);
    min-width: auto;
    max-width: none;
    transform: translateY(-100%);
    top: var(--spacing-md);
  }

  .notification.show {
    transform: translateY(0);
  }
}

@media (max-width: 480px) {
  .notification {
    left: var(--spacing-sm);
    right: var(--spacing-sm);
    padding: var(--spacing-md);
  }

  .notification-content {
    gap: var(--spacing-sm);
  }

  .notification-title {
    font-size: var(--font-size-sm);
  }

  .notification-message {
    font-size: var(--font-size-xs);
  }
}

/* 加载状态 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-lg);
  z-index: var(--z-modal);
  backdrop-filter: blur(4px);
  border-radius: inherit;
}

.request-builder-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-lg);
  padding: var(--spacing-3xl);
  color: var(--color-text-secondary);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--color-border-primary);
  border-top: 3px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* 小型加载器（用于按钮等） */
.loading-spinner.small {
  width: 16px;
  height: 16px;
  border-width: 2px;
}

/* 脉冲加载动画 */
.loading-pulse {
  width: 12px;
  height: 12px;
  background: var(--color-primary);
  border-radius: 50%;
  animation: pulse 1.5s ease-in-out infinite;
}

.loading-dots {
  display: flex;
  gap: var(--spacing-xs);
}

.loading-dots .loading-pulse:nth-child(1) {
  animation-delay: 0s;
}

.loading-dots .loading-pulse:nth-child(2) {
  animation-delay: 0.2s;
}

.loading-dots .loading-pulse:nth-child(3) {
  animation-delay: 0.4s;
}

/* 骨架屏加载 */
.skeleton {
  background: linear-gradient(90deg,
    var(--color-background-tertiary) 25%,
    var(--color-background-hover) 50%,
    var(--color-background-tertiary) 75%
  );
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: var(--border-radius-sm);
}

.skeleton-text {
  height: 1em;
  margin-bottom: var(--spacing-xs);
}

.skeleton-text.short {
  width: 60%;
}

.skeleton-text.medium {
  width: 80%;
}

.skeleton-text.long {
  width: 100%;
}

.skeleton-button {
  height: 36px;
  width: 120px;
}

.skeleton-input {
  height: 40px;
  width: 100%;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes skeleton-loading {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .request-builder-area,
  .results-area {
    margin-left: var(--spacing-lg);
    margin-right: var(--spacing-lg);
  }
}

@media (max-width: 1024px) {
  .request-top-row {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  .url-input-group {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }

  .action-buttons {
    justify-content: center;
  }

  .header-content {
    flex-wrap: wrap;
    gap: var(--spacing-md);
  }

  .header-actions {
    flex-wrap: wrap;
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .tool-header {
    padding: var(--spacing-md) var(--spacing-lg);
  }

  .header-content {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .header-actions {
    width: 100%;
    justify-content: center;
  }

  .request-builder-area,
  .results-area {
    margin: var(--spacing-md);
    border-radius: var(--border-radius-md);
  }

  .request-top-row {
    padding: var(--spacing-lg);
  }

  .tab-pane {
    padding: var(--spacing-lg);
  }

  .request-tabs {
    flex-wrap: wrap;
  }

  .request-tab {
    flex: 1;
    min-width: 80px;
  }

  .results-tabs {
    flex-wrap: wrap;
  }

  .result-tab {
    flex: 1;
    min-width: 100px;
  }
}

@media (max-width: 480px) {
  .tool-header {
    padding: var(--spacing-sm) var(--spacing-md);
  }

  .tool-name {
    font-size: var(--font-size-lg);
  }

  .tool-icon {
    font-size: var(--font-size-xl);
  }

  .header-btn {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-xs);
    min-height: 32px;
  }

  .request-builder-area,
  .results-area {
    margin: var(--spacing-sm);
  }

  .request-top-row {
    padding: var(--spacing-md);
    gap: var(--spacing-md);
  }

  .tab-pane {
    padding: var(--spacing-md);
  }

  .request-tab,
  .result-tab {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-xs);
    min-height: 40px;
  }

  .btn {
    padding: var(--spacing-xs) var(--spacing-md);
    font-size: var(--font-size-xs);
    min-height: 32px;
  }

  .compare-btn {
    padding: var(--spacing-sm) var(--spacing-lg);
  }

  .form-control {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-sm);
  }
}

/* 键值对编辑器 */
.key-value-editor {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.editor-header {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.editor-header h4 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin: 0;
}

.text-secondary {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin: 0;
}

.key-value-rows {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.key-value-row {
  display: grid;
  grid-template-columns: 1fr 1fr auto;
  gap: var(--spacing-sm);
  align-items: center;
}

.key-value-input {
  min-width: 0; /* 防止grid溢出 */
}

.remove-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border: 1px solid var(--color-border-secondary);
  background: var(--color-background-secondary);
  border-radius: var(--border-radius-md);
  color: var(--color-text-secondary);
  cursor: pointer;
  transition: var(--transition-fast);
  font-size: var(--font-size-sm);
}

.remove-btn:hover {
  background: var(--color-error);
  border-color: var(--color-error);
  color: var(--color-text-inverse);
  transform: scale(1.05);
}

.add-row-btn {
  align-self: flex-start;
  padding: var(--spacing-sm) var(--spacing-lg);
  border: 1px dashed var(--color-border-secondary);
  background: transparent;
  border-radius: var(--border-radius-md);
  color: var(--color-text-secondary);
  cursor: pointer;
  transition: var(--transition-fast);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.add-row-btn:hover {
  border-color: var(--color-primary);
  color: var(--color-primary);
  background: var(--color-primary-light);
}

/* cURL 解析器样式 */
.curl-parser {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

.curl-input-section {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.input-header {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.input-area {
  position: relative;
}

.curl-textarea {
  width: 100%;
  min-height: 120px;
  padding: var(--spacing-md);
  border: 1px solid var(--color-border-secondary);
  border-radius: var(--border-radius-md);
  background: var(--color-background-secondary);
  color: var(--color-text-primary);
  font-family: var(--font-family-mono);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-relaxed);
  resize: vertical;
  transition: var(--transition-fast);
}

.curl-textarea:focus {
  outline: none;
  border-color: var(--color-border-focus);
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.curl-textarea::placeholder {
  color: var(--color-text-tertiary);
  font-style: italic;
}

.input-actions {
  display: flex;
  gap: var(--spacing-sm);
  flex-wrap: wrap;
}

.curl-result-section {
  min-height: 100px;
}

.curl-result {
  border-radius: var(--border-radius-md);
  overflow: hidden;
}

.parse-result {
  padding: var(--spacing-lg);
  border-radius: var(--border-radius-md);
  border: 1px solid var(--color-border-primary);
}

.parse-result.loading {
  background: var(--color-background-tertiary);
  border-color: var(--color-border-secondary);
}

.parse-result.success {
  background: var(--color-background-secondary);
  border-color: var(--color-success);
  border-left: 4px solid var(--color-success);
}

.parse-result.error {
  background: var(--color-background-secondary);
  border-color: var(--color-error);
  border-left: 4px solid var(--color-error);
}

.result-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
}

.result-icon {
  font-size: var(--font-size-lg);
}

.result-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
}

.loading-indicator {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  color: var(--color-text-secondary);
}

.config-preview {
  margin-bottom: var(--spacing-lg);
}

.config-preview h5 {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-secondary);
  margin-bottom: var(--spacing-sm);
}

.config-details {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.config-item {
  font-size: var(--font-size-sm);
  color: var(--color-text-primary);
}

.config-item strong {
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
}

.config-item code {
  background: var(--color-background-tertiary);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  font-family: var(--font-family-mono);
  font-size: var(--font-size-xs);
  color: var(--color-text-primary);
}

.result-actions {
  display: flex;
  gap: var(--spacing-sm);
  flex-wrap: wrap;
}

.error-message {
  margin-bottom: var(--spacing-md);
}

.error-message p {
  color: var(--color-error);
  font-size: var(--font-size-sm);
  margin: 0;
}

.error-help {
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
}

.error-help p {
  margin: 0 0 var(--spacing-xs) 0;
}

.error-help ul {
  margin: 0;
  padding-left: var(--spacing-lg);
}

.error-help li {
  margin-bottom: var(--spacing-xs);
}

/* cURL语法高亮样式 */
.curl-editor-container {
  position: relative;
  border-radius: var(--border-radius-md);
  overflow: hidden;
}

.curl-highlight {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  padding: var(--spacing-md);
  font-family: var(--font-family-mono);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-relaxed);
  color: transparent;
  background: transparent;
  border: 1px solid transparent;
  white-space: pre-wrap;
  word-wrap: break-word;
  overflow: auto;
  pointer-events: none;
  z-index: 1;
}

.curl-textarea {
  position: relative;
  z-index: 2;
  background: transparent;
  color: var(--color-text-primary);
}

.curl-command {
  color: var(--color-primary);
  font-weight: var(--font-weight-bold);
}

.curl-option {
  color: var(--color-info);
  font-weight: var(--font-weight-medium);
}

.curl-method {
  color: var(--color-warning);
  font-weight: var(--font-weight-bold);
}

.curl-url {
  color: var(--color-success);
  text-decoration: underline;
}

.curl-header {
  color: #9c27b0; /* 紫色 */
}

.curl-data {
  color: #ff5722; /* 深橙色 */
}

.curl-string {
  color: var(--color-success);
}

.curl-continuation {
  color: var(--color-text-tertiary);
  font-weight: var(--font-weight-bold);
}

/* 深色主题下的语法高亮 */
@media (prefers-color-scheme: dark) {
  .curl-command {
    color: #64b5f6; /* 浅蓝色 */
  }

  .curl-option {
    color: #81c784; /* 浅绿色 */
  }

  .curl-method {
    color: #ffb74d; /* 浅橙色 */
  }

  .curl-url {
    color: #a5d6a7; /* 浅绿色 */
  }

  .curl-header {
    color: #ce93d8; /* 浅紫色 */
  }

  .curl-data {
    color: #ffab91; /* 浅橙红色 */
  }

  .curl-string {
    color: #c8e6c9; /* 浅绿色 */
  }
}

/* 容器查询支持 */
@container (max-width: 600px) {
  .url-input-group {
    grid-template-columns: 1fr;
  }

  .request-top-row {
    grid-template-columns: 1fr;
  }

  .key-value-row {
    grid-template-columns: 1fr;
    gap: var(--spacing-xs);
  }

  .input-actions {
    justify-content: center;
  }

  .result-actions {
    justify-content: center;
  }
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
  :root {
    /* 深色主题颜色覆盖 */
    --color-background-primary: #0d1117;
    --color-background-secondary: #161b22;
    --color-background-tertiary: #21262d;
    --color-background-hover: #30363d;
    --color-background-active: #21262d;

    --color-text-primary: #f0f6fc;
    --color-text-secondary: #8b949e;
    --color-text-tertiary: #6e7681;

    --color-border-primary: #30363d;
    --color-border-secondary: #21262d;
    --color-border-hover: #8b949e;

    /* 深色主题下的阴影 */
    --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.3);
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.4), 0 1px 2px rgba(0, 0, 0, 0.3);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.4), 0 2px 4px rgba(0, 0, 0, 0.3);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.5), 0 4px 6px rgba(0, 0, 0, 0.3);
    --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.6), 0 10px 10px rgba(0, 0, 0, 0.3);
  }

  /* 深色主题下的加载状态 */
  .loading-overlay {
    background: rgba(13, 17, 23, 0.9);
  }

  .loading-spinner {
    border-color: var(--color-border-primary);
    border-top-color: var(--color-primary);
  }

  /* 深色主题下的骨架屏 */
  .skeleton {
    background: linear-gradient(90deg,
      var(--color-background-tertiary) 25%,
      var(--color-background-hover) 50%,
      var(--color-background-tertiary) 75%
    );
  }

  /* 深色主题下的表单控件 */
  .form-control {
    background: var(--color-background-secondary);
    border-color: var(--color-border-secondary);
    color: var(--color-text-primary);
  }

  .form-control::placeholder {
    color: var(--color-text-tertiary);
  }

  /* 深色主题下的cURL文本区域 */
  .curl-textarea {
    background: var(--color-background-tertiary);
    border-color: var(--color-border-secondary);
    color: var(--color-text-primary);
  }

  .curl-textarea::placeholder {
    color: var(--color-text-tertiary);
  }

  /* 深色主题下的代码块 */
  .config-item code {
    background: var(--color-background-tertiary);
    color: var(--color-text-primary);
  }

  /* 深色主题下的通知 */
  .notification {
    border-color: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
  }

  /* 深色主题下的占位符图标 */
  .placeholder-content {
    color: var(--color-text-tertiary);
  }

  .placeholder-icon svg {
    stroke: var(--color-text-tertiary);
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  :root {
    /* 高对比度颜色 */
    --color-primary: #0066cc;
    --color-success: #008000;
    --color-error: #cc0000;
    --color-warning: #ff8800;
    --color-info: #0066cc;

    --color-text-primary: #000000;
    --color-text-secondary: #333333;
    --color-background-primary: #ffffff;
    --color-background-secondary: #ffffff;
    --color-border-primary: #000000;
    --color-border-secondary: #333333;
  }

  .api-diff-tool {
    border: 2px solid var(--color-border-primary);
  }

  .header-btn,
  .result-tab,
  .request-tab,
  .btn {
    border-width: 2px;
    font-weight: var(--font-weight-bold);
  }

  .result-tab.active,
  .request-tab.active {
    border-width: 3px;
    background: var(--color-primary);
    color: var(--color-text-inverse);
  }

  .form-control {
    border-width: 2px;
  }

  .form-control:focus {
    border-width: 3px;
    box-shadow: 0 0 0 2px var(--color-primary);
  }

  .notification {
    border: 3px solid var(--color-text-inverse);
  }

  /* 高对比度下的图标 */
  svg {
    stroke-width: 2.5;
  }

  /* 高对比度下的链接和按钮 */
  .btn:focus,
  .header-btn:focus,
  .request-tab:focus,
  .result-tab:focus {
    outline: 3px solid var(--color-primary);
    outline-offset: 2px;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  :root {
    --transition-fast: none;
    --transition-normal: none;
    --transition-slow: none;
  }

  .api-diff-tool *,
  .api-diff-tool *::before,
  .api-diff-tool *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    transition-delay: 0ms !important;
  }

  .loading-spinner {
    animation: none;
    border-top-color: var(--color-primary);
    border-right-color: transparent;
  }

  .loading-pulse {
    animation: none;
    opacity: 0.7;
  }

  .skeleton {
    animation: none;
    background: var(--color-background-tertiary);
  }

  /* 减少动画模式下的焦点指示器 */
  .tab-pane {
    animation: none;
  }

  .result-panel {
    animation: none;
  }

  .notification {
    transition: none;
    transform: translateX(0);
  }

  .notification.show {
    transform: translateX(0);
  }

  /* 保留重要的状态变化，但移除装饰性动画 */
  .btn:hover,
  .header-btn:hover {
    transform: none;
  }

  .remove-btn:hover {
    transform: none;
  }
}

/* 响应渲染器样式 */
.dual-response-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  gap: var(--spacing-lg);
}

.response-summary {
  padding: var(--spacing-lg);
  background: var(--color-background-tertiary);
  border-radius: var(--border-radius-md);
  border: 1px solid var(--color-border-primary);
}

.response-panels {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-lg);
  flex: 1;
  min-height: 0;
}

.response-panel {
  display: flex;
  flex-direction: column;
  background: var(--color-background-secondary);
  border: 1px solid var(--color-border-primary);
  border-radius: var(--border-radius-lg);
  overflow: hidden;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md) var(--spacing-lg);
  background: var(--color-background-tertiary);
  border-bottom: 1px solid var(--color-border-primary);
}

.panel-header h3 {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin: 0;
}

.panel-content {
  flex: 1;
  overflow: auto;
  padding: var(--spacing-lg);
  font-family: var(--font-family-mono);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-relaxed);
}

.sync-scroll-controls {
  display: flex;
  justify-content: center;
  padding: var(--spacing-md);
}

.sync-scroll-toggle {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  cursor: pointer;
  user-select: none;
}

.toggle-slider {
  position: relative;
  width: 44px;
  height: 24px;
  background: var(--color-border-secondary);
  border-radius: var(--border-radius-full);
  transition: var(--transition-fast);
}

.toggle-slider::before {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  width: 20px;
  height: 20px;
  background: var(--color-background-secondary);
  border-radius: 50%;
  transition: var(--transition-fast);
  box-shadow: var(--shadow-sm);
}

.sync-scroll-toggle input[type="checkbox"] {
  display: none;
}

.sync-scroll-toggle input[type="checkbox"]:checked + .toggle-slider {
  background: var(--color-primary);
}

.sync-scroll-toggle input[type="checkbox"]:checked + .toggle-slider::before {
  transform: translateX(20px);
}

.toggle-label {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
}

/* JSON折叠功能样式 */
.json-tree {
  font-family: var(--font-family-mono);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-normal);
}

.json-node {
  position: relative;
}

.json-key {
  color: var(--color-primary);
  font-weight: var(--font-weight-medium);
}

.json-string {
  color: var(--color-success);
}

.json-number {
  color: var(--color-info);
}

.json-boolean {
  color: var(--color-warning);
}

.json-null {
  color: var(--color-text-tertiary);
  font-style: italic;
}

.json-toggle {
  position: absolute;
  left: -20px;
  top: 0;
  width: 16px;
  height: 16px;
  border: none;
  background: none;
  cursor: pointer;
  color: var(--color-text-secondary);
  font-size: var(--font-size-xs);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--border-radius-sm);
  transition: var(--transition-fast);
}

.json-toggle:hover {
  background: var(--color-background-hover);
  color: var(--color-text-primary);
}

.json-toggle.collapsed::before {
  content: '▶';
}

.json-toggle.expanded::before {
  content: '▼';
}

.json-children {
  margin-left: var(--spacing-lg);
  border-left: 1px solid var(--color-border-secondary);
  padding-left: var(--spacing-md);
}

.json-children.collapsed {
  display: none;
}

.json-ellipsis {
  color: var(--color-text-tertiary);
  font-style: italic;
  cursor: pointer;
}

.json-ellipsis:hover {
  color: var(--color-text-secondary);
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .response-panels {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }

  .panel-content {
    padding: var(--spacing-md);
  }
}

@media (max-width: 768px) {
  .response-summary {
    padding: var(--spacing-md);
  }

  .panel-header {
    padding: var(--spacing-sm) var(--spacing-md);
  }

  .panel-header h3 {
    font-size: var(--font-size-sm);
  }

  .sync-scroll-controls {
    padding: var(--spacing-sm);
  }
}
