/**
 * API Diff Tool - 接口返回结果差异对比工具
 * 在新标签页中提供类似 Postman 的接口测试和双端对比功能
 */

import { BaseTool } from '../utils/tool-template';
import type {
  RequestConfig,
  DualExecutionResult,
  ComponentState,
  RequestExecutionState,
  ToolConfig as ApiDiffToolConfig
} from './api-diff/types/api-diff-types';

// 导入所有组件
import { RequestBuilder } from './api-diff/components/request-builder';
import { DualRequestExecutor } from './api-diff/components/dual-request-executor';
import { ResponseRenderer } from './api-diff/components/response-renderer';
import { DiffRenderer } from './api-diff/components/diff-renderer';
import { ConfigManager } from './api-diff/components/config-manager';

export class ApiDiffTool extends BaseTool {
  // 工具基本信息
  id = 'api-diff-tool';
  name = '接口 Diff 测试台';
  description = '页面版 Postman + 双端接口请求和可视化差异对比工具';
  icon = '🔄';
  categories = ['all'];
  version = { major: 1, minor: 0, patch: 0 };
  badge: 'new' = 'new';
  
  // 新标签页配置
  displayMode = 'newtab' as const;
  requiresFullscreen = true;
  
  // 传递给 newtab 的初始数据
  newtabData = {
    toolId: this.id,
    initialConfig: {
      theme: 'auto',
      layout: 'three-column',
      autoSave: true
    },
    permissions: ['storage', 'activeTab', 'clipboardWrite']
  };

  // 组件状态管理
  private componentStates: Map<string, ComponentState> = new Map();
  private requestExecutionState: RequestExecutionState = {
    executing: false,
    progress: 0,
    statusText: '就绪',
    cancellable: false
  };

  // 当前配置和响应数据
  private currentConfig: RequestConfig | null = null;
  private lastResponse: DualExecutionResult | null = null;

  // 组件实例
  private requestBuilder: RequestBuilder | null = null;
  private requestExecutor: DualRequestExecutor | null = null;
  private responseRenderer: ResponseRenderer | null = null;
  private diffRenderer: DiffRenderer | null = null;
  private configManager: ConfigManager | null = null;

  // DOM 容器引用
  private toolContainer: HTMLElement | null = null;
  private headerContainer: HTMLElement | null = null;
  private requestBuilderContainer: HTMLElement | null = null;
  private resultsContainer: HTMLElement | null = null;

  /**
   * 工具主入口方法
   * 在新标签页环境中初始化完整的工具界面
   */
  async action(): Promise<void> {
    try {
      console.log('🎬 API Diff Tool 开始执行');
      
      // 检查是否在 newtab 环境中
      if (!this.isInNewTabEnvironment()) {
        throw new Error('API Diff Tool 必须在新标签页环境中运行');
      }
      
      // 设置工具状态
      this.updateRequestExecutionState({
        executing: false,
        progress: 0,
        statusText: '初始化中...',
        cancellable: false
      });
      
      // 创建完整的工具界面
      await this.createToolInterface();
      
      // 初始化所有组件
      await this.initializeComponents();
      
      // 加载保存的配置
      await this.loadSavedConfiguration();
      
      // 设置事件监听器
      this.setupEventListeners();
      
      // 更新状态为就绪
      this.updateRequestExecutionState({
        executing: false,
        progress: 100,
        statusText: '就绪',
        cancellable: false
      });
      
      console.log('✅ API Diff Tool 初始化完成');

      // 验证组件是否正确加载
      this.validateComponentsLoaded();
      
    } catch (error) {
      console.error('❌ API Diff Tool 执行失败:', error);
      await this.showNotification('工具启动失败', `错误: ${(error as Error).message}`);
      throw error;
    }
  }

  /**
   * 新标签页环境初始化钩子
   */
  async onNewTabInit(): Promise<void> {
    console.log('🔧 API Diff Tool 在 New Tab 环境中初始化');
    
    try {
      // 加载工具专用样式
      await this.loadToolStyles();
      
      // 初始化全屏特定的资源
      await this.loadFullscreenResources();
      
      // 设置全局事件监听器
      this.setupGlobalEventListeners();
      
      // 初始化组件状态
      this.initializeComponentStates();
      
      console.log('✅ New Tab 环境初始化完成');
      
    } catch (error) {
      console.error('❌ New Tab 环境初始化失败:', error);
      throw error;
    }
  }

  /**
   * 新标签页环境销毁钩子
   */
  async onNewTabDestroy(): Promise<void> {
    console.log('🧹 API Diff Tool 在 New Tab 环境中清理');
    
    try {
      // 取消正在进行的请求
      await this.cancelCurrentRequests();
      
      // 清理资源
      await this.cleanupResources();
      
      // 移除事件监听器
      this.removeEventListeners();
      
      // 重置组件状态
      this.resetComponentStates();
      
      console.log('✅ New Tab 环境清理完成');
      
    } catch (error) {
      console.error('❌ New Tab 环境清理失败:', error);
    }
  }

  /**
   * 检查是否在新标签页环境中
   */
  private isInNewTabEnvironment(): boolean {
    return window.location.pathname.includes('toolpage.html') ||
           window.location.pathname.includes('toolpage/') ||
           window.location.pathname.includes('newtab.html') ||
           window.location.pathname.includes('newtab/');
  }

  /**
   * 验证组件是否正确加载
   */
  private validateComponentsLoaded(): void {
    console.log('🔍 验证组件加载状态...');

    // 检查DOM容器
    const containers = {
      'tool-container': document.getElementById('tool-container'),
      'request-builder-container': this.requestBuilderContainer,
      'results-container': this.resultsContainer
    };

    for (const [name, container] of Object.entries(containers)) {
      if (!container) {
        console.error(`❌ ${name} 容器未找到`);
      } else {
        console.log(`✅ ${name} 容器已加载`);
      }
    }

    // 检查组件实例
    const components = {
      'RequestBuilder': this.requestBuilder,
      'DualRequestExecutor': this.requestExecutor,
      'ResponseRenderer': this.responseRenderer,
      'DiffRenderer': this.diffRenderer,
      'ConfigManager': this.configManager
    };

    for (const [name, component] of Object.entries(components)) {
      if (!component) {
        console.error(`❌ ${name} 组件未初始化`);
      } else {
        console.log(`✅ ${name} 组件已初始化`);
      }
    }

    // 检查CSS样式是否加载
    const testElement = document.createElement('div');
    testElement.className = 'api-diff-tool';
    testElement.style.visibility = 'hidden';
    testElement.style.position = 'absolute';
    document.body.appendChild(testElement);

    const computedStyle = window.getComputedStyle(testElement);
    const hasStyles = computedStyle.fontFamily !== 'initial' && computedStyle.fontFamily !== '';

    document.body.removeChild(testElement);

    if (hasStyles) {
      console.log('✅ CSS样式已加载');
    } else {
      console.error('❌ CSS样式未正确加载');
    }

    console.log('🔍 组件验证完成');
  }

  /**
   * 直接加载API Diff样式
   */
  private async loadApiDiffStyles(): Promise<void> {
    // 检查是否已经加载
    const existingStyle = document.querySelector('style[data-api-diff-styles]');
    if (existingStyle) {
      return;
    }

    const style = document.createElement('style');
    style.setAttribute('data-api-diff-styles', 'true');
    style.textContent = this.getApiDiffCSS();
    document.head.appendChild(style);

    console.log('✅ API Diff样式内联加载成功');
  }

  /**
   * 获取API Diff CSS内容
   */
  private getApiDiffCSS(): string {
    return `
/* API Diff Tool Styles */
:root {
  --color-primary: #007bff;
  --color-primary-hover: #0056b3;
  --color-primary-light: #e3f2fd;
  --color-success: #28a745;
  --color-warning: #ffc107;
  --color-error: #dc3545;
  --color-info: #17a2b8;

  --color-background-primary: #f8f9fa;
  --color-background-secondary: #ffffff;
  --color-background-tertiary: #f1f3f4;
  --color-background-hover: #f8f9fa;
  --color-background-active: #e9ecef;

  --color-text-primary: #212529;
  --color-text-secondary: #6c757d;
  --color-text-tertiary: #adb5bd;
  --color-text-inverse: #ffffff;

  --color-border-primary: #dee2e6;
  --color-border-secondary: #e9ecef;
  --color-border-hover: #adb5bd;
  --color-border-focus: var(--color-primary);

  --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07), 0 2px 4px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1), 0 10px 10px rgba(0, 0, 0, 0.04);

  --font-family-base: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  --font-family-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;

  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;

  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;

  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 0.75rem;
  --spacing-lg: 1rem;
  --spacing-xl: 1.5rem;
  --spacing-2xl: 2rem;
  --spacing-3xl: 3rem;
  --spacing-4xl: 4rem;

  --border-radius-sm: 0.25rem;
  --border-radius-md: 0.375rem;
  --border-radius-lg: 0.5rem;
  --border-radius-xl: 0.75rem;
  --border-radius-full: 9999px;

  --transition-fast: 150ms ease;
  --transition-normal: 250ms ease;
  --transition-slow: 350ms ease;

  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}

@media (prefers-color-scheme: dark) {
  :root {
    --color-background-primary: #0d1117;
    --color-background-secondary: #161b22;
    --color-background-tertiary: #21262d;
    --color-background-hover: #30363d;
    --color-background-active: #21262d;

    --color-text-primary: #f0f6fc;
    --color-text-secondary: #8b949e;
    --color-text-tertiary: #6e7681;

    --color-border-primary: #30363d;
    --color-border-secondary: #21262d;
    --color-border-hover: #8b949e;
  }

  .parse-result.success {
    background: #0d2818;
    border-color: var(--color-success);
  }

  .parse-result.error {
    background: #2d1b1b;
    border-color: var(--color-error);
  }
}

.api-diff-tool * {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.api-diff-tool {
  font-family: var(--font-family-base);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  background: var(--color-background-primary);
  color: var(--color-text-primary);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.tool-header {
  background: var(--color-background-secondary);
  border-bottom: 1px solid var(--color-border-primary);
  padding: var(--spacing-lg) var(--spacing-xl);
  box-shadow: var(--shadow-sm);
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
  gap: var(--spacing-lg);
}

.header-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.tool-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.tool-icon {
  font-size: var(--font-size-2xl);
  line-height: 1;
}

.tool-name {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin: 0;
}

.header-right {
  display: flex;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.header-btn {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--color-border-secondary);
  background: var(--color-background-secondary);
  border-radius: var(--border-radius-md);
  cursor: pointer;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
  text-decoration: none;
  transition: var(--transition-fast);
  white-space: nowrap;
  min-height: 36px;
}

.header-btn:hover {
  background: var(--color-background-hover);
  border-color: var(--color-border-hover);
  color: var(--color-text-primary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.header-btn:active {
  transform: translateY(0);
  box-shadow: var(--shadow-xs);
}

.header-btn:focus {
  outline: 2px solid var(--color-border-focus);
  outline-offset: 2px;
}

.request-builder-area {
  background: var(--color-background-secondary);
  border-radius: var(--border-radius-lg);
  margin: var(--spacing-xl);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--color-border-primary);
  overflow: hidden;
}

.request-builder {
  display: flex;
  flex-direction: column;
  min-height: 400px;
}

.request-top-row {
  display: grid;
  grid-template-columns: auto 1fr auto;
  gap: var(--spacing-lg);
  padding: var(--spacing-xl);
  background: var(--color-background-tertiary);
  border-bottom: 1px solid var(--color-border-primary);
  align-items: end;
}

.method-selector {
  min-width: 120px;
}

.url-input-group {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-lg);
  min-width: 0;
}

.url-input {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  min-width: 0;
}

.action-buttons {
  display: flex;
  gap: var(--spacing-sm);
  align-items: end;
}

.form-label {
  display: block;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
  margin-bottom: var(--spacing-xs);
}

.form-control {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--color-border-secondary);
  border-radius: var(--border-radius-md);
  background: var(--color-background-secondary);
  color: var(--color-text-primary);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  transition: var(--transition-fast);
}

.form-control:focus {
  outline: none;
  border-color: var(--color-border-focus);
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.form-control:hover:not(:focus) {
  border-color: var(--color-border-hover);
}

.request-tabs {
  display: flex;
  background: var(--color-background-secondary);
  border-bottom: 1px solid var(--color-border-primary);
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.request-tabs::-webkit-scrollbar {
  display: none;
}

.request-tab {
  position: relative;
  padding: var(--spacing-md) var(--spacing-xl);
  border: none;
  background: transparent;
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: var(--transition-fast);
  white-space: nowrap;
  min-height: 48px;
  display: flex;
  align-items: center;
}

.request-tab:hover:not(.active) {
  color: var(--color-text-primary);
  background: var(--color-background-hover);
}

.request-tab.active {
  color: var(--color-primary);
  background: var(--color-background-secondary);
}

.request-tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--color-primary);
  border-radius: var(--border-radius-sm) var(--border-radius-sm) 0 0;
}

.request-tab-content {
  flex: 1;
  position: relative;
}

.tab-pane {
  display: none;
  padding: var(--spacing-xl);
  animation: fadeInUp var(--transition-normal);
}

.tab-pane.active {
  display: block;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-lg);
  border: 1px solid transparent;
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-tight);
  text-decoration: none;
  cursor: pointer;
  transition: var(--transition-fast);
  white-space: nowrap;
  min-height: 36px;
  user-select: none;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

.btn-primary {
  background: var(--color-primary);
  border-color: var(--color-primary);
  color: var(--color-text-inverse);
}

.btn-primary:hover:not(:disabled) {
  background: var(--color-primary-hover);
  border-color: var(--color-primary-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background: var(--color-background-secondary);
  border-color: var(--color-border-secondary);
  color: var(--color-text-secondary);
}

.btn-secondary:hover:not(:disabled) {
  background: var(--color-background-hover);
  border-color: var(--color-border-hover);
  color: var(--color-text-primary);
}

.btn-success {
  background: var(--color-success);
  border-color: var(--color-success);
  color: var(--color-text-inverse);
}

.btn-outline {
  background: transparent;
  border-color: var(--color-border-secondary);
  color: var(--color-text-secondary);
}

.btn-outline:hover:not(:disabled) {
  background: var(--color-background-hover);
  color: var(--color-text-primary);
}

.compare-btn {
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-hover));
  border: none;
  color: var(--color-text-inverse);
  font-weight: var(--font-weight-semibold);
  padding: var(--spacing-md) var(--spacing-xl);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
}

.compare-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.compare-btn:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: var(--shadow-md);
}

.results-area {
  background: var(--color-background-secondary);
  border-radius: var(--border-radius-lg);
  margin: 0 var(--spacing-xl) var(--spacing-xl);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--color-border-primary);
  overflow: hidden;
  min-height: 500px;
}

.results-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: var(--spacing-3xl);
}

.placeholder-content {
  text-align: center;
  color: var(--color-text-tertiary);
}

.placeholder-icon {
  font-size: var(--font-size-3xl);
  margin-bottom: var(--spacing-lg);
}

.placeholder-content h3 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
  margin-bottom: var(--spacing-sm);
}

.placeholder-content p {
  font-size: var(--font-size-sm);
  color: var(--color-text-tertiary);
}

.curl-parser {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

.curl-input-section {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.input-header {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.input-header h4 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin: 0;
}

.text-secondary {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin: 0;
}

.input-area {
  position: relative;
}

.curl-editor-container {
  position: relative;
  border-radius: var(--border-radius-md);
  overflow: hidden;
}

.curl-highlight {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  padding: var(--spacing-md);
  font-family: var(--font-family-mono);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-relaxed);
  color: transparent;
  background: transparent;
  border: 1px solid transparent;
  white-space: pre-wrap;
  word-wrap: break-word;
  overflow: auto;
  pointer-events: none;
  z-index: 1;
}

.curl-textarea {
  position: relative;
  z-index: 2;
  background: transparent;
  color: var(--color-text-primary);
  width: 100%;
  min-height: 120px;
  padding: var(--spacing-md);
  border: 1px solid var(--color-border-secondary);
  border-radius: var(--border-radius-md);
  font-family: var(--font-family-mono);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-relaxed);
  resize: vertical;
  transition: var(--transition-fast);
}

.curl-textarea:focus {
  outline: none;
  border-color: var(--color-border-focus);
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.curl-textarea::placeholder {
  color: var(--color-text-tertiary);
  font-style: italic;
}

.input-actions {
  display: flex;
  gap: var(--spacing-sm);
  flex-wrap: wrap;
}

.curl-command {
  color: var(--color-primary);
  font-weight: var(--font-weight-bold);
}

.curl-option {
  color: var(--color-info);
  font-weight: var(--font-weight-medium);
}

.curl-method {
  color: var(--color-warning);
  font-weight: var(--font-weight-bold);
}

.curl-url {
  color: var(--color-success);
  text-decoration: underline;
}

.curl-header {
  color: #9c27b0;
}

.curl-data {
  color: #ff5722;
}

.curl-string {
  color: var(--color-success);
}

.curl-continuation {
  color: var(--color-text-tertiary);
  font-weight: var(--font-weight-bold);
}

.parse-result {
  border-radius: var(--border-radius-md);
  padding: var(--spacing-lg);
  margin-top: var(--spacing-lg);
  border: 1px solid var(--color-border-secondary);
}

.parse-result.loading {
  background: var(--color-background-tertiary);
  border-color: var(--color-info);
}

.parse-result.success {
  background: var(--color-primary-light);
  border-color: var(--color-success);
}

.parse-result.error {
  background: #fef2f2;
  border-color: var(--color-error);
}

.result-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
}

.result-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

.result-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
}

.result-content {
  color: var(--color-text-secondary);
}

.config-preview {
  margin-bottom: var(--spacing-lg);
}

.config-preview h5 {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-sm);
}

.config-details {
  background: var(--color-background-secondary);
  border: 1px solid var(--color-border-secondary);
  border-radius: var(--border-radius-sm);
  padding: var(--spacing-md);
}

.config-item {
  margin-bottom: var(--spacing-xs);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-normal);
}

.config-item:last-child {
  margin-bottom: 0;
}

.config-item code {
  background: var(--color-background-tertiary);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  font-family: var(--font-family-mono);
  font-size: var(--font-size-xs);
  color: var(--color-text-primary);
}

.result-actions {
  display: flex;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-lg);
}

.error-message {
  margin-bottom: var(--spacing-md);
  padding: var(--spacing-md);
  background: var(--color-background-secondary);
  border-radius: var(--border-radius-sm);
  border-left: 4px solid var(--color-error);
}

.error-help {
  font-size: var(--font-size-sm);
}

.error-help ul {
  margin: var(--spacing-sm) 0 0 var(--spacing-lg);
  padding: 0;
}

.error-help li {
  margin-bottom: var(--spacing-xs);
  color: var(--color-text-secondary);
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid var(--color-border-secondary);
  border-top: 2px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.key-value-row {
  display: grid;
  grid-template-columns: 1fr 1fr auto;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-sm);
  align-items: center;
}

.key-value-row:last-child {
  margin-bottom: 0;
}

.key-input,
.value-input {
  min-width: 0;
}

.remove-row-btn {
  padding: var(--spacing-xs);
  min-height: 36px;
  width: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.remove-row-btn:hover {
  background: var(--color-error);
  border-color: var(--color-error);
  color: var(--color-text-inverse);
}

@media (max-width: 1024px) {
  .request-top-row {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  .url-input-group {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }

  .action-buttons {
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .request-builder-area,
  .results-area {
    margin: var(--spacing-md);
    border-radius: var(--border-radius-md);
  }

  .request-top-row {
    padding: var(--spacing-lg);
  }

  .tab-pane {
    padding: var(--spacing-lg);
  }
}

@media (max-width: 480px) {
  .request-builder-area,
  .results-area {
    margin: var(--spacing-sm);
  }

  .request-top-row {
    padding: var(--spacing-md);
    gap: var(--spacing-md);
  }

  .tab-pane {
    padding: var(--spacing-md);
  }

  .btn {
    padding: var(--spacing-xs) var(--spacing-md);
    font-size: var(--font-size-xs);
    min-height: 32px;
  }
}
    `;
  }

  /**
   * 创建工具界面
   */
  private async createToolInterface(): Promise<void> {
    const container = document.getElementById('tool-container');
    if (!container) {
      throw new Error('找不到工具容器元素');
    }

    this.toolContainer = container;
    
    // 创建主界面结构
    container.innerHTML = `
      <div class="api-diff-tool">
        <!-- Header Bar -->
        <header id="header-container" class="tool-header">
          <div class="header-content">
            <div class="header-left">
              <div class="tool-title">
                <span class="tool-icon" role="img" aria-label="API差异对比工具">${this.icon}</span>
                <h1 class="tool-name">${this.name}</h1>
              </div>
            </div>
            <div class="header-right">
              <nav class="header-actions" role="navigation" aria-label="工具操作">
                <button id="history-btn" class="header-btn" title="查看历史配置" aria-label="查看历史配置">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M3 3v5h5"/>
                    <path d="M3.05 13A9 9 0 1 0 6 5.3L3 8"/>
                    <path d="M12 7v5l4 2"/>
                  </svg>
                  History
                </button>
                <button id="import-btn" class="header-btn" title="导入配置" aria-label="导入配置">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                    <polyline points="7,10 12,15 17,10"/>
                    <line x1="12" y1="15" x2="12" y2="3"/>
                  </svg>
                  Import
                </button>
                <button id="export-btn" class="header-btn" title="导出配置" aria-label="导出配置">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M3 15v4a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-4"/>
                    <polyline points="17,8 12,3 7,8"/>
                    <line x1="12" y1="3" x2="12" y2="15"/>
                  </svg>
                  Export
                </button>
                <button id="settings-btn" class="header-btn" title="设置" aria-label="设置">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <circle cx="12" cy="12" r="3"/>
                    <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1"/>
                  </svg>
                  Settings
                </button>
              </nav>
            </div>
          </div>
        </header>

        <!-- Request Builder Area -->
        <main>
          <section id="request-builder-container" class="request-builder-area" aria-label="请求配置区域">
            <div class="request-builder-loading" role="status" aria-live="polite">
              <div class="loading-spinner" aria-hidden="true"></div>
              <span>正在加载请求构建器...</span>
            </div>
          </section>

          <!-- Results Area -->
          <section id="results-container" class="results-area" aria-label="结果展示区域">
            <div class="results-placeholder">
              <div class="placeholder-content">
                <div class="placeholder-icon" role="img" aria-label="准备就绪">
                  <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M6 2L3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4z"/>
                    <line x1="3" y1="6" x2="21" y2="6"/>
                    <path d="M16 10a4 4 0 0 1-8 0"/>
                  </svg>
                </div>
                <h3>准备就绪</h3>
                <p>配置请求参数并点击"对比"按钮开始测试</p>
              </div>
            </div>
          </section>
        </main>
      </div>
    `;

    // 获取容器引用
    this.headerContainer = document.getElementById('header-container');
    this.requestBuilderContainer = document.getElementById('request-builder-container');
    this.resultsContainer = document.getElementById('results-container');
  }

  /**
   * 初始化所有组件
   */
  private async initializeComponents(): Promise<void> {
    console.log('📦 正在初始化组件...');

    try {
      // 1. 初始化配置管理器
      this.setComponentState('config-manager', { loading: true, initialized: false });
      this.configManager = new ConfigManager();
      this.setComponentState('config-manager', { loading: false, initialized: true });

      // 2. 初始化请求构建器
      this.setComponentState('request-builder', { loading: true, initialized: false });
      if (!this.requestBuilderContainer) {
        throw new Error('请求构建器容器未找到');
      }

      this.requestBuilder = new RequestBuilder(this.requestBuilderContainer);
      this.requestBuilder.setCallbacks({
        onConfigChange: (config) => {
          this.currentConfig = config;
          this.handleConfigChange(config);
        },
        onValidationChange: (isValid, errors) => {
          this.handleValidationChange(isValid, errors);
        }
      });
      this.setComponentState('request-builder', { loading: false, initialized: true });

      // 3. 初始化请求执行器
      this.setComponentState('dual-request-executor', { loading: true, initialized: false });
      this.requestExecutor = new DualRequestExecutor();
      this.requestExecutor.addEventListener('status-change', (event) => {
        this.handleExecutionStatusChange(event.data.newStatus);
      });
      this.requestExecutor.addEventListener('completed', (event) => {
        this.handleExecutionCompleted(event.data);
      });
      this.requestExecutor.addEventListener('error', (event) => {
        this.handleExecutionError(event.data.error);
      });
      this.setComponentState('dual-request-executor', { loading: false, initialized: true });

      // 4. 初始化响应渲染器和差异渲染器
      this.setComponentState('response-renderer', { loading: true, initialized: false });
      this.setComponentState('diff-renderer', { loading: true, initialized: false });

      // 创建结果容器的子容器
      if (this.resultsContainer) {
        this.resultsContainer.innerHTML = `
          <div class="results-tabs" role="tablist" aria-label="结果展示选项">
            <button
              class="result-tab active"
              data-tab="response"
              role="tab"
              aria-selected="true"
              aria-controls="response-panel"
              id="response-tab-button"
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                <polyline points="14,2 14,8 20,8"/>
                <line x1="16" y1="13" x2="8" y2="13"/>
                <line x1="16" y1="17" x2="8" y2="17"/>
                <polyline points="10,9 9,9 8,9"/>
              </svg>
              响应对比
            </button>
            <button
              class="result-tab"
              data-tab="diff"
              role="tab"
              aria-selected="false"
              aria-controls="diff-panel"
              id="diff-tab-button"
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3"/>
                <path d="M9 9h6v6H9z"/>
              </svg>
              差异分析
            </button>
          </div>
          <div class="results-content">
            <div
              id="response-panel"
              class="result-panel active"
              role="tabpanel"
              aria-labelledby="response-tab-button"
              tabindex="0"
            ></div>
            <div
              id="diff-panel"
              class="result-panel"
              role="tabpanel"
              aria-labelledby="diff-tab-button"
              tabindex="0"
              hidden
            ></div>
          </div>
        `;

        const responsePanel = document.getElementById('response-panel');
        const diffPanel = document.getElementById('diff-panel');

        if (responsePanel && diffPanel) {
          this.responseRenderer = new ResponseRenderer(responsePanel);
          this.diffRenderer = new DiffRenderer(diffPanel);
        }
      }

      this.setComponentState('response-renderer', { loading: false, initialized: true });
      this.setComponentState('diff-renderer', { loading: false, initialized: true });

      // 5. 绑定结果标签切换事件
      this.bindResultTabEvents();

      console.log('✅ 所有组件初始化完成');

    } catch (error) {
      console.error('❌ 组件初始化失败:', error);
      throw error;
    }
  }

  /**
   * 加载工具样式
   */
  private async loadToolStyles(): Promise<void> {
    // 使用样式管理器加载样式，避免冲突
    const { styleManager } = await import('../utils/style-manager');

    try {
      // 确保基础样式模块已加载
      await styleManager.loadModule('design-tokens');
      await styleManager.loadModule('components');
      await styleManager.loadModule('layout');

      // 直接加载 API Diff Tool 专用样式
      await this.loadApiDiffStyles();

      console.log('✅ API Diff Tool 样式加载成功');
    } catch (error) {
      console.warn('❌ API Diff Tool 样式加载失败:', error);
      throw error;
    }
  }

  /**
   * 加载全屏资源
   */
  private async loadFullscreenResources(): Promise<void> {
    // 这里可以加载大型资源、字体、图标等
    console.log('📦 加载全屏资源...');
  }

  /**
   * 设置全局事件监听器
   */
  private setupGlobalEventListeners(): void {
    // 键盘快捷键
    document.addEventListener('keydown', this.handleKeyboardShortcuts.bind(this));
    
    // 窗口大小变化
    window.addEventListener('resize', this.handleWindowResize.bind(this));
    
    // 页面卸载前保存
    window.addEventListener('beforeunload', this.handleBeforeUnload.bind(this));
  }

  /**
   * 设置工具事件监听器
   */
  private setupEventListeners(): void {
    // Header 按钮事件
    this.headerContainer?.addEventListener('click', this.handleHeaderActions.bind(this));

    // 监听请求对比事件
    this.handleRequestCompareEvent = this.handleRequestCompareEvent.bind(this);
    document.addEventListener('request-compare', this.handleRequestCompareEvent);
  }

  /**
   * 处理请求对比事件
   */
  private handleRequestCompareEvent(event: any): void {
    this.handleRequestCompare(event.detail);
  }

  /**
   * 绑定结果标签切换事件
   */
  private bindResultTabEvents(): void {
    const resultTabs = document.querySelectorAll('.result-tab');
    resultTabs.forEach(tab => {
      tab.addEventListener('click', (e) => {
        const target = e.target as HTMLElement;
        const tabName = target.getAttribute('data-tab');
        if (tabName) {
          this.switchResultTab(tabName);
        }
      });
    });
  }

  /**
   * 切换结果标签
   */
  private switchResultTab(tabName: string): void {
    // 更新标签状态和可访问性属性
    const tabs = document.querySelectorAll('.result-tab');
    tabs.forEach(tab => {
      const isActive = tab.getAttribute('data-tab') === tabName;

      if (isActive) {
        tab.classList.add('active');
        tab.setAttribute('aria-selected', 'true');
        tab.setAttribute('tabindex', '0');
      } else {
        tab.classList.remove('active');
        tab.setAttribute('aria-selected', 'false');
        tab.setAttribute('tabindex', '-1');
      }
    });

    // 更新面板显示和可访问性属性
    const panels = document.querySelectorAll('.result-panel');
    panels.forEach(panel => {
      const isActive = panel.id === `${tabName}-panel`;

      if (isActive) {
        panel.classList.add('active');
        panel.removeAttribute('hidden');
        panel.setAttribute('tabindex', '0');
        // 触发重新渲染动画
        (panel as HTMLElement).style.animation = 'none';
        panel.scrollTop; // 强制重排
        (panel as HTMLElement).style.animation = '';
      } else {
        panel.classList.remove('active');
        panel.setAttribute('hidden', '');
        panel.setAttribute('tabindex', '-1');
      }
    });

    // 触发自定义事件
    document.dispatchEvent(new CustomEvent('result-tab-changed', {
      detail: { activeTab: tabName },
      bubbles: true
    }));
  }

  /**
   * 处理 Header 按钮点击
   */
  private handleHeaderActions(event: Event): void {
    const target = event.target as HTMLElement;
    const buttonId = target.id;

    switch (buttonId) {
      case 'history-btn':
        this.showHistoryDialog();
        break;
      case 'import-btn':
        this.showImportDialog();
        break;
      case 'export-btn':
        this.showExportDialog();
        break;
      case 'settings-btn':
        this.showSettingsDialog();
        break;
    }
  }

  /**
   * 处理键盘快捷键
   */
  private handleKeyboardShortcuts(event: KeyboardEvent): void {
    // Ctrl/Cmd + Enter: 执行请求
    if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {
      event.preventDefault();
      // 这里将在后续任务中实现
      console.log('快捷键: 执行请求');
    }
    
    // Ctrl/Cmd + S: 保存配置
    if ((event.ctrlKey || event.metaKey) && event.key === 's') {
      event.preventDefault();
      this.saveCurrentConfiguration();
    }
  }

  /**
   * 处理窗口大小变化
   */
  private handleWindowResize(): void {
    // 响应式布局调整
    console.log('窗口大小变化，调整布局');
  }

  /**
   * 处理页面卸载前事件
   */
  private handleBeforeUnload(event: BeforeUnloadEvent): void {
    if (this.requestExecutionState.executing) {
      event.preventDefault();
      // 现代浏览器会显示标准的确认对话框
      return;
    }
  }

  /**
   * 处理配置变化
   */
  private handleConfigChange(config: RequestConfig): void {
    this.currentConfig = config;
    console.log('配置已更新:', config);
  }

  /**
   * 处理验证状态变化
   */
  private handleValidationChange(isValid: boolean, errors: Record<string, string>): void {
    // 更新UI状态，显示验证错误等
    console.log('验证状态:', isValid, errors);
  }

  /**
   * 处理执行状态变化
   */
  private handleExecutionStatusChange(status: string): void {
    this.updateRequestExecutionState({
      statusText: `执行状态: ${status}`
    });
  }

  /**
   * 处理执行完成
   */
  private handleExecutionCompleted(result: DualExecutionResult): void {
    this.lastResponse = result;
    this.renderResults(result);

    // 保存执行历史
    if (this.configManager && this.currentConfig) {
      this.configManager.saveHistory('temp', result);
    }
  }

  /**
   * 处理执行错误
   */
  private handleExecutionError(error: string): void {
    this.updateRequestExecutionState({
      executing: false,
      statusText: `执行失败: ${error}`
    });
    this.showNotification('执行失败', error);
  }

  /**
   * 处理请求对比
   */
  private async handleRequestCompare(config: RequestConfig): Promise<void> {
    if (!this.requestExecutor || this.requestExecutionState.executing) {
      return;
    }

    try {
      this.updateRequestExecutionState({
        executing: true,
        progress: 0,
        statusText: '正在执行请求...',
        cancellable: true
      });

      const result = await this.requestExecutor.execute(config);
      this.handleExecutionCompleted(result);

    } catch (error) {
      this.handleExecutionError(error instanceof Error ? error.message : String(error));
    } finally {
      this.updateRequestExecutionState({
        executing: false,
        cancellable: false
      });
    }
  }

  /**
   * 渲染结果
   */
  private renderResults(result: DualExecutionResult): void {
    // 渲染响应对比
    if (this.responseRenderer) {
      this.responseRenderer.renderDualResponse(result);
    }

    // 渲染差异分析
    if (this.diffRenderer) {
      this.diffRenderer.renderDiff(result);
    }

    // 更新状态
    this.updateRequestExecutionState({
      statusText: `对比完成 - 耗时 ${result.totalDuration}ms`,
      progress: 100
    });
  }

  /**
   * 显示历史对话框
   */
  private async showHistoryDialog(): Promise<void> {
    if (!this.configManager) return;

    try {
      const histories = await this.configManager.getHistory();
      // 这里应该显示历史记录对话框
      console.log('历史记录:', histories);
      this.showNotification('信息', '历史记录功能开发中...');
    } catch (error) {
      this.showNotification('错误', '加载历史记录失败');
    }
  }

  /**
   * 显示导入对话框
   */
  private showImportDialog(): void {
    // 创建文件输入元素
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = async (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file && this.configManager) {
        try {
          const text = await file.text();
          const result = await this.configManager.importConfig(text);
          if (result.success) {
            this.showNotification('成功', '配置导入成功');
            // 重新加载配置到界面
            if (result.configId && this.requestBuilder) {
              const loadResult = await this.configManager.loadConfig(result.configId);
              if (loadResult.success && loadResult.config) {
                this.requestBuilder.setRequestConfig(loadResult.config);
              }
            }
          } else {
            this.showNotification('错误', result.error || '导入失败');
          }
        } catch (error) {
          this.showNotification('错误', '文件读取失败');
        }
      }
    };
    input.click();
  }

  /**
   * 显示导出对话框
   */
  private async showExportDialog(): Promise<void> {
    if (!this.configManager || !this.currentConfig) {
      this.showNotification('错误', '没有可导出的配置');
      return;
    }

    try {
      // 先保存当前配置
      const name = prompt('请输入配置名称:') || '未命名配置';
      const saveResult = await this.configManager.saveConfig(this.currentConfig, name);

      if (saveResult.success && saveResult.configId) {
        const exportResult = await this.configManager.exportConfig(saveResult.configId);
        if (exportResult.success && exportResult.data) {
          // 下载文件
          const blob = new Blob([exportResult.data], { type: 'application/json' });
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = `${name}.json`;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          URL.revokeObjectURL(url);

          this.showNotification('成功', '配置导出成功');
        } else {
          this.showNotification('错误', exportResult.error || '导出失败');
        }
      } else {
        this.showNotification('错误', saveResult.error || '保存配置失败');
      }
    } catch (error) {
      this.showNotification('错误', '导出配置失败');
    }
  }

  /**
   * 显示设置对话框
   */
  private async showSettingsDialog(): Promise<void> {
    if (!this.configManager) return;

    try {
      const settings = await this.configManager.getSettings();
      // 这里应该显示设置对话框
      console.log('当前设置:', settings);
      this.showNotification('信息', '设置功能开发中...');
    } catch (error) {
      this.showNotification('错误', '加载设置失败');
    }
  }

  /**
   * 加载保存的配置
   */
  private async loadSavedConfiguration(): Promise<void> {
    if (!this.configManager || !this.requestBuilder) return;

    try {
      const configs = await this.configManager.getRecentConfigs(10);
      if (configs.length === 0) {
        this.showNotification('信息', '没有保存的配置');
        return;
      }

      // 简单实现：加载最近的配置
      // 实际应该显示配置选择对话框
      const config = configs[0];
      this.requestBuilder.setRequestConfig(config.config);
      this.showNotification('成功', `已加载配置: ${config.name}`);
    } catch (error) {
      this.showNotification('错误', '加载配置失败');
    }
  }

  /**
   * 保存当前配置
   */
  private async saveCurrentConfiguration(): Promise<void> {
    if (!this.configManager || !this.currentConfig) {
      this.showNotification('错误', '没有可保存的配置');
      return;
    }

    try {
      const name = prompt('请输入配置名称:');
      if (!name) return;

      const result = await this.configManager.saveConfig(this.currentConfig, name);
      if (result.success) {
        this.showNotification('成功', '配置保存成功');
      } else {
        this.showNotification('错误', result.error || '保存失败');
      }
    } catch (error) {
      this.showNotification('错误', '保存配置失败');
    }
  }

  /**
   * 取消当前请求
   */
  private async cancelCurrentRequests(): Promise<void> {
    if (this.requestExecutor && this.requestExecutor.canCancel()) {
      this.requestExecutor.cancel();
      this.updateRequestExecutionState({
        executing: false,
        cancellable: false,
        statusText: '请求已取消'
      });
      this.showNotification('信息', '请求已取消');
    }
  }

  /**
   * 清理资源
   */
  private async cleanupResources(): Promise<void> {
    // 取消正在进行的请求
    if (this.requestExecutor) {
      this.requestExecutor.cancel();
      this.requestExecutor.destroy();
    }

    // 清理配置管理器
    if (this.configManager) {
      this.configManager.destroy();
    }

    // 清理组件实例
    this.requestBuilder = null;
    this.requestExecutor = null;
    this.responseRenderer = null;
    this.diffRenderer = null;
    this.configManager = null;

    console.log('资源清理完成');
  }

  /**
   * 移除事件监听器
   */
  private removeEventListeners(): void {
    // 移除全局事件监听器
    document.removeEventListener('request-compare', this.handleRequestCompareEvent.bind(this));
    console.log('事件监听器已移除');
  }

  /**
   * 显示通知
   */
  protected async showNotification(title: string, message: string, type: 'success' | 'error' | 'info' = 'info'): Promise<void> {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.setAttribute('role', 'alert');
    notification.setAttribute('aria-live', 'assertive');

    // 选择合适的图标
    let iconSvg = '';
    switch (type) {
      case 'success':
        iconSvg = `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <polyline points="20,6 9,17 4,12"/>
        </svg>`;
        break;
      case 'error':
        iconSvg = `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <circle cx="12" cy="12" r="10"/>
          <line x1="15" y1="9" x2="9" y2="15"/>
          <line x1="9" y1="9" x2="15" y2="15"/>
        </svg>`;
        break;
      case 'info':
      default:
        iconSvg = `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <circle cx="12" cy="12" r="10"/>
          <line x1="12" y1="16" x2="12" y2="12"/>
          <line x1="12" y1="8" x2="12.01" y2="8"/>
        </svg>`;
        break;
    }

    notification.innerHTML = `
      <div class="notification-content">
        <div class="notification-icon" aria-hidden="true">${iconSvg}</div>
        <div class="notification-text">
          <div class="notification-title">${title}</div>
          <div class="notification-message">${message}</div>
        </div>
      </div>
      <button class="notification-close" aria-label="关闭通知">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <line x1="18" y1="6" x2="6" y2="18"/>
          <line x1="6" y1="6" x2="18" y2="18"/>
        </svg>
      </button>
    `;

    document.body.appendChild(notification);

    // 绑定关闭事件
    const closeBtn = notification.querySelector('.notification-close');
    const closeNotification = () => {
      notification.classList.remove('show');
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
      }, 300);
    };

    closeBtn?.addEventListener('click', closeNotification);

    // 键盘支持
    notification.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        closeNotification();
      }
    });

    // 显示动画
    requestAnimationFrame(() => {
      notification.classList.add('show');
    });

    // 自动关闭（错误消息显示更长时间）
    const autoCloseDelay = type === 'error' ? 8000 : 5000;
    setTimeout(() => {
      if (notification.parentNode && notification.classList.contains('show')) {
        closeNotification();
      }
    }, autoCloseDelay);
  }

  /**
   * 组件状态管理方法
   */
  private initializeComponentStates(): void {
    this.componentStates.clear();
  }

  private resetComponentStates(): void {
    this.componentStates.clear();
  }

  private setComponentState(componentId: string, state: Partial<ComponentState>): void {
    const currentState = this.componentStates.get(componentId) || {
      loading: false,
      initialized: false
    };
    
    this.componentStates.set(componentId, { ...currentState, ...state });
  }



  /**
   * 请求执行状态管理
   */
  private updateRequestExecutionState(state: Partial<RequestExecutionState>): void {
    this.requestExecutionState = { ...this.requestExecutionState, ...state };
    
    // 触发状态更新事件
    document.dispatchEvent(new CustomEvent('request-execution-state-changed', {
      detail: this.requestExecutionState
    }));
  }

  /**
   * 获取当前请求执行状态
   */
  public getRequestExecutionState(): RequestExecutionState {
    return { ...this.requestExecutionState };
  }
}
