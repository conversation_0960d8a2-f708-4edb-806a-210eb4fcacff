/**
 * XUID切换助手
 * 专业的XUID身份切换工具，支持快速切换Cookie中的XUID值进行调试
 */

import { BaseTool } from '../utils/tool-template';
import { XuidStorageManager, XuidData } from '../utils/xuid-storage-manager';
import { XuidCookieManager, CurrentXuidInfo, ClusterStatus } from '../utils/xuid-cookie-manager';
import { XuidNetworkMonitor, UserInfo } from '../utils/xuid-network-monitor';
import { XuidUIManager } from '../utils/xuid-ui-manager';
import { styleManager } from '../utils/style-manager';
import { notificationManager } from '../utils/notification-manager';

// 手机号查询API响应类型
interface PhoneQueryResponse {
  errNo: number;
  errMsg?: string;
  data: {
    detail: Array<{
      userId: string;
      name: string;
      phone: string;
      userName: string;
      type?: string;
    }>;
  };
}

// 姓名查询API响应类型
interface NameQueryResponse {
  errNo: number;
  errMsg?: string;
  data: {
    list: Array<{
      staffUid: string;
      staffName: string;
      staffType?: string;
      staffStatus?: string;
      organization?: any[];
    }>;
    total: number;
    offset: number;
  };
}

// 资产ID转换API响应类型
interface AssetConvertResponse {
  errNo: number;
  errMsg?: string;
  data: {
    xuid: string;
  };
}

export class XuidTool extends BaseTool {
  id = 'xuid';
  name = 'XUID切换助手';
  description = 'XUID身份切换工具，支持快速切换Cookie中的XUID值进行调试';
  icon = '🕹️';
  categories = ['all'];
  version = { major: 2, minor: 0, patch: 0 };

  // 管理器实例
  private storageManager: XuidStorageManager;
  private cookieManager: XuidCookieManager;
  private networkMonitor: XuidNetworkMonitor;
  private uiManager: XuidUIManager;

  // 状态变量
  private currentTab: any | null = null;
  private currentDomain: string = '';
  private currentXuid: string = '';
  private currentXuidCookieName: string = '';
  private currentCluster: 'tips' | 'small' | 'stable' | 'online' | 'unknown' = 'online';
  private xuidData: XuidData | null = null;
  private currentApiUserInfo: UserInfo | null = null;

  // 异步检测相关
  private lastKnownAssistantUid: string | null = null;

  constructor() {
    super();

    // 初始化管理器
    this.storageManager = new XuidStorageManager();
    this.cookieManager = new XuidCookieManager();
    this.networkMonitor = new XuidNetworkMonitor();
    this.uiManager = new XuidUIManager(this.networkMonitor);

    // 设置UI事件处理器
    this.uiManager.setEventHandlers({
      onClusterSwitch: this.handleClusterSwitch.bind(this),
      onXuidSwitch: this.handleXuidSwitch.bind(this),
      onXuidDelete: this.handleXuidDelete.bind(this)
    });
  }

  async action(): Promise<void> {
    try {
      // 清除之前的选中状态（防止页面刷新后状态残留）
      this.uiManager.clearSelection();
      console.log('已清除之前的选中状态');

      // 加载XUID样式
      await this.loadXuidStyles();

      // 快速初始化基本信息（不包括网络请求）
      await this.quickInitialize();

      // 立即创建并显示模态框
      const modal = this.createXuidModal();
      document.body.appendChild(modal);

      // 初始化UI组件
      this.initializeUIComponents(modal);

      // 绑定事件
      this.bindModalEvents(modal);

      // 显示加载状态
      this.showLoadingState();

      // 异步完成剩余初始化和数据加载
      this.completeInitializationAsync();

      // 检测assistantUid是否发生变化
      this.checkAssistantUidChangeOnOpen();

    } catch (error) {
      console.error('XUID切换助手启动失败:', error);
      await this.showNotification('错误', 'XUID切换助手启动失败');
    }
  }

  /**
   * 注册XUID样式模块
   */
  private registerXuidStyleModule(): void {
    // 注册XUID样式模块
    styleManager.registerModule({
      name: 'xuid',
      path: '/styles/xuid.css',
      dependencies: ['design-tokens', 'components']
    });
  }




  /**
   * 加载XUID样式文件
   */
  private async loadXuidStyles(): Promise<void> {
    try {
      // 注册XUID样式模块
      this.registerXuidStyleModule();

      // 使用StyleManager加载样式
      await styleManager.loadModule('xuid');
      console.log('✅ XUID样式模块加载成功');
    } catch (error) {
      console.error('❌ XUID样式模块加载失败:', error);
      throw error; // 让调用者处理错误
    }
  }

  /**
   * 快速初始化（不包括网络请求）
   */
  private async quickInitialize(): Promise<void> {
    try {
      console.log('开始快速初始化XUID切换助手...');

      // 获取当前标签页信息
      await this.getCurrentTabInfo();
      console.log('当前标签页:', this.currentTab?.url);

      // 加载存储的数据
      await this.loadStoredData();
      console.log('数据加载完成');

      // 获取当前XUID
      await this.getCurrentXuidInfo();
      console.log('当前XUID:', this.currentXuid);

      // 检测集群状态
      await this.detectClusterStatus();
      console.log('当前集群:', this.currentCluster);

      console.log('XUID切换助手快速初始化完成');

    } catch (error) {
      console.error('快速初始化失败:', error);
      throw error;
    }
  }

  /**
   * 异步完成剩余初始化
   */
  private async completeInitializationAsync(): Promise<void> {
    try {
      console.log('开始异步完成初始化...');

      // 检查是否需要清除缓存（XUID可能已经改变）
      const cachedUserInfo = this.networkMonitor.getCachedUserInfo(this.currentXuid);
      if (cachedUserInfo && cachedUserInfo.xuid !== this.currentXuid) {
        console.log('检测到XUID变化，清除缓存:', {
          cached: cachedUserInfo.xuid,
          current: this.currentXuid
        });
        this.networkMonitor.clearCache();
      }

      // 并行获取用户信息和预加载资产信息
      console.log('开始并行加载用户信息和资产信息...');
      const [userInfo, assetInfo] = await Promise.allSettled([
        this.refreshUserInfo(),
        this.preloadAssetInfo()
      ]);

      if (userInfo.status === 'fulfilled') {
        console.log('用户信息初始化完成');
      } else {
        console.warn('用户信息初始化失败:', userInfo.reason);
      }

      if (assetInfo.status === 'fulfilled') {
        console.log('资产信息预加载完成');
      } else {
        console.warn('资产信息预加载失败:', assetInfo.reason);
      }

      // 隐藏加载状态并更新显示
      this.hideLoadingState();
      await this.updateDisplay();

      console.log('XUID切换助手异步初始化完成');

    } catch (error) {
      console.error('异步初始化失败:', error);
      this.hideLoadingState();
      this.showErrorState('初始化失败');
    }
  }

  /**
   * 获取当前标签页信息
   */
  private async getCurrentTabInfo(): Promise<void> {
    try {
      this.currentTab = await this.cookieManager.getCurrentTab();

      if (this.currentTab && this.currentTab.url) {
        this.currentDomain = this.cookieManager.getDomainFromUrl(this.currentTab.url);
      } else {
        this.currentDomain = '';
      }
    } catch (error) {
      console.error('获取当前标签页失败:', error);
      throw error;
    }
  }

  /**
   * 加载存储的数据
   */
  private async loadStoredData(): Promise<void> {
    try {
      this.xuidData = await this.storageManager.loadData();
    } catch (error) {
      console.error('加载存储数据失败:', error);
      throw error;
    }
  }

  /**
   * 获取当前XUID信息
   */
  private async getCurrentXuidInfo(): Promise<void> {
    try {
      if (!this.currentTab?.url) {
        this.currentXuid = '';
        this.currentXuidCookieName = '';
        return;
      }

      const xuidInfo: CurrentXuidInfo = await this.cookieManager.getCurrentXuid(this.currentTab.url);
      this.currentXuid = xuidInfo.value;
      this.currentXuidCookieName = xuidInfo.cookieName;
    } catch (error) {
      console.error('获取当前XUID失败:', error);
      this.currentXuid = '';
      this.currentXuidCookieName = '';
    }
  }

  /**
   * 检测集群状态
   */
  private async detectClusterStatus(): Promise<void> {
    try {
      if (!this.currentTab?.url) {
        this.currentCluster = 'unknown';
        return;
      }

      const clusterStatus: ClusterStatus = await this.cookieManager.getClusterStatus(this.currentTab.url);
      this.currentCluster = clusterStatus.cluster;
    } catch (error) {
      console.error('检测集群状态失败:', error);
      this.currentCluster = 'unknown';
    }
  }



  /**
   * 刷新用户信息
   */
  private async refreshUserInfo(): Promise<void> {
    try {
      const userInfo = await this.networkMonitor.detectCurrentUserInfo();
      if (userInfo) {
        this.currentApiUserInfo = userInfo;
        console.log('用户信息刷新成功:', userInfo);
      }
    } catch (error) {
      console.warn('刷新用户信息失败:', error);
    }
  }

  /**
   * 预加载资产信息
   */
  private async preloadAssetInfo(): Promise<void> {
    try {
      console.log('开始预加载资产信息...');

      // 等待用户信息获取完成（如果还没完成的话）
      let retryCount = 0;
      const maxRetries = 10; // 最多等待5秒

      while (!this.currentApiUserInfo?.assistantUid && retryCount < maxRetries) {
        await new Promise(resolve => setTimeout(resolve, 500));
        retryCount++;
      }

      if (!this.currentApiUserInfo?.assistantUid) {
        console.log('预加载资产信息失败：未获取到assistantUid');
        return;
      }

      // 预加载资产信息到缓存
      const assetType = await this.networkMonitor.fetchAssetInfoForStatus(this.currentApiUserInfo.assistantUid);
      if (assetType) {
        console.log('资产信息预加载成功:', assetType);
        // 设置全局变量供UI使用
        (window as any).currentAssetType = assetType;
      } else {
        console.log('预加载资产信息失败：API返回空结果');
      }
    } catch (error) {
      console.warn('预加载资产信息失败:', error);
    }
  }

  /**
   * 显示加载状态
   */
  private showLoadingState(): void {
    try {
      // 更新状态信息显示为加载中
      const statusElements = {
        username: document.getElementById('currentUsername'),
        xuid: document.getElementById('currentXuid'),
        assetType: document.getElementById('currentAssetType'),
        assistantUid: document.getElementById('currentAssistantUid'),
        domain: document.getElementById('currentDomain'),
        cluster: document.getElementById('currentCluster')
      };

      // 显示已知信息，其他显示加载中
      if (statusElements.xuid) statusElements.xuid.textContent = this.currentXuid || '获取中...';
      if (statusElements.domain) statusElements.domain.textContent = this.currentDomain || '获取中...';
      if (statusElements.cluster) statusElements.cluster.textContent = this.currentCluster || '获取中...';
      if (statusElements.username) statusElements.username.textContent = '加载中...';
      if (statusElements.assetType) statusElements.assetType.textContent = '加载中...';
      if (statusElements.assistantUid) statusElements.assistantUid.textContent = '加载中...';

      // 显示XUID列表加载状态
      const listContainer = document.getElementById('xuidListContainer');
      if (listContainer) {
        listContainer.innerHTML = '<div class="xuid-loading-state">正在加载XUID列表...</div>';
      }

    } catch (error) {
      console.warn('显示加载状态失败:', error);
    }
  }

  /**
   * 隐藏加载状态
   */
  private hideLoadingState(): void {
    try {
      // 移除加载状态元素
      const loadingElements = document.querySelectorAll('.xuid-loading-state');
      loadingElements.forEach(element => element.remove());
    } catch (error) {
      console.warn('隐藏加载状态失败:', error);
    }
  }

  /**
   * 显示错误状态
   */
  private showErrorState(message: string): void {
    try {
      const listContainer = document.getElementById('xuidListContainer');
      if (listContainer) {
        listContainer.innerHTML = `<div class="xuid-error-state">加载失败: ${message}</div>`;
      }
    } catch (error) {
      console.warn('显示错误状态失败:', error);
    }
  }
  
  /**
   * 创建XUID模态框
   */
  private createXuidModal(): HTMLElement {
    const modal = document.createElement('div');
    modal.className = 'modal-overlay xuid-modal';
    modal.innerHTML = this.getXuidModalHTML();
    return modal;
  }

  /**
   * 获取XUID模态框的HTML内容
   */
  private getXuidModalHTML(): string {
    return `
      <div class="modal-content">
        <div class="modal-header">
          <h3 class="modal-title">🕹️ XUID切换助手</h3>
          <button class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
          <!-- 状态信息区域 -->
          <div class="xuid-status-info-section">
            <div class="xuid-status-info-grid">
              <div class="xuid-status-info-row">
                <div class="xuid-status-info-item">
                  <div class="xuid-info-label">用户姓名</div>
                  <div class="xuid-info-value" id="currentUserName">获取中...</div>
                </div>
                <div class="xuid-status-info-item">
                  <div class="xuid-info-label">真人ID</div>
                  <div class="xuid-info-value" id="currentXuid">获取中...</div>
                </div>
              </div>
              <div class="xuid-status-info-row">
                <div class="xuid-status-info-item">
                  <div class="xuid-info-label">资产类型</div>
                  <div class="xuid-info-value" id="currentAssetType">获取中...</div>
                </div>
                <div class="xuid-status-info-item">
                  <div class="xuid-info-label">资产ID</div>
                  <div class="xuid-info-value" id="currentAssistantUid">获取中...</div>
                </div>
              </div>
              <div class="xuid-status-info-row">
                <div class="xuid-status-info-item">
                  <div class="xuid-info-label">当前集群</div>
                  <div class="xuid-cluster-status" id="clusterStatus">获取中...</div>
                </div>
              </div>
            </div>
          </div>

          <!-- 快速操作区域 -->
          <div class="xuid-quick-actions-section">
            <div class="xuid-quick-actions-header">
              <h3>⚡ 快速操作</h3>
            </div>
            <div class="xuid-actions-content" id="actionsContent">
              <div class="xuid-manual-switch-row">
                <input type="text" class="xuid-manual-input" id="manualXuidInput"
                       placeholder="输入XUID、资产ID、手机号或姓名">
                <button class="xuid-manual-switch-btn" id="manualSwitchBtn">切换</button>
              </div>
              <div class="xuid-action-buttons-row">
                <button class="xuid-action-btn primary" id="recordXuidBtn">记录XUID</button>
                <button class="xuid-action-btn warning" id="clearXuidBtn">清除XUID</button>
                <div class="xuid-cluster-dropdown">
                  <button class="xuid-action-btn cluster" id="clusterSwitchBtn">
                    <span id="clusterSwitchText">切换集群</span>
                  </button>
                  <div class="xuid-cluster-dropdown-menu" id="clusterDropdown" style="display: none;">
                    <div class="xuid-cluster-option" data-cluster="tips">
                      <div class="xuid-cluster-color-dot tips"></div>
                      Tips
                    </div>
                    <div class="xuid-cluster-option" data-cluster="small">
                      <div class="xuid-cluster-color-dot small"></div>
                      Small
                    </div>
                    <div class="xuid-cluster-option" data-cluster="stable">
                      <div class="xuid-cluster-color-dot stable"></div>
                      Stable
                    </div>
                    <div class="xuid-cluster-option" data-cluster="online">
                      <div class="xuid-cluster-color-dot online"></div>
                      Online
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- XUID管理区域 -->
          <div class="xuid-management-section">
            <div class="xuid-management-header">
              <h3>📋 XUID管理</h3>
              <div class="xuid-batch-controls">
                <label class="xuid-checkbox-label">
                  <input type="checkbox" id="selectAllXuids">
                  全选
                </label>
              </div>
            </div>

            <div class="xuid-list-header">
              <div class="xuid-header-checkbox">选择</div>
              <div class="xuid-header-realname">姓名</div>
              <div class="xuid-header-asset">资产</div>
              <div class="xuid-header-xuid">XUID</div>
            </div>

            <div class="xuid-list-container" id="xuidListContainer">
              <div class="xuid-empty-state" id="emptyState" style="display: none;">
                <div class="empty-icon">📝</div>
                <div class="empty-title">暂无XUID记录</div>
                <div class="empty-text">点击"记录XUID"按钮开始记录</div>
              </div>
            </div>

            <div class="xuid-action-buttons-row">
              <button class="xuid-action-btn primary" id="switchXuidBtn" disabled>切换选中</button>
              <button class="xuid-action-btn warning" id="deleteXuidBtn" disabled>删除选中</button>
            </div>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * 初始化UI组件
   */
  private initializeUIComponents(_modal: HTMLElement): void {
    // 初始化集群下拉菜单
    this.uiManager.initClusterDropdown();
  }

  /**
   * 绑定模态框事件
   */
  private bindModalEvents(modal: HTMLElement): void {
    // 关闭模态框
    const closeBtn = modal.querySelector('.modal-close') as HTMLButtonElement;
    closeBtn?.addEventListener('click', () => {
      modal.remove();
    });

    // 点击背景关闭
    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        modal.remove();
      }
    });

    // 手动切换XUID
    const manualSwitchBtn = modal.querySelector('#manualSwitchBtn') as HTMLButtonElement;
    manualSwitchBtn?.addEventListener('click', () => {
      this.handleManualSwitch();
    });

    // 记录XUID
    const recordBtn = modal.querySelector('#recordXuidBtn') as HTMLButtonElement;
    recordBtn?.addEventListener('click', () => {
      this.handleRecordXuid();
    });

    // 清除XUID
    const clearBtn = modal.querySelector('#clearXuidBtn') as HTMLButtonElement;
    clearBtn?.addEventListener('click', () => {
      this.handleClearXuid();
    });

    // 切换选中XUID
    const switchBtn = modal.querySelector('#switchXuidBtn') as HTMLButtonElement;
    switchBtn?.addEventListener('click', () => {
      this.handleSwitchSelectedXuids();
    });

    // 删除选中XUID
    const deleteBtn = modal.querySelector('#deleteXuidBtn') as HTMLButtonElement;
    deleteBtn?.addEventListener('click', () => {
      this.handleDeleteSelectedXuids();
    });

    // 全选复选框
    const selectAllBtn = modal.querySelector('#selectAllXuids') as HTMLInputElement;
    selectAllBtn?.addEventListener('change', (e) => {
      this.handleSelectAll((e.target as HTMLInputElement).checked);
    });
  }

  /**
   * 更新显示
   */
  private async updateDisplay(): Promise<void> {
    try {
      // 检查域名支持
      if (!this.checkDomainSupport()) {
        this.uiManager.setDomainUnsupportedState();
        return;
      }

      this.uiManager.restoreNormalState();

      // 获取用户名
      const username = this.currentApiUserInfo?.username ||
                      this.networkMonitor.getCachedUsername(this.currentXuid) ||
                      '未知用户';

      // 获取资产信息
      const assetInfo = await this.getAssetInfo();
      const assistantUid = assetInfo?.assistantUid || null;

      // 更新状态显示
      this.uiManager.updateCurrentStatus(
        this.currentDomain,
        this.currentXuid,
        this.currentCluster,
        username,
        assistantUid
      );

      // 渲染XUID列表
      console.log('[UPDATE-DISPLAY] 开始渲染XUID列表...');
      console.log('[UPDATE-DISPLAY] xuidData存在:', !!this.xuidData);
      if (this.xuidData) {
        console.log('[UPDATE-DISPLAY] XUID数据:', {
          totalXuids: Object.keys(this.xuidData.xuids).length,
          currentDomain: this.currentDomain,
          domainXuids: this.xuidData.xuids[this.currentDomain] ? Object.keys(this.xuidData.xuids[this.currentDomain]).length : 0
        });
        this.uiManager.renderXuidList(
          this.xuidData.xuids,
          this.xuidData.settings.expandedDomains,
          this.currentDomain,
          this.storageManager
        );
        console.log('[UPDATE-DISPLAY] XUID列表渲染完成');
      } else {
        console.log('[UPDATE-DISPLAY] xuidData为空，无法渲染列表');
      }

    } catch (error) {
      console.error('更新显示失败:', error);
    }
  }

  /**
   * 带重试机制的更新显示
   */
  private async updateDisplayWithRetry(maxRetries: number = 5, delay: number = 50): Promise<void> {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      console.log(`[UPDATE-DISPLAY-RETRY] 尝试更新显示 (${attempt}/${maxRetries})`);

      // 检查DOM元素是否存在
      const container = document.getElementById('xuidListContainer');
      const modal = document.querySelector('.xuid-modal');
      const modalBody = document.querySelector('.modal-body');

      console.log('[UPDATE-DISPLAY-RETRY] DOM检查:', {
        attempt,
        hasContainer: !!container,
        hasModal: !!modal,
        hasModalBody: !!modalBody,
        modalVisible: modal ? getComputedStyle(modal).display !== 'none' : false,
        containerParent: container?.parentElement?.className,
        allContainers: Array.from(document.querySelectorAll('[id*="Container"]')).map(el => el.id)
      });

      // 确保核心DOM元素存在（emptyState会在renderXuidList中按需创建）
      if (container && modal && modalBody) {
        console.log('[UPDATE-DISPLAY-RETRY] DOM元素存在，执行更新显示');
        try {
          await this.updateDisplay();
          console.log('[UPDATE-DISPLAY-RETRY] 更新显示成功');
          return;
        } catch (error) {
          console.error('[UPDATE-DISPLAY-RETRY] 更新显示失败:', error);
          if (attempt === maxRetries) {
            throw error;
          }
        }
      }

      if (attempt < maxRetries) {
        console.log(`[UPDATE-DISPLAY-RETRY] DOM元素不完整，等待${delay}ms后重试...`);
        await new Promise(resolve => setTimeout(resolve, delay));
        delay = Math.min(delay * 1.5, 500); // 渐进式退避，最大500ms
      } else {
        console.warn('[UPDATE-DISPLAY-RETRY] 达到最大重试次数，DOM元素仍不完整');
        console.warn('[UPDATE-DISPLAY-RETRY] 最终DOM状态:', {
          container: !!container,
          modal: !!modal,
          modalBody: !!modalBody
        });
      }
    }
  }

  /**
   * 检查域名支持
   */
  private checkDomainSupport(): boolean {
    if (!this.currentDomain) return false;

    // 支持zuoyebang.cc和suanshubang.cc域名
    return this.currentDomain.includes('zuoyebang.cc') ||
           this.currentDomain.includes('suanshubang.cc');
  }

  /**
   * 获取资产信息
   */
  private async getAssetInfo(): Promise<{ assistantUid: string; assetType: string } | null> {
    try {
      // 获取assistantUid
      const assistantUid = this.currentApiUserInfo?.assistantUid;

      if (!assistantUid) {
        console.log('未获取到assistantUid，无法获取资产信息');
        return null;
      }

      // 优先检查是否已有缓存的资产信息
      const cacheKey = `${assistantUid}_precise`;
      const cachedAssetType = this.networkMonitor.getAssetInfoFromCache(cacheKey);

      if (cachedAssetType) {
        console.log('使用缓存的资产信息:', cachedAssetType);
        // 设置全局变量供UI使用
        (window as any).currentAssetType = cachedAssetType;

        return {
          assistantUid: assistantUid,
          assetType: cachedAssetType
        };
      }

      // 如果没有缓存，则从网络监控器获取资产信息
      console.log('缓存中无资产信息，开始API调用...');
      const assetType = await this.networkMonitor.fetchAssetInfoForStatus(assistantUid);

      if (assetType) {
        // 设置全局变量供UI使用
        (window as any).currentAssetType = assetType;

        return {
          assistantUid: assistantUid,
          assetType: assetType
        };
      }

      return null;
    } catch (error) {
      console.error('获取资产信息失败:', error);
      return null;
    }
  }

  /**
   * 处理手动切换XUID - 支持XUID、手机号、姓名三种输入类型
   */
  private async handleManualSwitch(): Promise<void> {
    const startTime = Date.now();

    try {
      if (!this.checkDomainSupport()) {
        await this.showNotification('错误', '当前域名不支持此功能');
        return;
      }

      const input = document.getElementById('manualXuidInput') as HTMLInputElement;
      if (!input) return;

      const inputValue = input.value.trim();
      if (!inputValue) {
        await this.showNotification('警告', '请输入XUID、资产ID、手机号或姓名');
        input.focus();
        return;
      }

      console.log(`[MANUAL-SWITCH] 开始处理输入: "${this.maskSensitiveInfo(inputValue)}"`);

      // 验证输入格式
      const validation = this.cookieManager.validateXuidValue(inputValue);
      if (!validation.valid) {
        await this.showNotification('错误', validation.message || '输入格式无效');
        input.focus();
        return;
      }

      console.log(`[MANUAL-SWITCH] 输入类型: ${validation.inputType}, 需要转换: ${validation.needsConversion}`);

      let finalXuidValue = validation.value!;

      // 根据输入类型进行相应处理
      if (validation.needsConversion) {
        if (validation.inputType === 'phone') {
          console.log(`[MANUAL-SWITCH] 开始手机号转换流程...`);
          await this.showNotification('信息', '正在查询手机号对应的用户信息...');

          try {
            const convertedXuid = await this.convertPhoneToXuid(validation.value!);
            if (!convertedXuid) {
              await this.showNotification('错误', '未找到对应的用户信息，请检查手机号是否正确');
              input.focus();
              return;
            }
            finalXuidValue = convertedXuid;
            console.log(`[MANUAL-SWITCH] 手机号转换成功: ${this.maskSensitiveInfo(finalXuidValue)}`);
          } catch (error) {
            console.error('[MANUAL-SWITCH] 手机号转换失败:', error);
            await this.showNotification('错误', '查询用户信息失败，请稍后重试');
            return;
          }

        } else if (validation.inputType === 'asset') {
          console.log(`[MANUAL-SWITCH] 开始资产ID转换流程...`);
          await this.showNotification('信息', '正在转换资产ID为XUID...');

          try {
            const convertResult = await this.convertAssetIdToXuid(validation.value!);
            if (!convertResult.success) {
              await this.showNotification('错误', convertResult.error || '资产ID转换失败');
              input.focus();
              return;
            }

            finalXuidValue = convertResult.xuid!;
            console.log(`[MANUAL-SWITCH] 资产ID转换成功: ${this.maskSensitiveInfo(finalXuidValue)}`);

            // 如果转换成功且值发生变化，需要执行资产切换流程
            if (convertResult.needsAssetSwitch) {
              console.log(`[MANUAL-SWITCH] 检测到需要资产切换，开始执行切换流程...`);
              try {
                // 先设置XUID Cookie
                await this.setXuidCookie(finalXuidValue);
                console.log(`[MANUAL-SWITCH] XUID Cookie设置完成`);

                // 再调用资产切换接口
                await this.changeSelectedAssistant(validation.value!);
                console.log(`[MANUAL-SWITCH] 资产切换接口调用完成`);

                // 清空输入框
                input.value = '';

                // 刷新显示
                await this.refreshAfterSwitch();

                const endTime = Date.now();
                const duration = endTime - startTime;
                console.log(`[MANUAL-SWITCH] 资产ID切换完成，总耗时: ${duration}ms`);
                return; // 提前返回，避免重复执行后续的Cookie设置
              } catch (assetSwitchError) {
                console.warn(`[MANUAL-SWITCH] 资产切换流程失败，但继续使用转换后的XUID:`, assetSwitchError);
                await this.showNotification('警告', '资产切换失败，但XUID转换成功，将继续切换');
              }
            }
          } catch (error) {
            console.error('[MANUAL-SWITCH] 资产ID转换失败:', error);
            await this.showNotification('错误', '资产ID转换失败，请稍后重试');
            return;
          }

        } else if (validation.inputType === 'name') {
          console.log(`[MANUAL-SWITCH] 开始姓名转换流程...`);
          await this.showNotification('信息', '正在查询姓名对应的用户信息...');

          try {
            const convertedXuid = await this.convertNameToXuid(validation.value!);
            if (!convertedXuid) {
              await this.showNotification('错误', '未找到对应的用户信息，请检查姓名是否正确');
              input.focus();
              return;
            }
            finalXuidValue = convertedXuid;
            console.log(`[MANUAL-SWITCH] 姓名转换成功: ${this.maskSensitiveInfo(finalXuidValue)}`);
          } catch (error) {
            console.error('[MANUAL-SWITCH] 姓名转换失败:', error);
            await this.showNotification('错误', '查询用户信息失败，请稍后重试');
            return;
          }
        }
      }

      console.log(`[MANUAL-SWITCH] 最终使用XUID: ${this.maskSensitiveInfo(finalXuidValue)}`);

      // 设置XUID Cookie
      await this.setXuidCookie(finalXuidValue);

      // 清空输入框
      input.value = '';

      // 刷新显示
      await this.refreshAfterSwitch();

      const endTime = Date.now();
      const duration = endTime - startTime;
      console.log(`[MANUAL-SWITCH] 手动切换完成，总耗时: ${duration}ms`);

    } catch (error) {
      const endTime = Date.now();
      const duration = endTime - startTime;
      console.error(`[MANUAL-SWITCH] 手动切换失败，耗时: ${duration}ms`, error);
      await this.showNotification('错误', '切换失败');
    }
  }

  /**
   * 处理记录XUID
   */
  private async handleRecordXuid(): Promise<void> {
    try {
      if (!this.checkDomainSupport()) {
        await this.showNotification('错误', '当前域名不支持此功能');
        return;
      }

      if (!this.currentXuid) {
        await this.showNotification('警告', '当前页面没有XUID');
        return;
      }

      if (!this.xuidData) {
        await this.showNotification('错误', '数据未加载');
        return;
      }

      const username = this.currentApiUserInfo?.username ||
                      this.networkMonitor.getCachedUsername(this.currentXuid) ||
                      undefined;

      const assistantUid = this.currentApiUserInfo?.assistantUid;

      const result = await this.storageManager.addXuidRecord(
        this.currentDomain,
        this.currentXuid,
        this.currentXuidCookieName,
        this.xuidData,
        username,
        assistantUid
      );

      if (result.success) {
        // 获取并保存XUID管理的资产数据
        console.log('[RECORD-XUID] 开始获取XUID管理资产数据...');
        console.log('[RECORD-XUID] 当前XUID:', this.currentXuid);
        console.log('[RECORD-XUID] 当前assistantUid:', this.currentApiUserInfo?.assistantUid);
        try {
          const assetType = await this.networkMonitor.fetchAssetInfoForList(this.currentXuid);
          console.log('[RECORD-XUID] fetchAssetInfoForList返回结果:', assetType);
          if (assetType) {
            console.log('[RECORD-XUID] 获取到XUID管理资产类型:', assetType);
            // 将字符串包装为AssetInfo对象
            const assetInfo = { type: assetType };
            await this.storageManager.saveAssetInfoForList(this.currentXuid, assetInfo);
            console.log('[RECORD-XUID] XUID管理资产数据已保存');
          } else {
            console.log('[RECORD-XUID] 未获取到XUID管理资产数据');
          }
        } catch (assetError) {
          console.error('[RECORD-XUID] 获取资产信息失败:', assetError);
        }

        await this.showNotification('成功', result.message || 'XUID记录成功');

        // 重新加载存储的数据以确保列表显示最新数据
        console.log('[RECORD-XUID] 重新加载存储数据...');
        await this.loadStoredData();
        console.log('[RECORD-XUID] 存储数据重新加载完成');

        // 等待一小段时间确保DOM操作完成
        console.log('[RECORD-XUID] 等待DOM稳定...');
        await new Promise(resolve => setTimeout(resolve, 100));

        // 使用延迟重试机制更新显示
        console.log('[RECORD-XUID] 开始更新显示...');
        await this.updateDisplayWithRetry();
      } else {
        await this.showNotification('错误', result.message || 'XUID记录失败');
      }

    } catch (error) {
      console.error('记录XUID失败:', error);
      await this.showNotification('错误', '记录失败');
    }
  }

  /**
   * 处理清除XUID
   */
  private async handleClearXuid(): Promise<void> {
    try {
      if (!this.checkDomainSupport()) {
        await this.showNotification('错误', '当前域名不支持此功能');
        return;
      }

      // 使用项目标准的确认对话框
      const confirmed = await notificationManager.confirm({
        title: '清除XUID',
        message: '确定要清除当前域名下的XUID Cookie吗？\n\n页面将会刷新。',
        confirmText: '清除',
        cancelText: '取消',
        type: 'warning'
      });

      if (!confirmed) return;

      const result = await this.cookieManager.clearCurrentDomainXuid(this.currentTab?.url || '');

      if (result.success) {
        await this.showNotification('成功', '当前域名XUID已清除');

        // 刷新页面
        if (this.currentTab?.id) {
          await this.executeScript(() => {
            window.location.reload();
          });
        }
      } else {
        await this.showNotification('错误', result.error || '清除失败');
      }

    } catch (error) {
      console.error('清除XUID失败:', error);
      await this.showNotification('错误', '清除失败');
    }
  }

  /**
   * 处理切换选中的XUID
   */
  private async handleSwitchSelectedXuids(): Promise<void> {
    console.log(`[HANDLE-SWITCH-SELECTED] ========== 按钮触发切换选中XUID ==========`);

    try {
      const selectedXuids = this.uiManager.getSelectedXuids();
      console.log(`[HANDLE-SWITCH-SELECTED] 当前选中的XUID列表:`, selectedXuids);

      if (selectedXuids.length === 0) {
        console.log(`[HANDLE-SWITCH-SELECTED] 没有选中任何XUID`);
        await this.showNotification('警告', '请先选择要切换的XUID');
        return;
      }

      if (selectedXuids.length > 1) {
        console.log(`[HANDLE-SWITCH-SELECTED] 选中了多个XUID，不允许切换`);
        await this.showNotification('警告', '一次只能切换一个XUID，请选择单个XUID');
        return;
      }

      const selectedXuid = selectedXuids[0];
      console.log(`[HANDLE-SWITCH-SELECTED] 开始切换到XUID: ${selectedXuid}`);

      await this.setXuidCookie(selectedXuid);
      console.log(`[HANDLE-SWITCH-SELECTED] Cookie设置完成，开始刷新...`);

      await this.refreshAfterSwitch();
      console.log(`[HANDLE-SWITCH-SELECTED] 切换选中XUID完成: ${selectedXuid}`);

    } catch (error) {
      console.error('[HANDLE-SWITCH-SELECTED] 切换选中XUID失败:', error);
      await this.showNotification('错误', '切换失败');
    }
  }

  /**
   * 处理删除选中的XUID
   */
  private async handleDeleteSelectedXuids(): Promise<void> {
    try {
      const selectedXuids = this.uiManager.getSelectedXuids();

      if (selectedXuids.length === 0) {
        await this.showNotification('警告', '请先选择要删除的XUID');
        return;
      }

      // 使用项目标准的确认对话框
      const confirmed = await notificationManager.confirm({
        title: '删除XUID记录',
        message: `确定要删除选中的 ${selectedXuids.length} 个XUID记录吗？\n\n此操作不可撤销。`,
        confirmText: '删除',
        cancelText: '取消',
        type: 'danger'
      });

      if (!confirmed) return;

      if (!this.xuidData) {
        await this.showNotification('错误', '数据未加载');
        return;
      }

      // 批量删除
      for (const xuid of selectedXuids) {
        await this.storageManager.deleteXuidRecord(this.currentDomain, xuid, this.xuidData);
      }

      await this.showNotification('成功', `已删除 ${selectedXuids.length} 个XUID记录`);
      this.uiManager.clearSelection();
      await this.updateDisplay();

    } catch (error) {
      console.error('删除选中XUID失败:', error);
      await this.showNotification('错误', '删除失败');
    }
  }

  /**
   * 处理全选
   */
  private handleSelectAll(checked: boolean): void {
    const checkboxes = document.querySelectorAll('.xuid-item input[type="checkbox"]') as NodeListOf<HTMLInputElement>;
    checkboxes.forEach(checkbox => {
      if (!checkbox.disabled) {
        checkbox.checked = checked;
        // 触发change事件
        checkbox.dispatchEvent(new Event('change'));
      }
    });
  }

  /**
   * 设置XUID Cookie
   */
  private async setXuidCookie(xuidValue: string): Promise<void> {
    console.log(`[SET-XUID-COOKIE] ========== 开始设置XUID Cookie ==========`);
    console.log(`[SET-XUID-COOKIE] 目标XUID: ${xuidValue}`);

    if (!this.currentTab?.url) {
      console.error(`[SET-XUID-COOKIE] 无法获取当前标签页`);
      throw new Error('无法获取当前标签页');
    }

    console.log(`[SET-XUID-COOKIE] 当前标签页: ${this.currentTab.url}`);
    console.log(`[SET-XUID-COOKIE] 当前域名: ${this.currentDomain}`);
    console.log(`[SET-XUID-COOKIE] Cookie名称: ${this.currentXuidCookieName || 'XUID'}`);

    const result = await this.cookieManager.setXuidCookie(
      this.currentTab.url,
      this.currentDomain,
      this.currentXuidCookieName || 'XUID',
      xuidValue
    );

    console.log(`[SET-XUID-COOKIE] Cookie设置结果:`, result);

    if (!result.success) {
      console.error(`[SET-XUID-COOKIE] Cookie设置失败:`, result.error);
      throw new Error(result.error || '设置Cookie失败');
    }

    console.log(`[SET-XUID-COOKIE] ========== XUID Cookie设置完成 ==========`);
  }



  /**
   * 切换后刷新
   */
  private async refreshAfterSwitch(): Promise<void> {
    try {
      console.log('[REFRESH-AFTER-SWITCH] 开始切换后刷新流程...');

      // 显示切换成功提示
      await this.showNotification('成功', 'XUID切换成功，页面即将刷新...');

      // 清除内存缓存，但保留localStorage缓存
      this.networkMonitor.clearCache();
      console.log('[REFRESH-AFTER-SWITCH] 已清除内存缓存');

      // 同时启动模态框更新和页面刷新
      const updatePromises = [];

      // 1. 更新模态框状态信息
      const updateModalPromise = (async () => {
        try {
          // 更新当前XUID信息
          await this.getCurrentXuidInfo();
          console.log('[REFRESH-AFTER-SWITCH] 当前XUID已更新:', this.currentXuid);

          // 立即获取新的用户信息（Cookie已经设置，API应该能获取到新信息）
          await this.refreshUserInfo();
          console.log('[REFRESH-AFTER-SWITCH] 用户信息已刷新:', this.currentApiUserInfo);

          // 更新显示
          if (document.querySelector('.xuid-modal')) {
            console.log('[REFRESH-AFTER-SWITCH] 开始更新模态框显示...');
            await this.updateDisplay();
            console.log('[REFRESH-AFTER-SWITCH] 模态框显示已更新');

            // 切换成功后清空选中状态
            console.log('[REFRESH-AFTER-SWITCH] 清空选中状态...');
            this.uiManager.clearSelection();
            console.log('[REFRESH-AFTER-SWITCH] 选中状态已清空');
          }
        } catch (error) {
          console.error('[REFRESH-AFTER-SWITCH] 模态框更新失败:', error);
        }
      })();

      updatePromises.push(updateModalPromise);

      // 2. 刷新页面
      if (this.currentTab?.id) {
        const refreshPagePromise = (async () => {
          try {
            console.log('[REFRESH-AFTER-SWITCH] 开始刷新页面...');
            await this.executeScript(() => {
              window.location.reload();
            });
          } catch (error) {
            console.warn('[REFRESH-AFTER-SWITCH] 页面刷新失败:', error);
          }
        })();

        updatePromises.push(refreshPagePromise);
      }

      // 等待所有操作完成（实际上页面刷新会中断，但模态框更新会完成）
      await Promise.allSettled(updatePromises);
      console.log('[REFRESH-AFTER-SWITCH] 切换后刷新流程完成');

    } catch (error) {
      console.error('[REFRESH-AFTER-SWITCH] 刷新失败:', error);
      await this.showNotification('警告', 'XUID已切换，但状态更新失败');
    }
  }

  /**
   * UI事件处理器：集群切换
   */
  private async handleClusterSwitch(cluster: 'tips' | 'small' | 'stable' | 'online'): Promise<void> {
    try {
      console.log('集群切换开始:', {
        cluster,
        currentDomain: this.currentDomain,
        currentTabUrl: this.currentTab?.url,
        domainSupported: this.checkDomainSupport()
      });

      if (!this.checkDomainSupport()) {
        console.warn('域名不支持集群切换功能:', this.currentDomain);
        await this.showNotification('错误', '当前域名不支持此功能');
        return;
      }

      if (!this.currentTab?.url) {
        console.error('无法获取当前标签页URL');
        await this.showNotification('错误', '无法获取当前标签页');
        return;
      }

      console.log('开始设置集群模式...');
      const result = await this.cookieManager.setClusterMode(
        this.currentTab.url,
        this.currentDomain,
        cluster
      );

      console.log('集群模式设置结果:', result);

      if (result.success) {
        this.currentCluster = cluster;
        console.log('集群切换成功，更新显示...');
        await this.showNotification('成功', result.message || `已切换到${cluster}集群`);
        await this.updateDisplay();

        // 刷新页面
        if (this.currentTab?.id) {
          console.log('刷新页面...');
          await this.executeScript(() => {
            window.location.reload();
          });
        }
      } else {
        console.error('集群切换失败:', result.error);
        await this.showNotification('错误', result.error || '集群切换失败');
      }

    } catch (error) {
      console.error('集群切换失败:', error);
      await this.showNotification('错误', '集群切换失败');
    }
  }

  /**
   * UI事件处理器：XUID切换
   */
  private async handleXuidSwitch(_domain: string, xuidValue: string): Promise<void> {
    console.log(`[HANDLE-XUID-SWITCH] ========== UI事件触发XUID切换 ==========`);
    console.log(`[HANDLE-XUID-SWITCH] 域名: ${_domain}`);
    console.log(`[HANDLE-XUID-SWITCH] 目标XUID: ${xuidValue}`);

    try {
      console.log(`[HANDLE-XUID-SWITCH] 开始切换到XUID: ${xuidValue}`);
      await this.setXuidCookie(xuidValue);
      await this.refreshAfterSwitch();
      console.log(`[HANDLE-XUID-SWITCH] XUID切换完成: ${xuidValue}`);
    } catch (error) {
      console.error('[HANDLE-XUID-SWITCH] XUID切换失败:', error);
      await this.showNotification('错误', 'XUID切换失败');
    }
  }

  /**
   * UI事件处理器：XUID删除
   */
  private async handleXuidDelete(domain: string, xuidValue: string): Promise<void> {
    try {
      if (!this.xuidData) {
        await this.showNotification('错误', '数据未加载');
        return;
      }

      // 使用项目标准的确认对话框
      const confirmed = await notificationManager.confirm({
        title: '删除XUID',
        message: `确定要删除XUID ${xuidValue} 吗？\n\n此操作不可撤销。`,
        confirmText: '删除',
        cancelText: '取消',
        type: 'danger'
      });

      if (!confirmed) return;

      const result = await this.storageManager.deleteXuidRecord(domain, xuidValue, this.xuidData);

      if (result.success) {
        await this.showNotification('成功', result.message || 'XUID删除成功');
        await this.updateDisplay();
      } else {
        await this.showNotification('错误', result.message || 'XUID删除失败');
      }

    } catch (error) {
      console.error('XUID删除失败:', error);
      await this.showNotification('错误', 'XUID删除失败');
    }
  }

  /**
   * 重写基类的 showNotification 方法，使用项目统一的通知管理器
   */
  protected async showNotification(type: string, message: string): Promise<void> {
    switch (type) {
      case '成功':
        notificationManager.success(message);
        break;
      case '错误':
        notificationManager.error(message);
        break;
      case '警告':
        notificationManager.warning(message);
        break;
      case '信息':
      default:
        notificationManager.info(message);
        break;
    }
  }

  /**
   * 敏感信息脱敏显示
   */
  private maskSensitiveInfo(input: string): string {
    if (!input || typeof input !== 'string') {
      return input;
    }

    // 手机号脱敏：显示前3位和后4位
    if (/^\d{11}$/.test(input)) {
      return input.substring(0, 3) + '****' + input.substring(7);
    }

    // XUID脱敏：显示前4位和后4位
    if (/^\d{8,20}$/.test(input)) {
      if (input.length <= 8) {
        return input.substring(0, 4) + '****';
      }
      return input.substring(0, 4) + '****' + input.substring(input.length - 4);
    }

    // 姓名不脱敏（通常不包含敏感信息）
    return input;
  }

  /**
   * 通过资产ID转换为XUID
   */
  private async convertAssetIdToXuid(assetId: string): Promise<{
    success: boolean;
    xuid?: string;
    error?: string;
    needsAssetSwitch?: boolean;
  }> {
    const startTime = Date.now();
    console.log(`[ASSET-CONVERT] 开始资产ID转换: ${this.maskSensitiveInfo(assetId)}`);

    try {
      // 构建转换API的URL
      const baseUrl = `https://${this.currentDomain}`;
      const apiPath = '/fwyytool/desk/user/convertxuidapi';
      const url = `${baseUrl}${apiPath}?xuid=${encodeURIComponent(assetId)}`;

      console.log(`[ASSET-CONVERT] 请求URL: ${url}`);

      // 发送API请求
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'accept': 'application/json, text/plain, */*',
          'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
          'sec-ch-ua': '"Chromium";v="130", "Google Chrome";v="130", "Not?A_Brand";v="99"',
          'sec-ch-ua-mobile': '?0',
          'sec-ch-ua-platform': '"macOS"',
          'sec-fetch-dest': 'empty',
          'sec-fetch-mode': 'cors',
          'sec-fetch-site': 'same-origin',
          'Referer': `${baseUrl}/`,
          'Referrer-Policy': 'strict-origin-when-cross-origin'
        },
        credentials: 'include'
      });

      const endTime = Date.now();
      const duration = endTime - startTime;

      console.log(`[ASSET-CONVERT] API响应状态: ${response.status}, 耗时: ${duration}ms`);

      if (!response.ok) {
        const errorMsg = `资产ID转换API请求失败: ${response.status} ${response.statusText}`;
        console.warn(`[ASSET-CONVERT] ${errorMsg}`);

        // 降级处理：如果API不可用，返回原始值
        console.warn(`[ASSET-CONVERT] 启用降级处理，使用原始资产ID作为XUID`);
        return {
          success: true,
          xuid: assetId,
          needsAssetSwitch: false
        };
      }

      // 解析响应
      const responseText = await response.text();
      let responseData: AssetConvertResponse;

      try {
        responseData = JSON.parse(responseText);
      } catch (parseError) {
        console.error('[ASSET-CONVERT] JSON解析失败:', parseError);
        // 降级处理
        return {
          success: true,
          xuid: assetId,
          needsAssetSwitch: false
        };
      }

      // 检查API是否返回错误
      if (responseData.errNo !== undefined && responseData.errNo !== 0) {
        const errorMsg = responseData.errMsg || '未知错误';
        console.warn(`[ASSET-CONVERT] API返回业务错误: errNo=${responseData.errNo}, errMsg=${errorMsg}`);
        // 降级处理
        return {
          success: true,
          xuid: assetId,
          needsAssetSwitch: false
        };
      }

      // 检查响应数据格式
      if (!responseData.data || !responseData.data.xuid) {
        console.warn(`[ASSET-CONVERT] 响应数据格式异常`);
        // 降级处理
        return {
          success: true,
          xuid: assetId,
          needsAssetSwitch: false
        };
      }

      const convertedXuid = responseData.data.xuid.toString();
      const needsAssetSwitch = convertedXuid !== assetId;

      console.log(`[ASSET-CONVERT] ✅ 资产ID转换成功！`);
      console.log(`[ASSET-CONVERT] 原始资产ID: ${this.maskSensitiveInfo(assetId)}`);
      console.log(`[ASSET-CONVERT] 转换后XUID: ${this.maskSensitiveInfo(convertedXuid)}`);
      console.log(`[ASSET-CONVERT] 需要资产切换: ${needsAssetSwitch}`);
      console.log(`[ASSET-CONVERT] 总耗时: ${duration}ms`);

      return {
        success: true,
        xuid: convertedXuid,
        needsAssetSwitch: needsAssetSwitch
      };

    } catch (error) {
      const endTime = Date.now();
      const duration = endTime - startTime;
      console.error(`[ASSET-CONVERT] 资产ID转换失败，耗时: ${duration}ms`, error);

      // 降级处理：网络错误时使用原始值
      console.warn(`[ASSET-CONVERT] 网络错误降级处理，使用原始资产ID作为XUID`);
      return {
        success: true,
        xuid: assetId,
        needsAssetSwitch: false
      };
    }
  }

  /**
   * 通过手机号转换为XUID
   */
  private async convertPhoneToXuid(phone: string): Promise<string | null> {
    const startTime = Date.now();
    console.log(`[PHONE-CONVERT] 开始手机号转换: ${this.maskSensitiveInfo(phone)}`);

    try {
      // 构建手机号查询API的URL
      const baseUrl = `https://${this.currentDomain}`;
      const apiPath = '/userprofile/manage/businessaccountdetail';
      const requestTimestamp = Date.now();
      const url = `${baseUrl}${apiPath}?_=${requestTimestamp}`;

      // 构建表单数据
      const formData = new URLSearchParams();
      formData.append('phone', phone);
      formData.append('name', '');
      formData.append('userName', '');
      formData.append('wxNum', '');
      formData.append('deviceUid', '');

      console.log(`[PHONE-CONVERT] 请求URL: ${url}`);

      // 发送API请求
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'accept': 'application/json, text/plain, */*',
          'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
          'content-type': 'application/x-www-form-urlencoded',
          'sec-ch-ua': '"Chromium";v="130", "Google Chrome";v="130", "Not?A_Brand";v="99"',
          'sec-ch-ua-mobile': '?0',
          'sec-ch-ua-platform': '"macOS"',
          'sec-fetch-dest': 'empty',
          'sec-fetch-mode': 'cors',
          'sec-fetch-site': 'same-origin',
          'x-requested-with': 'XMLHttpRequest',
          'Referrer-Policy': 'strict-origin-when-cross-origin'
        },
        body: formData.toString(),
        credentials: 'include'
      });

      const endTime = Date.now();
      const duration = endTime - startTime;

      console.log(`[PHONE-CONVERT] API响应状态: ${response.status}, 耗时: ${duration}ms`);

      if (!response.ok) {
        throw new Error(`手机号查询API请求失败: ${response.status} ${response.statusText}`);
      }

      // 解析响应
      const responseText = await response.text();
      let responseData: PhoneQueryResponse;

      try {
        responseData = JSON.parse(responseText);
      } catch (parseError) {
        console.error('[PHONE-CONVERT] JSON解析失败:', parseError);
        throw new Error('手机号查询API响应解析失败');
      }

      // 检查API是否返回错误
      if (responseData.errNo !== undefined && responseData.errNo !== 0) {
        const errorMsg = responseData.errMsg || '未知错误';
        console.warn(`[PHONE-CONVERT] API返回业务错误: errNo=${responseData.errNo}, errMsg=${errorMsg}`);
        throw new Error(`手机号查询失败: ${errorMsg}`);
      }

      // 检查响应数据格式
      const actualData = responseData.data;
      if (!actualData || !actualData.detail || !Array.isArray(actualData.detail) || actualData.detail.length === 0) {
        console.warn(`[PHONE-CONVERT] 未找到匹配的用户`);
        return null;
      }

      console.log(`[PHONE-CONVERT] 找到 ${actualData.detail.length} 个匹配结果`);

      const user = actualData.detail[0]; // 取第一个匹配的用户
      console.log(`[PHONE-CONVERT] 选择用户:`, {
        userId: user.userId,
        name: user.name,
        phone: this.maskSensitiveInfo(user.phone),
        userName: user.userName
      });

      if (user.userId) {
        const userId = user.userId.toString();
        console.log(`[PHONE-CONVERT] 手机号转换成功: ${this.maskSensitiveInfo(userId)}`);
        return userId;
      } else {
        console.warn(`[PHONE-CONVERT] 用户数据中没有userId字段`);
        return null;
      }

    } catch (error) {
      const endTime = Date.now();
      const duration = endTime - startTime;
      console.error(`[PHONE-CONVERT] 手机号转换失败，耗时: ${duration}ms`, error);
      throw error;
    }
  }

  /**
   * 通过姓名转换为XUID
   */
  private async convertNameToXuid(name: string): Promise<string | null> {
    const startTime = Date.now();
    console.log(`[NAME-CONVERT] 开始姓名转换: ${name}`);

    try {
      // 构建姓名查询API的URL
      const baseUrl = `https://${this.currentDomain}`;
      const apiPath = '/dataproxy/userprofile/getPersonList';
      const url = `${baseUrl}${apiPath}`;

      const requestData = {
        staffName: [name],
        rnSize: 10
      };

      console.log(`[NAME-CONVERT] 请求URL: ${url}`);

      // 发送API请求
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
        credentials: 'include'
      });

      const endTime = Date.now();
      const duration = endTime - startTime;

      console.log(`[NAME-CONVERT] API响应状态: ${response.status}, 耗时: ${duration}ms`);

      if (!response.ok) {
        throw new Error(`姓名查询API请求失败: ${response.status} ${response.statusText}`);
      }

      // 解析响应
      const responseText = await response.text();
      let responseData: NameQueryResponse;

      try {
        responseData = JSON.parse(responseText);
      } catch (parseError) {
        console.error('[NAME-CONVERT] JSON解析失败:', parseError);
        throw new Error('姓名查询API响应解析失败');
      }

      // 检查API是否返回错误
      if (responseData.errNo !== undefined && responseData.errNo !== 0) {
        const errorMsg = responseData.errMsg || '未知错误';
        console.warn(`[NAME-CONVERT] API返回业务错误: errNo=${responseData.errNo}, errMsg=${errorMsg}`);
        throw new Error(`姓名查询失败: ${errorMsg}`);
      }

      // 检查响应数据格式
      const actualData = responseData.data;
      if (!actualData || !actualData.list || !Array.isArray(actualData.list) || actualData.list.length === 0) {
        console.warn(`[NAME-CONVERT] 未找到匹配的用户`);
        return null;
      }

      console.log(`[NAME-CONVERT] 找到 ${actualData.list.length} 个匹配结果`);

      const user = actualData.list[0]; // 取第一个匹配的用户
      console.log(`[NAME-CONVERT] 选择用户:`, {
        staffUid: user.staffUid,
        staffName: user.staffName,
        staffType: user.staffType,
        staffStatus: user.staffStatus
      });

      if (user.staffUid) {
        const xuidValue = user.staffUid.toString();
        console.log(`[NAME-CONVERT] 姓名转换成功: ${this.maskSensitiveInfo(xuidValue)}`);
        return xuidValue;
      } else {
        console.warn(`[NAME-CONVERT] 用户数据中没有staffUid字段`);
        return null;
      }

    } catch (error) {
      const endTime = Date.now();
      const duration = endTime - startTime;
      console.error(`[NAME-CONVERT] 姓名转换失败，耗时: ${duration}ms`, error);
      throw error;
    }
  }

  /**
   * 调用资产切换接口 - 当资产ID转换后值发生变化时调用
   */
  private async changeSelectedAssistant(assistantUid: string): Promise<void> {
    const startTime = Date.now();
    console.log(`[ASSET-SWITCH] 开始调用资产切换接口`);
    console.log(`[ASSET-SWITCH] assistantUid (原始资产ID): ${assistantUid}`);
    console.log(`[ASSET-SWITCH] 当前域名: ${this.currentDomain}`);

    try {
      // 构建资产切换API的URL
      const baseUrl = `https://${this.currentDomain}`;
      const apiPath = '/assistantdesk/deskv1/user/changeselectedassistant';
      const url = `${baseUrl}${apiPath}?assistantUid=${encodeURIComponent(assistantUid)}`;

      console.log(`[ASSET-SWITCH] 请求URL: ${url}`);

      // 发送API请求
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'accept': 'application/json, text/plain, */*',
          'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
          'sec-ch-ua': '"Chromium";v="130", "Google Chrome";v="130", "Not?A_Brand";v="99"',
          'sec-ch-ua-mobile': '?0',
          'sec-ch-ua-platform': '"macOS"',
          'sec-fetch-dest': 'empty',
          'sec-fetch-mode': 'cors',
          'sec-fetch-site': 'same-origin',
          'Referer': `${baseUrl}/`,
          'Referrer-Policy': 'strict-origin-when-cross-origin'
        },
        credentials: 'include'
      });

      const endTime = Date.now();
      const duration = endTime - startTime;

      console.log(`[ASSET-SWITCH] API响应状态: ${response.status}, 耗时: ${duration}ms`);

      if (!response.ok) {
        const errorMsg = `资产切换API请求失败: ${response.status} ${response.statusText}`;
        console.warn(`[ASSET-SWITCH] ${errorMsg}`);
        throw new Error(errorMsg);
      }

      // 解析响应
      const responseText = await response.text();
      let responseData: any;

      try {
        responseData = JSON.parse(responseText);
      } catch (parseError) {
        console.error('[ASSET-SWITCH] JSON解析失败:', parseError);
        throw new Error('资产切换API响应解析失败');
      }

      // 检查API是否返回错误
      if (responseData.errNo !== undefined && responseData.errNo !== 0) {
        const errorMsg = responseData.errMsg || responseData.errStr || '未知错误';
        console.warn(`[ASSET-SWITCH] API返回业务错误: errNo=${responseData.errNo}, errMsg=${errorMsg}`);
        throw new Error(`资产切换失败: ${errorMsg}`);
      }

      console.log(`[ASSET-SWITCH] ✅ 资产切换成功！`);
      console.log(`[ASSET-SWITCH] assistantUid: ${assistantUid}`);
      console.log(`[ASSET-SWITCH] 总耗时: ${duration}ms`);

    } catch (error) {
      const endTime = Date.now();
      const duration = endTime - startTime;
      console.error(`[ASSET-SWITCH] 资产切换失败，耗时: ${duration}ms`, error);
      throw error;
    }
  }

  /**
   * 打开工具时检测assistantUid是否发生变化
   */
  private async checkAssistantUidChangeOnOpen(): Promise<void> {
    try {
      console.log('[ASSISTANT-UID-DETECTION] 开始检测assistantUid变化（打开时）...');

      // 记录当前缓存的assistantUid作为基准
      this.lastKnownAssistantUid = this.currentApiUserInfo?.assistantUid || null;
      console.log('[ASSISTANT-UID-DETECTION] 当前缓存的assistantUid:', this.lastKnownAssistantUid);

      // 强制调用userinfo接口获取最新信息（不使用缓存）
      const latestUserInfo = await this.networkMonitor.fetchUserInfoFromApi(this.currentTab?.id!);

      if (!latestUserInfo) {
        console.log('[ASSISTANT-UID-DETECTION] 无法获取最新用户信息，跳过检测');
        return;
      }

      const currentAssistantUid = latestUserInfo.assistantUid || null;
      console.log('[ASSISTANT-UID-DETECTION] 检测结果:', {
        lastKnown: this.lastKnownAssistantUid,
        current: currentAssistantUid,
        changed: this.lastKnownAssistantUid !== currentAssistantUid
      });

      // 检查assistantUid是否发生变化
      if (this.lastKnownAssistantUid !== currentAssistantUid) {
        console.log('[ASSISTANT-UID-DETECTION] 检测到assistantUid变化，更新状态信息');

        // 更新当前用户信息
        this.currentApiUserInfo = latestUserInfo;
        this.lastKnownAssistantUid = currentAssistantUid;

        // 更新缓存
        if (latestUserInfo.xuid && latestUserInfo.username) {
          this.networkMonitor.setCachedUserInfo(latestUserInfo.xuid, latestUserInfo);
          console.log('[ASSISTANT-UID-DETECTION] 已更新用户信息缓存');
        }

        // 清除资产信息缓存，强制重新获取
        this.networkMonitor.clearCache();

        // 更新显示
        if (document.querySelector('.xuid-modal')) {
          await this.updateDisplay();
          console.log('[ASSISTANT-UID-DETECTION] 状态信息已更新');
        }
      } else {
        console.log('[ASSISTANT-UID-DETECTION] assistantUid未发生变化');
      }

    } catch (error) {
      console.error('[ASSISTANT-UID-DETECTION] 检测assistantUid变化失败:', error);
    }
  }


}
