/**
 * 告警解析器工具
 * 用于解析Falcon监控系统的告警信息，生成Grafana日志查询跳转链接
 */

import { BaseTool, TOOL_CATEGORIES, TOOL_ICONS } from '../utils/tool-template';
import { AlertParserCore, ParsedAlert, GrafanaUrlResult } from '../utils/alert-parser-core';
import { notificationManager } from '../utils/notification-manager';

export class AlertParserTool extends BaseTool {
  id = 'alert-parser';
  name = '告警解析器';
  description = '解析Falcon监控告警信息，自动生成Grafana日志查询跳转链接';
  icon = '🚨';
  categories = ['all'];
  version = { major: 1, minor: 0, patch: 1 };

  // 核心解析器实例
  private parser: AlertParserCore;

  constructor() {
    super();
    this.parser = new AlertParserCore();
  }

  async action(): Promise<void> {
    try {
      const modal = this.createModal();
      document.body.appendChild(modal);

      // 加载上次保存的输入内容
      await this.loadSavedInput(modal);

    } catch (error) {
      console.error('告警解析器启动失败:', error);
      await this.showNotification('错误', '告警解析器启动失败');
    }
  }

  private createModal(): HTMLElement {
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
      <div class="modal-content alert-parser-modal">
        <div class="modal-header">
          <h3>🚨 告警解析器</h3>
          <button class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
          <div class="input-section">
            <label for="alert-input">告警信息：</label>
            <textarea id="alert-input" placeholder="请粘贴告警信息...

示例：[P2][异常] 名称: sql平均耗时超过1s 主机IP: ************ 监控项: log.sql_cost_avg > 1000"></textarea>

            <div class="button-group">
              <button id="parse-alert-btn" class="btn btn-primary">🔍 解析告警</button>
              <button id="clear-input-btn" class="btn btn-secondary">🗑️ 清空</button>
            </div>

            <div class="advanced-options">
              <details>
                <summary style="cursor: pointer;">⚙️ 高级选项</summary>
                <div style="margin-top: var(--spacing-3); padding: var(--spacing-3); border: var(--input-border-width) solid var(--border); border-radius: var(--radius-md); background-color: var(--surface);">
                  <label style="display: flex; align-items: center; margin-bottom: var(--spacing-2); color: var(--text-primary); font-size: var(--font-size-sm);">
                    <input type="checkbox" id="ignore-file-path" style="margin-right: var(--spacing-2);">
                    忽略日志文件限制 (移除tp参数)
                  </label>
                  <label style="display: flex; align-items: center; margin-bottom: var(--spacing-2); color: var(--text-primary); font-size: var(--font-size-sm);">
                    <input type="checkbox" id="ignore-cluster" style="margin-right: var(--spacing-2);">
                    忽略集群限制 (查询所有集群)
                  </label>
                  <div>
                    <label for="custom-search" style="display: block; margin-bottom: var(--spacing-1); font-size: var(--font-size-xs); color: var(--text-secondary);">自定义搜索条件:</label>
                    <input type="text" id="custom-search" class="form-control" placeholder="例如: ERROR|WARN" style="width: 100%; padding: var(--spacing-2); font-size: var(--font-size-sm);">
                  </div>
                </div>
              </details>
            </div>
          </div>

          <div id="result-section" class="result-section" style="display: none;">
            <h4>解析结果</h4>
            <div id="result-content"></div>
          </div>
        </div>
      </div>
    `;

    this.bindModalEvents(modal);
    return modal;
  }

  private bindModalEvents(modal: HTMLElement): void {
    // 关闭模态框
    const closeBtn = modal.querySelector('.modal-close') as HTMLButtonElement;

    closeBtn?.addEventListener('click', () => {
      modal.remove();
    });

    // 解析告警按钮
    const parseBtn = modal.querySelector('#parse-alert-btn') as HTMLButtonElement;
    parseBtn?.addEventListener('click', async () => {
      await this.processAlert(modal);
    });

    // 清空按钮
    const clearBtn = modal.querySelector('#clear-input-btn') as HTMLButtonElement;
    clearBtn?.addEventListener('click', () => {
      this.clearInput(modal);
    });



    // 点击背景关闭
    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        modal.remove();
      }
    });

    // 输入框变化时保存内容
    const alertInput = modal.querySelector('#alert-input') as HTMLTextAreaElement;
    alertInput?.addEventListener('input', () => {
      this.saveInputContent(alertInput.value);
    });
  }

  /**
   * 处理告警解析
   */
  private async processAlert(modal: HTMLElement): Promise<void> {
    const alertInput = modal.querySelector('#alert-input') as HTMLTextAreaElement;
    const resultSection = modal.querySelector('#result-section') as HTMLElement;
    const resultContent = modal.querySelector('#result-content') as HTMLElement;
    const parseBtn = modal.querySelector('#parse-alert-btn') as HTMLButtonElement;

    const alertText = alertInput.value.trim();
    if (!alertText) {
      await this.showNotification('提示', '请输入告警信息');
      return;
    }

    try {
      parseBtn.disabled = true;
      parseBtn.textContent = '解析中...';

      // 保存输入内容
      this.saveInputContent(alertText);

      // 解析告警
      const parsedAlert = this.parser.parseAlert(alertText);
      console.log('解析结果:', parsedAlert);

      // 获取高级选项
      const ignoreFilePathCheckbox = modal.querySelector('#ignore-file-path') as HTMLInputElement;
      const ignoreClusterCheckbox = modal.querySelector('#ignore-cluster') as HTMLInputElement;
      const customSearchInput = modal.querySelector('#custom-search') as HTMLInputElement;

      const ignoreFilePath = ignoreFilePathCheckbox?.checked || false;
      const ignoreCluster = ignoreClusterCheckbox?.checked || false;
      const customSearchCondition = customSearchInput?.value.trim() || undefined;

      // 生成Grafana URL
      let grafanaResult: GrafanaUrlResult | null = null;
      if (parsedAlert.hostInfo && parsedAlert.alertTime) {
        grafanaResult = await this.generateGrafanaUrlWithOptions(
          parsedAlert,
          ignoreFilePath,
          customSearchCondition,
          ignoreCluster
        );
      }

      // 显示结果
      this.renderResult(resultContent, parsedAlert, grafanaResult);
      resultSection.style.display = 'block';

      await this.showNotification('成功', '告警解析完成');

    } catch (error) {
      console.error('告警解析失败:', error);
      resultContent.innerHTML = `
        <div class="result-error">
          <p>告警解析失败: ${error instanceof Error ? error.message : '未知错误'}</p>
        </div>
      `;
      resultSection.style.display = 'block';
      await this.showNotification('错误', '告警解析失败');
    } finally {
      parseBtn.disabled = false;
      parseBtn.textContent = '🔍 解析告警';
    }
  }

  /**
   * 生成Grafana URL（带高级选项）
   */
  private async generateGrafanaUrlWithOptions(
    parsedAlert: ParsedAlert,
    ignoreFilePath: boolean = false,
    customSearchCondition?: string,
    ignoreCluster: boolean = false
  ): Promise<GrafanaUrlResult> {
    try {
      return await this.parser.generateGrafanaUrl(
        parsedAlert,
        false, // usePatternSearch
        customSearchCondition,
        ignoreFilePath,
        ignoreCluster
      );
    } catch (error) {
      console.error('生成Grafana URL失败:', error);
      // 返回一个基本的错误结果
      return {
        url: 'https://log-search-docker.zuoyebang.cc/explore',
        collecterInfo: {
          name: parsedAlert.metricInfo?.metricName || 'unknown',
          type: 'error',
          error: error instanceof Error ? error.message : '未知错误'
        },
        searchType: 'error',
        baseQuery: 'error'
      };
    }
  }

  /**
   * 清空输入
   */
  private clearInput(modal: HTMLElement): void {
    const alertInput = modal.querySelector('#alert-input') as HTMLTextAreaElement;
    const resultSection = modal.querySelector('#result-section') as HTMLElement;

    if (alertInput) {
      alertInput.value = '';
    }

    if (resultSection) {
      resultSection.style.display = 'none';
    }

    // 清空保存的内容
    this.saveInputContent('');
  }



  /**
   * 保存输入内容到本地存储
   */
  private async saveInputContent(content: string): Promise<void> {
    try {
      await this.saveData('alertInput', content);
    } catch (error) {
      console.warn('保存输入内容失败:', error);
    }
  }

  /**
   * 加载保存的输入内容
   */
  private async loadSavedInput(modal: HTMLElement): Promise<void> {
    try {
      const savedContent = await this.loadData<string>('alertInput', '');
      const alertInput = modal.querySelector('#alert-input') as HTMLTextAreaElement;

      if (alertInput && savedContent) {
        alertInput.value = savedContent;
      }
    } catch (error) {
      console.warn('加载保存的输入内容失败:', error);
    }
  }

  /**
   * 渲染解析结果
   */
  private renderResult(container: HTMLElement, parsedAlert: ParsedAlert, grafanaResult: GrafanaUrlResult | null): void {
    const hostInfo = parsedAlert.hostInfo;
    const metricInfo = parsedAlert.metricInfo;

    container.innerHTML = `
      <div class="alert-result">
        <div class="summary-grid">
          ${parsedAlert.name ? `
            <div class="summary-item alert-name">
              <div class="summary-label">告警名称</div>
              <div class="summary-value">${parsedAlert.name}</div>
            </div>
          ` : ''}

          ${hostInfo?.serviceName ? `
            <div class="summary-item">
              <div class="summary-label">服务名</div>
              <div class="summary-value">${hostInfo.serviceName}</div>
            </div>
          ` : ''}

          ${hostInfo?.moduleName ? `
            <div class="summary-item">
              <div class="summary-label">模块名</div>
              <div class="summary-value">${hostInfo.moduleName}</div>
            </div>
          ` : ''}

          ${hostInfo?.clusterId ? `
            <div class="summary-item">
              <div class="summary-label">集群</div>
              <div class="summary-value">${hostInfo.fullCluster}</div>
            </div>
          ` : ''}

          ${metricInfo?.metricName ? `
            <div class="summary-item metric-name-item">
              <div class="summary-label">监控指标</div>
              <div class="summary-value metric-name-value">${metricInfo.metricName}</div>
            </div>
          ` : ''}

          ${parsedAlert.alertTime ? `
            <div class="summary-item">
              <div class="summary-label">告警时间</div>
              <div class="summary-value">${parsedAlert.alertTime}</div>
            </div>
          ` : ''}
        </div>

        ${grafanaResult ? `
          <div class="url-section">
            <div class="url-label">Grafana查询链接</div>
            <div class="url-container">
              <div class="url-text url-text-truncated" title="${grafanaResult.url}">${grafanaResult.url}</div>
              <div class="action-buttons">
                <button class="copy-btn" data-url="${grafanaResult.url}">复制链接</button>
                <button class="open-btn" data-url="${grafanaResult.url}">打开链接</button>
              </div>
            </div>

            ${grafanaResult.collecterInfo ? `
              <div class="collecter-info">
                <div class="collecter-info-title">📊 指标信息</div>
                <div class="collecter-info-content">
                  <div>指标名称: ${grafanaResult.collecterInfo.name}</div>
                  <div>指标类型: ${grafanaResult.collecterInfo.type}</div>
                  ${grafanaResult.collecterInfo.file_path ? `<div>日志文件: ${grafanaResult.collecterInfo.file_path}</div>` : ''}
                  ${grafanaResult.searchType !== 'none' ? `<div>搜索类型: ${grafanaResult.searchType}</div>` : ''}
                  ${grafanaResult.searchType !== 'none' ? `<div>搜索内容: ${grafanaResult.searchCondition}</div>` : ''}
                  ${grafanaResult.collecterInfo.error ? `<div class="collecter-info-error">错误: ${grafanaResult.collecterInfo.error}</div>` : ''}
                </div>
              </div>
            ` : ''}
          </div>
        ` : ''}
      </div>
    `;

    // 绑定按钮事件
    if (grafanaResult) {
      this.bindActionButtons(container, grafanaResult.url);
    }
  }

  /**
   * 绑定操作按钮事件
   */
  private bindActionButtons(container: HTMLElement, url: string): void {
    // 复制链接按钮
    const copyBtn = container.querySelector('.copy-btn') as HTMLButtonElement;
    copyBtn?.addEventListener('click', async () => {
      try {
        await navigator.clipboard.writeText(url);
        await this.showNotification('成功', '链接已复制到剪贴板');
      } catch (error) {
        console.error('复制链接失败:', error);
        await this.showNotification('错误', '复制链接失败');
      }
    });

    // 打开链接按钮
    const openBtn = container.querySelector('.open-btn') as HTMLButtonElement;
    openBtn?.addEventListener('click', () => {
      try {
        window.open(url, '_blank');
      } catch (error) {
        console.error('打开链接失败:', error);
        this.showNotification('错误', '打开链接失败');
      }
    });
  }

  /**
   * 重写基类的 showNotification 方法，使用项目统一的通知管理器
   */
  protected async showNotification(type: string, message: string): Promise<void> {
    switch (type) {
      case '成功':
        notificationManager.success(message);
        break;
      case '错误':
        notificationManager.error(message);
        break;
      case '警告':
        notificationManager.warning(message);
        break;
      case '信息':
      default:
        notificationManager.info(message);
        break;
    }
  }
}
