/**
 * 任务列表工具
 * 提供课程任务列表跳转功能，支持课程ID查询和XUID配置
 */

import { BaseTool } from '../utils/tool-template';
import { XuidCookieManager, CurrentXuidInfo } from '../utils/xuid-cookie-manager';
import { XuidNetworkMonitor } from '../utils/xuid-network-monitor';
import { Modal } from '../utils/ui-components';
import { styleManager } from '../utils/style-manager';
import { notificationManager } from '../utils/notification-manager';

// 课程查询API响应类型
interface CourseQueryResponse {
  errNo: number;
  errMsg?: string;
  data: {
    courseID: string;
    courseName: string;
    teacherList: Array<{
      xuid: string;
      name: string;
      assistantUid: string;
      assetType: string;
    }>;
  };
}

// 任务列表配置数据类型
interface TaskListConfig {
  courseId: string;
  xuid?: string;
}

export class TaskListTool extends BaseTool {
  id = 'task-list';
  name = '任务列表';
  description = '快速跳转到课程任务列表页面，支持课程ID查询和XUID配置';
  icon = '📚';
  categories = ['all'];
  version = { major: 1, minor: 0, patch: 0 };

  // 管理器实例
  private cookieManager: XuidCookieManager;
  private networkMonitor: XuidNetworkMonitor;
  private modal: Modal | null = null;

  // 状态变量
  private currentDomain: string = '';

  constructor() {
    super();
    
    // 初始化管理器
    this.cookieManager = new XuidCookieManager();
    this.networkMonitor = new XuidNetworkMonitor();
  }

  async action(): Promise<void> {
    try {
      // 加载任务列表样式
      await this.loadTaskListStyles();

      // 检查域名支持
      if (!await this.checkDomainSupport()) {
        notificationManager.show({
          message: '当前域名不支持此功能',
          type: 'warning'
        });
        return;
      }

      // 显示任务列表配置模态框
      this.showTaskListModal();

    } catch (error) {
      console.error('任务列表工具启动失败:', error);
      notificationManager.show({
        message: '任务列表工具启动失败',
        type: 'error'
      });
    }
  }

  /**
   * 加载任务列表样式
   */
  private async loadTaskListStyles(): Promise<void> {
    try {
      // 注册任务列表样式模块
      styleManager.registerModule({
        name: 'task-list',
        path: '/styles/task-list.css',
        dependencies: ['design-tokens', 'components']
      });

      // 加载样式
      await styleManager.loadModule('task-list');
    } catch (error) {
      console.warn('加载任务列表样式失败:', error);
    }
  }

  /**
   * 检查域名支持
   */
  private async checkDomainSupport(): Promise<boolean> {
    try {
      this.currentDomain = await this.getCurrentDomain();
      
      // 检查是否为支持的域名
      const supportedDomains = [
        'assistantdesk.zuoyebang.cc',
        'assistantdesk.zuoyebang.com',
        'assistantdesk-tips.zuoyebang.cc',
        'assistantdesk-small.zuoyebang.cc',
        'assistantdesk-stable.zuoyebang.cc'
      ];

      const hostname = new URL(this.currentDomain).hostname;
      return supportedDomains.includes(hostname);
    } catch (error) {
      console.error('域名检查失败:', error);
      return false;
    }
  }

  /**
   * 获取当前域名
   */
  private async getCurrentDomain(): Promise<string> {
    try {
      const tabs = await browser.tabs.query({ active: true, currentWindow: true });
      if (tabs && tabs.length > 0 && tabs[0].url) {
        const url = new URL(tabs[0].url);
        return url.origin;
      }
      throw new Error('无法获取当前标签页');
    } catch (error) {
      console.error('获取当前域名失败:', error);
      throw error;
    }
  }

  /**
   * 显示任务列表配置模态框
   */
  private showTaskListModal(): void {
    // 创建模态框内容
    const content = this.createModalContent();

    // 创建模态框
    this.modal = new Modal(content, {
      title: '📚 任务列表跳转',
      size: 'md',
      closable: true,
      backdrop: true
    });

    // 添加底部按钮
    const footer = this.createModalFooter();
    this.modal.addFooter(footer);

    // 绑定事件
    this.bindModalEvents();

    // 显示模态框
    this.modal.open();

    // 设置焦点到课程ID输入框
    setTimeout(() => {
      const courseIdInput = document.getElementById('taskListCourseId') as HTMLInputElement;
      if (courseIdInput) {
        courseIdInput.focus();
      }
    }, 100);
  }

  /**
   * 创建模态框内容
   */
  private createModalContent(): HTMLElement {
    const container = document.createElement('div');
    container.className = 'task-list-form';

    container.innerHTML = `
      <div class="form-group">
        <label for="taskListCourseId" class="form-label">
          课程ID <span class="text-error">*</span>
        </label>
        <input
          type="text"
          id="taskListCourseId"
          class="form-control"
          placeholder="请输入课程ID"
          maxlength="50"
          required
        >
      </div>

      <div class="form-group">
        <label for="taskListXuid" class="form-label">XUID</label>
        <input
          type="text"
          id="taskListXuid"
          class="form-control"
          placeholder="请输入XUID（可选）"
          maxlength="20"
        >
      </div>

      <div class="form-tips">
        <div class="tips-header">💡 提示：</div>
        <ul class="tips-list">
          <li>课程ID为必填项</li>
          <li>XUID为可选项，留空则使用课程下任意老师</li>
          <li>提交后将跳转到方舟任务列表页面</li>
        </ul>
      </div>
    `;

    return container;
  }

  /**
   * 创建模态框底部
   */
  private createModalFooter(): HTMLElement {
    const footer = document.createElement('div');
    footer.innerHTML = `
      <button type="button" class="btn btn-secondary" id="taskListCancel">取消</button>
      <button type="button" class="btn btn-primary" id="taskListSubmit">跳转到任务列表</button>
    `;
    return footer;
  }

  /**
   * 绑定模态框事件
   */
  private bindModalEvents(): void {
    if (!this.modal) return;

    const modalElement = this.modal.getElement();
    
    // 取消按钮
    const cancelBtn = modalElement.querySelector('#taskListCancel') as HTMLButtonElement;
    if (cancelBtn) {
      cancelBtn.addEventListener('click', () => this.closeModal());
    }

    // 提交按钮
    const submitBtn = modalElement.querySelector('#taskListSubmit') as HTMLButtonElement;
    if (submitBtn) {
      submitBtn.addEventListener('click', () => this.handleSubmit());
    }

    // 回车键提交
    const courseIdInput = modalElement.querySelector('#taskListCourseId') as HTMLInputElement;
    const xuidInput = modalElement.querySelector('#taskListXuid') as HTMLInputElement;
    
    [courseIdInput, xuidInput].forEach(input => {
      if (input) {
        input.addEventListener('keypress', (e) => {
          if (e.key === 'Enter') {
            this.handleSubmit();
          }
        });
      }
    });
  }

  /**
   * 处理表单提交
   */
  private async handleSubmit(): Promise<void> {
    if (!this.modal) return;

    const modalElement = this.modal.getElement();
    const courseIdInput = modalElement.querySelector('#taskListCourseId') as HTMLInputElement;
    const xuidInput = modalElement.querySelector('#taskListXuid') as HTMLInputElement;

    if (!courseIdInput || !xuidInput) return;

    const courseId = courseIdInput.value.trim();
    const xuid = xuidInput.value.trim();

    // 验证课程ID
    if (!courseId) {
      notificationManager.show({
        message: '请输入课程ID',
        type: 'warning'
      });
      courseIdInput.focus();
      return;
    }

    if (courseId.length < 1 || courseId.length > 50) {
      notificationManager.show({
        message: '课程ID长度应在1-50字符之间',
        type: 'warning'
      });
      courseIdInput.focus();
      return;
    }

    // 验证XUID（如果提供）
    if (xuid && (!/^\d{8,20}$/.test(xuid))) {
      notificationManager.show({
        message: 'XUID格式不正确，应为8-20位数字',
        type: 'warning'
      });
      xuidInput.focus();
      return;
    }

    try {
      // 显示加载状态
      notificationManager.show({
        message: '正在查询课程信息...',
        type: 'info',
        duration: 3000
      });

      // 查询课程信息
      const config: TaskListConfig = { courseId, xuid: xuid || undefined };
      await this.submitTaskListConfig(config);

      // 关闭模态框
      this.closeModal();

    } catch (error) {
      console.error('提交任务列表配置失败:', error);
      notificationManager.show({
        message: '操作失败，请重试',
        type: 'error'
      });
    }
  }

  /**
   * 关闭模态框
   */
  private closeModal(): void {
    if (this.modal) {
      this.modal.close();
      this.modal = null;
    }
  }

  /**
   * 提交任务列表配置
   */
  private async submitTaskListConfig(config: TaskListConfig): Promise<void> {
    try {
      console.log(`[TASK-LIST] 开始任务列表配置: ${config.courseId}`);

      // 查询课程信息
      const courseInfo = await this.queryCourseInfo(config.courseId, config.xuid);

      if (!courseInfo) {
        throw new Error('查询课程信息失败，请检查课程ID是否正确');
      }

      console.log(`[TASK-LIST] 课程信息查询成功:`, courseInfo);

      // 构建课程数据，使用与原始代码相同的格式
      const courseDataTaskV2 = {
        "year": courseInfo.year,
        "courseId": courseInfo.courseId,
        "checkedData": {
          "courseId": courseInfo.courseId,
          "serviceTypeLabel": courseInfo.courseServiceTypeName,
          "serviceTypeValue": courseInfo.courseServiceType
        },
        "serviceId": courseInfo.serviceId
      };

      console.log(`[TASK-LIST] 课程数据对象:`, courseDataTaskV2);

      // 执行完整的跳转逻辑
      await this.targetAssistantDesk(courseInfo);

      console.log(`[TASK-LIST] ✅ 任务列表配置完成`);
      notificationManager.show({
        message: '方舟任务页面已在新标签页中打开',
        type: 'success'
      });

    } catch (error) {
      console.error('[TASK-LIST] 配置失败:', error);
      throw error;
    }
  }

  /**
   * 获取当前XUID
   */
  private async getCurrentXuid(): Promise<string> {
    try {
      // 获取当前标签页URL
      const tabs = await browser.tabs.query({ active: true, currentWindow: true });
      if (!tabs || tabs.length === 0 || !tabs[0].url) {
        throw new Error('无法获取当前标签页');
      }

      const currentXuidInfo = await this.cookieManager.getCurrentXuid(tabs[0].url);
      if (currentXuidInfo && currentXuidInfo.value) {
        return currentXuidInfo.value;
      }
      throw new Error('无法获取当前XUID');
    } catch (error) {
      console.error('获取当前XUID失败:', error);
      throw new Error('无法获取当前XUID，请确保已登录');
    }
  }

  /**
   * 查询课程信息
   */
  private async queryCourseInfo(courseId: string, xuid?: string): Promise<any> {
    try {
      // 使用正确的API端点
      let apiUrl = `${this.currentDomain}/fwyytool/desk/ark/jumptasklistapi?courseId=${encodeURIComponent(courseId)}`;

      // 如果提供了XUID，添加到URL参数中
      if (xuid && xuid.trim()) {
        apiUrl += `&xuid=${encodeURIComponent(xuid)}`;
        console.log(`[TASK-LIST] 包含XUID参数`);
      } else {
        console.log(`[TASK-LIST] 不包含XUID参数`);
      }

      console.log(`[TASK-LIST] 请求URL: ${apiUrl}`);

      const response = await fetch(apiUrl, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Accept': 'application/json, text/plain, */*',
          'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      // 先获取响应文本，然后解析JSON
      const responseText = await response.text();
      console.log(`[TASK-LIST] 响应体长度: ${responseText.length} 字符`);

      let responseData;
      try {
        responseData = JSON.parse(responseText);
        console.log(`[TASK-LIST] JSON解析成功`);
      } catch (parseError) {
        console.error(`[TASK-LIST] JSON解析失败:`, parseError);
        console.error(`[TASK-LIST] 响应内容前500字符:`, responseText.substring(0, 500));
        const errorMessage = parseError instanceof Error ? parseError.message : String(parseError);
        throw new Error(`API响应格式错误: ${errorMessage}`);
      }

      console.log(`[TASK-LIST] API响应:`, responseData);

      // 检查API响应状态
      if (responseData.errNo !== undefined && responseData.errNo !== 0) {
        const errorMsg = responseData.errMsg || '未知错误';
        console.error(`[TASK-LIST] API返回错误: errNo=${responseData.errNo}, errMsg=${errorMsg}`);
        throw new Error(`${errorMsg} (errNo: ${responseData.errNo})`);
      }

      if (!responseData.data) {
        console.error(`[TASK-LIST] 响应数据中没有data字段`);
        throw new Error('API响应数据格式错误');
      }

      return responseData.data;
    } catch (error) {
      console.error(`[TASK-LIST] 查询课程信息失败:`, error);
      throw error;
    }
  }



  /**
   * 执行助手桌面跳转逻辑
   */
  private async targetAssistantDesk(courseData: any): Promise<void> {
    const startTime = Date.now();
    console.log(`[TASK-LIST] 开始执行助手桌面跳转`);
    console.log(`[TASK-LIST] 课程数据:`, {
      courseId: courseData.courseId,
      year: courseData.year,
      xuid: courseData.xuid || '',
      assistantUid: courseData.assistantUid,
      serviceId: courseData.serviceId,
      courseServiceType: courseData.courseServiceType,
      courseServiceTypeName: courseData.courseServiceTypeName
    });

    try {
      // 第一步：设置XUID Cookie（如果有XUID）
      if (courseData.xuid) {
        console.log(`[TASK-LIST] 第一步：设置XUID Cookie`);
        await this.setXuidCookie(courseData.xuid);
      }

      // 第二步：调用助手切换API（如果有assistantUid）
      if (courseData.assistantUid) {
        console.log(`[TASK-LIST] 第二步：调用助手切换API`);
        await this.changeSelectedAssistant(courseData.assistantUid);
      }

      // 第三步：设置课程数据到sessionStorage
      console.log(`[TASK-LIST] 第三步：设置课程数据到sessionStorage`);
      await this.setCourseDataToStorage(courseData);

      // 第四步：打开新窗口跳转到任务页面
      console.log(`[TASK-LIST] 第四步：打开任务页面`);
      const domain = new URL(this.currentDomain).hostname;
      const taskUrl = `https://${domain}/assistantdesk/view/assistant-first-line-teacher-v2/first-line-teacher/task/crm-task-v2`;

      await browser.tabs.create({
        url: taskUrl,
        active: true
      });

      const endTime = Date.now();
      const duration = endTime - startTime;
      console.log(`[TASK-LIST] ✅ 助手桌面跳转完成，总耗时: ${duration}ms`);

    } catch (error) {
      const endTime = Date.now();
      const duration = endTime - startTime;

      console.error(`[TASK-LIST] ❌ 助手桌面跳转失败，耗时: ${duration}ms`);
      const errorName = error instanceof Error ? error.name : 'Unknown';
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error(`[TASK-LIST] 错误类型: ${errorName}`);
      console.error(`[TASK-LIST] 错误消息: ${errorMessage}`);

      throw error;
    }
  }

  /**
   * 设置XUID Cookie
   */
  private async setXuidCookie(xuid: string): Promise<void> {
    try {
      const xuidString = String(xuid);
      console.log(`[TASK-LIST] 开始设置XUID Cookie`);

      // 获取当前标签页URL
      const tabs = await browser.tabs.query({ active: true, currentWindow: true });
      if (!tabs || tabs.length === 0 || !tabs[0].url) {
        throw new Error('无法获取当前标签页');
      }

      const tabUrl = tabs[0].url;
      const domain = new URL(this.currentDomain).hostname;

      // 使用XuidCookieManager设置Cookie
      const result = await this.cookieManager.setXuidCookie(tabUrl, domain, 'xuid', xuidString);

      if (!result.success) {
        throw new Error(result.error || 'Cookie设置失败');
      }

      console.log(`[TASK-LIST] ✅ XUID Cookie设置成功`);
    } catch (error) {
      console.error(`[TASK-LIST] ❌ XUID Cookie设置失败:`, error);
      throw error;
    }
  }

  /**
   * 调用助手切换API
   */
  private async changeSelectedAssistant(assistantUid: string): Promise<void> {
    try {
      console.log(`[TASK-LIST] 开始调用助手切换API`);

      const apiUrl = `${this.currentDomain}/fwyytool/desk/ark/changeselectedassistant`;
      const response = await fetch(apiUrl, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify({
          assistantUid: assistantUid
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      console.log(`[TASK-LIST] 助手切换API响应:`, result);

      if (result.errNo !== 0) {
        throw new Error(result.errMsg || '助手切换失败');
      }

      console.log(`[TASK-LIST] ✅ 助手切换成功`);
    } catch (error) {
      console.error(`[TASK-LIST] ❌ 助手切换失败:`, error);
      throw error;
    }
  }

  /**
   * 设置课程数据到sessionStorage
   */
  private async setCourseDataToStorage(courseData: any): Promise<void> {
    try {
      console.log(`[TASK-LIST] 开始设置课程数据到sessionStorage`);

      // 获取当前活动标签页
      const tabs = await browser.tabs.query({ active: true, currentWindow: true });
      if (!tabs || tabs.length === 0) {
        throw new Error('无法获取当前标签页');
      }

      const tabId = tabs[0].id;
      if (!tabId) {
        throw new Error('无法获取标签页ID');
      }

      // 构建要存储的数据
      const storageData = {
        year: courseData.year,
        courseId: courseData.courseId,
        checkedData: {
          courseId: courseData.courseId,
          serviceTypeLabel: courseData.courseServiceTypeName,
          serviceTypeValue: courseData.courseServiceType
        },
        serviceId: courseData.serviceId
      };

      // 通过content script设置sessionStorage
      await browser.tabs.executeScript(tabId, {
        code: `
          try {
            const data = ${JSON.stringify(storageData)};
            sessionStorage.setItem('arkCourseDataTaskV2', JSON.stringify(data));
            console.log('[TASK-LIST] sessionStorage数据已设置:', data);
            true;
          } catch (error) {
            console.error('[TASK-LIST] sessionStorage设置失败:', error);
            false;
          }
        `
      });

      console.log(`[TASK-LIST] ✅ 课程数据已设置到sessionStorage`);
    } catch (error) {
      console.error(`[TASK-LIST] ❌ 设置sessionStorage失败:`, error);
      // 这个错误不应该阻止整个流程，所以只记录日志
    }
  }

}
