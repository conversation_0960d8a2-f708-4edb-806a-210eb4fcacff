# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a modern browser extension project built with the WXT framework (https://wxt.dev/). The project is a comprehensive TypeScript-based browser extension tool collection called "服务运营工具集合" (Service Operations Tool Collection) focused on development and productivity tools for internal services.

## Development Commands

```bash
# Development
npm run dev              # Chrome development mode with hot reload
npm run dev:firefox      # Firefox development mode

# Building
npm run build           # Build Chrome version for production
npm run build:firefox   # Build Firefox version for production

# Packaging
npm run zip             # Package Chrome extension as .zip
npm run zip:firefox     # Package Firefox extension as .zip

# Type checking
npm run compile         # TypeScript compilation check

# Tool generation
npm run create-tool     # Generate new tool template
npm run tool:create     # Alias for create-tool

# Setup
npm install            # Install dependencies and setup styles
npm run setup-styles   # Setup style symlinks (runs automatically)
```

## Architecture Overview

### Core Architecture

The extension follows a modular architecture with three main entry points:

**Entry Points:**
- **Background Script** (`entrypoints/background.ts`) - Extension lifecycle management
- **Content Script** (`entrypoints/content.ts`) - Page interaction scoped to `*.zuoyebang.cc/*` domains
- **Popup** (`entrypoints/popup/main.ts`) - Main user interface and tool management

**Core Systems:**
- **Tool System** (`utils/tool-template.ts`, `utils/tool-registry.ts`) - Dynamic tool registration and management
- **Category System** (`utils/category-manager.ts`) - Dynamic categorization with user-defined categories
- **UI System** (`utils/ui-components.ts`, `utils/style-manager.ts`) - Component-based UI with modular CSS
- **Settings System** (`utils/settings-manager.ts`) - Persistent user settings management
- **Update System** (`utils/update-manager.ts`, `utils/update-notification.ts`) - Tool version checking and notifications
- **Notification System** (`utils/notification-manager.ts`) - User notifications and dialogs

### Tool Architecture

Tools are implemented as classes extending `BaseTool` and follow this pattern:

```typescript
export class MyTool extends BaseTool {
  id = 'my-tool';
  name = 'My Tool';
  description = 'Tool description';
  icon = '🛠️';
  categories = ['all', 'utility'];
  version = { major: 1, minor: 0, patch: 0 };

  async action(): Promise<void> {
    // Tool implementation
  }
}
```

**Current Tools:**
- **XuidTool** (`tools/xuid.ts`) - XUID management with cookie, network, and storage monitoring
- **AlertParserTool** (`tools/alert-parser.ts`) - Alert parsing functionality
- **TaskListTool** (`tools/task-list.ts`) - Task management tool

### Data Management

All data is stored locally using Chrome storage APIs:
- **Settings** - User preferences and extension configuration
- **Tool Data** - Tool-specific data and user settings
- **Categories** - User-defined categories and tool assignments
- **Update States** - Tool version information and update notifications

### Style System

Uses a modular CSS architecture with design tokens:
- **Design Tokens** (`styles/design-tokens.css`) - Color, spacing, typography tokens
- **Components** (`styles/components.css`) - Reusable component styles
- **Layout** (`styles/layout.css`) - Layout and grid styles
- **Utilities** (`styles/utilities.css`) - Utility classes and helpers
- **Tool-specific** (`styles/alert-parser.css`, `styles/task-list.css`) - Individual tool styles

## Key Configuration

- **wxt.config.ts** - WXT framework configuration with extension manifest
- **tsconfig.json** - TypeScript configuration (extends WXT defaults)
- **package.json** - Dependencies and npm scripts
- **web-ext.config.ts** - Web extension specific configuration

## Extension Permissions

The extension requires these key permissions:
- **Core**: `activeTab`, `storage`, `tabs`, `scripting`
- **User Interaction**: `notifications`, `downloads`, `clipboardWrite`, `clipboardRead`
- **Data Access**: `cookies`, `webRequest`
- **Host Permissions**: `<all_urls>`, `*.zuoyebang.cc/*` domains

## Important Development Notes

### Tool Registration
Tools are registered in `entrypoints/popup/main.ts` using the `registerToolWithSettings()` function, which preserves user settings when tools are updated.

### Style Management
The StyleManager (`utils/style-manager.ts`) handles CSS module loading with dependency management. Tools should register their CSS modules during initialization.

### Settings Persistence
All settings are automatically persisted using Chrome storage APIs. Tools should use the settingsManager for configuration rather than direct storage access.

### Update System
The extension includes a sophisticated update system that:
- Automatically checks for tool updates based on configurable intervals
- Provides user notifications for available updates
- Supports update dismissal and ignoring
- Handles manual update checks

### Category Management
Users can create, edit, and delete custom categories. The system automatically handles tool categorization and maintains backward compatibility when categories are removed.