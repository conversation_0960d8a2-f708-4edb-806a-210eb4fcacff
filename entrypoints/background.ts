export default defineBackground(() => {
  console.log('fwyy-tools background script started', { id: browser.runtime.id });

  // XUID工具相关的后台脚本功能
  initializeXuidBackground();
});

/**
 * 初始化XUID工具的后台脚本功能
 */
function initializeXuidBackground() {
  // 扩展安装时的初始化
  browser.runtime.onInstalled.addListener((details) => {
    console.log('fwyy-tools已安装', details);

    // 初始化XUID存储数据
    initializeXuidStorage();
  });

  // 扩展启动时的初始化
  browser.runtime.onStartup.addListener(() => {
    console.log('fwyy-tools已启动');
  });

  // 监听来自内容脚本的消息
  browser.runtime.onMessage.addListener((request, sender, sendResponse) => {
    console.log('收到消息:', request);

    switch (request.action) {
      case 'getCurrentXuid':
        handleGetCurrentXuid(request, sender, sendResponse);
        return true; // 保持消息通道开放

      case 'setXuid':
        handleSetXuid(request, sender, sendResponse);
        return true;

      default:
        console.log('未知消息类型:', request.action);
    }
  });

  // 监听标签页更新事件
  browser.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
    // 当页面完全加载后，可以进行一些初始化操作
    if (changeInfo.status === 'complete' && tab.url) {
      console.log('页面加载完成:', tab.url);
    }
  });

  // 监听Cookie变化事件
  browser.cookies.onChanged.addListener((changeInfo) => {
    // 只关注XUID Cookie的变化
    if (changeInfo.cookie.name === 'XUID') {
      console.log('XUID Cookie发生变化:', changeInfo);

      // 通知相关标签页更新显示
      notifyXuidUpdate(changeInfo);
    }
  });

  // 定期清理数据（每24小时）
  browser.alarms.create('xuid-cleanup', { periodInMinutes: 24 * 60 });
  browser.alarms.onAlarm.addListener((alarm) => {
    if (alarm.name === 'xuid-cleanup') {
      cleanupXuidData();
    }
  });
}

/**
 * 初始化XUID存储数据
 */
async function initializeXuidStorage() {
  try {
    const result = await browser.storage.local.get(['xuids', 'settings', 'assetInfo', 'assetInfoList']);

    // 如果没有数据，初始化默认数据
    if (!result.xuids) {
      await browser.storage.local.set({
        xuids: {},
        settings: {
          maxXuidCount: 100,
          expandedDomains: [],
          xuidCookieNames: ['XUID', 'xuid', 'Xuid', 'XUid', 'xuId', 'xUid', 'xUId']
        },
        assetInfo: {},
        assetInfoList: {}
      });
      console.log('XUID存储数据已初始化');
    }
  } catch (error) {
    console.error('初始化XUID存储失败:', error);
  }
}

/**
 * 处理获取当前XUID的请求
 */
async function handleGetCurrentXuid(request: any, sender: any, sendResponse: any) {
  try {
    if (!sender.tab) {
      sendResponse({ success: false, error: '无法获取标签页信息' });
      return;
    }

    const cookie = await browser.cookies.get({
      url: sender.tab.url,
      name: 'XUID'
    });

    sendResponse({
      success: true,
      xuid: cookie ? cookie.value : null,
      domain: new URL(sender.tab.url).hostname
    });

  } catch (error) {
    console.error('获取XUID失败:', error);
    sendResponse({ success: false, error: (error as Error).message });
  }
}

/**
 * 处理设置XUID的请求
 */
async function handleSetXuid(request: any, sender: any, sendResponse: any) {
  try {
    if (!sender.tab) {
      sendResponse({ success: false, error: '无法获取标签页信息' });
      return;
    }

    const url = new URL(sender.tab.url);

    await browser.cookies.set({
      url: sender.tab.url,
      name: 'XUID',
      value: request.xuid,
      domain: url.hostname,
      path: '/'
    });

    sendResponse({ success: true });

  } catch (error) {
    console.error('设置XUID失败:', error);
    sendResponse({ success: false, error: (error as Error).message });
  }
}

/**
 * 通知XUID更新
 */
async function notifyXuidUpdate(changeInfo: any) {
  try {
    // 获取所有标签页
    const tabs = await browser.tabs.query({});

    // 向每个标签页发送更新消息
    tabs.forEach(tab => {
      if (tab.url && tab.url.includes(changeInfo.cookie.domain)) {
        browser.tabs.sendMessage(tab.id, {
          action: 'xuidChanged',
          xuid: changeInfo.cookie.value,
          domain: changeInfo.cookie.domain
        }).catch(() => {
          // 忽略无法发送消息的标签页（如chrome://页面）
        });
      }
    });

  } catch (error) {
    console.error('通知XUID更新失败:', error);
  }
}

/**
 * 清理过期的XUID数据
 */
async function cleanupXuidData() {
  try {
    const result = await browser.storage.local.get(['xuids']);

    if (result.xuids) {
      // 这里可以添加清理逻辑，比如删除长时间未使用的XUID
      console.log('XUID数据清理完成');
    }

  } catch (error) {
    console.error('XUID数据清理失败:', error);
  }
}
