<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>服务运营工具集合 - 工具页面</title>
    <meta name="manifest.type" content="chrome_url_overrides" />
  </head>
  <body>
    <div id="app">
      <!-- 加载状态 -->
      <div id="loading" class="loading-screen">
        <div class="loading-content">
          <div class="loading-spinner">🛠️</div>
          <div class="loading-text">正在启动工具...</div>
        </div>
      </div>
      
      <!-- 工具容器 -->
      <div id="tool-container" class="tool-container" style="display: none;">
        <!-- 工具内容将在这里动态加载 -->
      </div>
      
      <!-- 工具选择界面 -->
      <div id="tool-selector" class="tool-selector" style="display: none;">
        <header class="selector-header">
          <h1>🛠️ 选择工具</h1>
          <div class="search-container">
            <input type="text" id="selector-search" placeholder="搜索工具..." class="search-input">
            <span class="search-icon">🔍</span>
          </div>
        </header>
        
        <main class="selector-tools" id="selector-tools">
          <!-- 工具列表将在这里动态生成 -->
        </main>
      </div>
    </div>
    <script type="module" src="./main.ts"></script>
  </body>
</html>
