/**
 * 主样式文件 - 使用模块化样式系统
 * 导入设计令牌和组件样式
 */

/* 导入设计令牌 */
@import url('../../styles/design-tokens.css');
/* 导入通知组件样式 */
@import '../../styles/notifications.css';

/* 导入组件样式 */
@import url('../../styles/components.css');

/* 导入布局样式 */
@import url('../../styles/layout.css');

/* 导入工具类样式 */
@import url('../../styles/utilities.css');

/* 导入动画样式 */
@import url('../../styles/animations.css');

/* 导入更新相关样式 */
@import url('../../styles/update.css');

/* 应用基础字体设置 */
:root {
  font-family: var(--font-family-sans);
  line-height: var(--line-height-normal);
  font-weight: var(--font-weight-normal);
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 应用特定样式 */
html {
  width: 450px;
  height: 600px;
  overflow: hidden;
}

body {
  margin: 0;
  padding: 0;
  background-color: var(--background);
  color: var(--text-primary);
  width: 450px !important;
  height: 600px !important;
  min-width: 450px;
  max-width: 450px;
  min-height: 600px;
  max-height: 600px;
  overflow: hidden;
  box-sizing: border-box;
}

#app {
  display: flex;
  flex-direction: column;
  width: 450px;
  height: 600px;
  box-sizing: border-box;
}

/* 应用头部样式 */
.header {
  padding: var(--spacing-4);
  background-color: var(--surface);
  border-bottom: 1px solid var(--border);
  flex-shrink: 0;
}

.title {
  margin: 0 0 var(--spacing-3) 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  text-align: center;
}

.search-container {
  position: relative;
  display: flex;
  align-items: center;
}

.search-input {
  width: 100%;
  padding: var(--spacing-2) var(--spacing-3) var(--spacing-2) var(--spacing-8);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  background-color: var(--background);
  color: var(--text-primary);
  font-size: var(--font-size-sm);
  transition: border-color var(--duration-150) var(--ease-in-out),
              box-shadow var(--duration-150) var(--ease-in-out);
}

.search-input:focus {
  outline: none;
  border-color: var(--border-focus);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.search-input::placeholder {
  color: var(--text-tertiary);
}

.search-icon {
  position: absolute;
  left: 12px;
  color: var(--text-secondary);
  pointer-events: none;
}

/* 分类导航 */
.categories {
  display: flex;
  padding: 12px 16px;
  gap: 8px;
  background-color: var(--surface);
  border-bottom: 1px solid var(--border);
  overflow-x: auto;
  flex-shrink: 0;
}

.categories::-webkit-scrollbar {
  display: none;
}

.category-btn {
  padding: 6px 12px;
  border: 2px solid var(--border);
  border-radius: 16px;
  background-color: var(--surface);
  color: var(--text-primary);
  font-size: 12px;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.2s;
  white-space: nowrap;
  flex-shrink: 0;
}

.category-btn:hover {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.category-btn.active {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

/* 分类管理按钮 */
.category-manage-btn {
  padding: 6px 12px;
  border: 2px dashed var(--border);
  border-radius: 16px;
  background-color: transparent;
  color: var(--text-secondary);
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
  white-space: nowrap;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  gap: 4px;
  margin-left: auto;
}

.category-manage-btn:hover {
  background-color: var(--surface-hover);
  color: var(--text-primary);
  border-color: var(--primary);
}

.category-manage-btn .manage-icon {
  font-size: 14px;
}

.category-manage-btn .manage-text {
  font-size: 11px;
}

/* 分类管理模态框 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background-color: var(--surface);
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  max-width: 90vw;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.category-manager-modal {
  width: 400px;
  height: 500px;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid var(--border);
  background-color: var(--surface);
  min-height: 48px;
}

.modal-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.modal-close-btn {
  background: none;
  border: none;
  font-size: 24px;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  transition: all 0.2s;
}

.modal-close-btn:hover {
  background-color: var(--surface-hover);
  color: var(--text-primary);
}

.modal-body {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 分类列表 */
.category-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  flex: 1;
}

.category-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  border: 1px solid var(--border);
  border-radius: 8px;
  background-color: var(--background);
  transition: all 0.2s;
}

.category-item:hover {
  background-color: var(--surface-hover);
  border-color: var(--primary);
}

.category-info {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.category-icon {
  font-size: 16px;
}

.category-name {
  font-weight: 500;
  color: var(--text-primary);
}

.category-id {
  font-size: 12px;
  color: var(--text-secondary);
}

.category-actions {
  display: flex;
  gap: 6px;
}

/* 添加分类区域 */
.add-category-section {
  border-top: 1px solid var(--border);
  padding-top: 16px;
}

.input-group {
  display: flex;
  gap: 8px;
}

.form-control {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid var(--border);
  border-radius: 6px;
  background-color: var(--background);
  color: var(--text-primary);
  font-size: 14px;
  transition: all 0.2s;
}

.form-control:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.1);
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
}

.btn-primary {
  background-color: var(--primary);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-hover);
}

.btn-secondary {
  background-color: var(--surface);
  color: var(--text-primary);
  border: 1px solid var(--border);
}

.btn-secondary:hover {
  background-color: var(--surface-hover);
}

.btn-danger {
  background-color: var(--error);
  color: white;
}

.btn-danger:hover {
  background-color: var(--color-error-700);
}

.btn-sm {
  padding: 4px 8px;
  font-size: 12px;
}

.text-muted {
  color: var(--text-secondary);
  font-size: 12px;
}

/* 工具卡片分类标签 */
.tool-categories {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin-top: 8px;
}

.category-tag {
  background-color: var(--primary);
  color: white;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 10px;
  font-weight: 500;
}

/* 工具操作按钮 */
.tool-actions {
  display: flex;
  gap: 4px;
}

.tool-settings-btn {
  background: none;
  border: none;
  font-size: 14px;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s;
}

.tool-settings-btn:hover {
  background-color: var(--surface-hover);
  color: var(--text-primary);
}

/* 工具设置模态框 */
.tool-settings-modal {
  width: 450px;
  max-height: 600px;
}

.setting-section {
  margin-bottom: 24px;
}

.setting-section h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
}

.tool-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.tool-info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: var(--background);
  border-radius: 6px;
}

.tool-info-label {
  font-weight: 500;
  color: var(--text-secondary);
}

.tool-info-value {
  font-weight: 500;
  color: var(--text-primary);
}

.tool-info-value.enabled {
  color: var(--success);
}

.tool-info-value.disabled {
  color: var(--error);
}

/* 分类选择器 */
.categories-selector {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid var(--border);
  border-radius: 8px;
  padding: 12px;
}

.category-option {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
}

.category-option:hover {
  background-color: var(--surface-hover);
}

.category-option input[type="checkbox"] {
  margin: 0;
}

.category-option-icon {
  font-size: 16px;
}

.category-option-name {
  font-weight: 500;
  color: var(--text-primary);
}

/* 切换开关 */
.toggle-switch {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
}

.toggle-slider {
  position: relative;
  width: 44px;
  height: 24px;
  background-color: var(--border);
  border-radius: 12px;
  transition: all 0.2s;
}

.toggle-slider::before {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  width: 20px;
  height: 20px;
  background-color: white;
  border-radius: 50%;
  transition: all 0.2s;
}

.toggle-switch input[type="checkbox"] {
  display: none;
}

.toggle-switch input[type="checkbox"]:checked + .toggle-slider {
  background-color: var(--primary);
}

.toggle-switch input[type="checkbox"]:checked + .toggle-slider::before {
  transform: translateX(20px);
}

.toggle-label {
  font-weight: 500;
  color: var(--text-primary);
}

/* 模态框底部 */
.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 20px;
  border-top: 1px solid var(--border);
  background-color: var(--surface);
  min-height: 48px;
}

/* 拖拽功能样式 */
.drag-handle {
  position: absolute;
  top: 8px;
  left: 8px;
  color: var(--text-secondary);
  font-size: 12px;
  cursor: grab;
  opacity: 0;
  transition: opacity 0.2s;
  user-select: none;
  line-height: 1;
}

.tool-card:hover .drag-handle {
  opacity: 1;
}

.drag-handle:active {
  cursor: grabbing;
}

.tool-card.dragging {
  opacity: 0.5;
  transform: rotate(5deg);
  z-index: 1000;
}

.tool-card.drag-over {
  border-color: var(--primary);
  background-color: var(--surface-hover);
}

.tool-card.drag-over-top {
  border-top: 3px solid var(--primary);
}

.tool-card.drag-over-bottom {
  border-bottom: 3px solid var(--primary);
}

/* 调整工具卡片布局以适应拖拽手柄 */
.tool-card {
  position: relative;
  padding-left: 24px; /* 为拖拽手柄留出空间 */
}

/* 拖拽时的动画效果 */
.tool-card {
  transition: all 0.2s ease;
}

.tool-card:not(.dragging) {
  transition: transform 0.2s ease, border-color 0.2s ease, background-color 0.2s ease;
}

/* 工具容器 */
.tools-container {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  align-content: start;
}

.tools-container::-webkit-scrollbar {
  width: 6px;
}

.tools-container::-webkit-scrollbar-track {
  background: var(--surface);
}

.tools-container::-webkit-scrollbar-thumb {
  background: var(--border);
  border-radius: 3px;
}

.tools-container::-webkit-scrollbar-thumb:hover {
  background: var(--secondary-color);
}

/* 工具卡片 */
.tool-card {
  background-color: var(--surface);
  border: 1px solid var(--border);
  border-radius: 12px;
  padding: 14px;
  cursor: pointer;
  transition: all 0.2s;
  text-align: center;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.tool-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-color);
}

.tool-card.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.tool-card.disabled:hover {
  transform: none;
  box-shadow: var(--shadow);
  border-color: var(--border);
}

.tool-icon {
  font-size: 24px;
  margin-bottom: 8px;
  display: block;
}

.tool-name {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 4px;
  line-height: 1.2;
}

.tool-description {
  font-size: 12px;
  color: var(--text-primary);
  opacity: 0.8;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  flex: 1;
  margin-bottom: 8px;
}

/* 工具底部信息 */
.tool-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
  padding-top: 8px;
  border-top: 1px solid var(--border);
  border-top-color: transparent;
}

.tool-version {
  font-size: 10px;
  color: var(--text-primary);
  opacity: 0.6;
  font-weight: 500;
}

.update-btn {
  background: none;
  border: none;
  font-size: 12px;
  cursor: pointer;
  padding: 2px 4px;
  border-radius: 4px;
  transition: all 0.2s;
  opacity: 0.7;
}

.update-btn:hover {
  opacity: 1;
  background-color: var(--surface-hover);
}



@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.tool-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  background-color: var(--primary-color);
  color: white;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 8px;
  font-weight: 500;
}

.tool-badge.new {
  background-color: #10b981;
}

.tool-badge.beta {
  background-color: #f59e0b;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: var(--text-secondary);
}

.empty-state-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-state-text {
  font-size: 16px;
  margin-bottom: 8px;
}

.empty-state-subtext {
  font-size: 14px;
  opacity: 0.7;
}

/* 底部 */
.footer {
  padding: 12px 16px;
  background-color: var(--surface);
  border-top: 1px solid var(--border);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.settings-btn {
  padding: 6px 12px;
  border: 1px solid var(--border-hover);
  border-radius: 6px;
  background-color: var(--surface);
  color: var(--text-primary);
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.settings-btn:hover {
  background-color: var(--surface-hover);
  color: var(--text-primary);
  border-color: var(--border-hover);
}

.version {
  font-size: 11px;
  color: var(--text-primary);
  opacity: 0.6;
}

/* 响应式调整 - 针对更小屏幕的备用方案 */
@media (max-width: 450px) {
  body {
    width: 100%;
    min-width: 320px;
  }

  .tools-container {
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
  }

  .tool-card {
    padding: 12px;
  }

  .tool-icon {
    font-size: 20px;
  }

  .tool-name {
    font-size: 13px;
  }

  .tool-description {
    font-size: 11px;
  }
}

/* 加载动画 */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px;
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid var(--border);
  border-top: 2px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 工具卡片进入动画 */
.tool-card {
  animation: fadeInUp 0.3s ease-out;
  position: relative;
  overflow: hidden;
}

.tool-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.tool-card:hover::before {
  left: 100%;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 搜索输入框增强效果 */
.search-input {
  position: relative;
  transition: all 0.3s ease;
}

.search-input:focus {
  transform: scale(1.02);
}

/* 分类按钮增强效果 */
.category-btn {
  position: relative;
  overflow: hidden;
}

.category-btn::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.3s, height 0.3s;
}

.category-btn:active::after {
  width: 200px;
  height: 200px;
}

/* 工具图标旋转效果 */
.tool-card:hover .tool-icon {
  animation: iconBounce 0.6s ease;
}

@keyframes iconBounce {
  0%, 20%, 60%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  80% {
    transform: translateY(-5px);
  }
}

/* 徽章脉冲效果 */
.tool-badge {
  animation: badgePulse 2s infinite;
}

@keyframes badgePulse {
  0% {
    box-shadow: 0 0 0 0 rgba(79, 70, 229, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(79, 70, 229, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(79, 70, 229, 0);
  }
}

.tool-badge.new {
  animation: badgePulse 2s infinite;
}

.tool-badge.new {
  box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.7);
}

@keyframes badgePulse {
  0% {
    box-shadow: 0 0 0 0 currentColor;
  }
  70% {
    box-shadow: 0 0 0 10px transparent;
  }
  100% {
    box-shadow: 0 0 0 0 transparent;
  }
}

/* 设置按钮效果已移至上方统一定义 */

/* 滚动条美化 */
.tools-container::-webkit-scrollbar {
  width: 8px;
}

.tools-container::-webkit-scrollbar-track {
  background: var(--surface);
  border-radius: 4px;
}

.tools-container::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, var(--primary-color), var(--primary-hover));
  border-radius: 4px;
  transition: all 0.3s;
}

.tools-container::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, var(--primary-hover), var(--primary-color));
  transform: scaleY(1.1);
}

/* 工具卡片点击效果 */
.tool-card:active {
  transform: translateY(-2px) scale(0.98);
  transition: transform 0.1s;
}

/* 空状态动画 */
.empty-state {
  animation: fadeIn 0.5s ease-out;
}

.empty-state-icon {
  animation: float 3s ease-in-out infinite;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* 加载状态增强 */
.loading-spinner {
  animation: spin 1s linear infinite, pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 工具卡片网格动画 */
.tools-container {
  animation: gridFadeIn 0.5s ease-out;
}

@keyframes gridFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式动画调整 */
@media (prefers-reduced-motion: reduce) {
  .tool-card,
  .tool-card::before,
  .tool-card:hover .tool-icon,
  .category-btn::after,
  .search-input,
  .settings-btn,
  .loading-spinner,
  .empty-state,
  .empty-state-icon,
  .tools-container {
    animation: none !important;
    transition: none !important;
  }

  .tool-card:hover {
    transform: none;
  }
}

/* ========== 紧凑模式样式 ========== */
.compact-mode .header {
  padding: var(--spacing-3);
}

.compact-mode .title {
  font-size: var(--font-size-base);
  margin-bottom: var(--spacing-2);
}

.compact-mode .categories {
  padding: var(--spacing-2) var(--spacing-3);
  gap: var(--spacing-1);
}

.compact-mode .category-btn {
  padding: 4px 8px;
  font-size: 11px;
}

.compact-mode .tools-container {
  padding: var(--spacing-3);
  gap: var(--spacing-2);
  grid-template-columns: repeat(4, 1fr);
}

.compact-mode .tool-card {
  padding: var(--spacing-2);
}

.compact-mode .tool-icon {
  font-size: 20px;
  margin-bottom: 4px;
}

.compact-mode .tool-name {
  font-size: 12px;
  margin-bottom: 2px;
}

.compact-mode .tool-description {
  font-size: 10px;
  -webkit-line-clamp: 1;
  margin-bottom: 6px;
}

.compact-mode .tool-footer {
  padding-top: 6px;
}

.compact-mode .tool-version {
  font-size: 9px;
}

.compact-mode .update-btn {
  font-size: 10px;
  padding: 1px 3px;
}

.compact-mode .update-badge {
  font-size: 8px;
  padding: 1px 4px;
}

.compact-mode .footer {
  padding: var(--spacing-2) var(--spacing-3);
}

.compact-mode .settings-btn {
  padding: 4px 8px;
  font-size: 11px;
}

.compact-mode .version {
  font-size: 10px;
}
