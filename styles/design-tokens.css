/**
 * 设计令牌系统
 * 统一的设计规范，包括颜色、字体、间距、阴影等
 */

:root {
  /* ========== 颜色系统 ========== */
  
  /* 主色调 */
  --color-primary-50: #eef2ff;
  --color-primary-100: #e0e7ff;
  --color-primary-200: #c7d2fe;
  --color-primary-300: #a5b4fc;
  --color-primary-400: #818cf8;
  --color-primary-500: #6366f1;
  --color-primary-600: #4f46e5;
  --color-primary-700: #4338ca;
  --color-primary-800: #3730a3;
  --color-primary-900: #312e81;
  --color-primary-950: #1e1b4b;

  /* 中性色 */
  --color-gray-50: #f9fafb;
  --color-gray-100: #f3f4f6;
  --color-gray-200: #e5e7eb;
  --color-gray-300: #d1d5db;
  --color-gray-400: #9ca3af;
  --color-gray-500: #6b7280;
  --color-gray-600: #4b5563;
  --color-gray-700: #374151;
  --color-gray-800: #1f2937;
  --color-gray-900: #111827;
  --color-gray-950: #030712;

  /* 语义化颜色 */
  --color-success-50: #f0fdf4;
  --color-success-100: #dcfce7;
  --color-success-200: #bbf7d0;
  --color-success-400: #4ade80;
  --color-success-500: #22c55e;
  --color-success-600: #16a34a;
  --color-success-700: #15803d;
  --color-success-900: #14532d;

  --color-warning-50: #fffbeb;
  --color-warning-100: #fef3c7;
  --color-warning-200: #fde68a;
  --color-warning-400: #fbbf24;
  --color-warning-500: #f59e0b;
  --color-warning-600: #d97706;
  --color-warning-700: #b45309;
  --color-warning-900: #78350f;

  --color-error-50: #fef2f2;
  --color-error-100: #fee2e2;
  --color-error-200: #fecaca;
  --color-error-400: #f87171;
  --color-error-500: #ef4444;
  --color-error-600: #dc2626;
  --color-error-700: #b91c1c;
  --color-error-900: #7f1d1d;

  --color-info-50: #eff6ff;
  --color-info-100: #dbeafe;
  --color-info-200: #bfdbfe;
  --color-info-400: #60a5fa;
  --color-info-500: #3b82f6;
  --color-info-600: #2563eb;
  --color-info-700: #1d4ed8;
  --color-info-900: #1e3a8a;

  /* 扩展颜色系统 */
  --color-orange-50: #fff7ed;
  --color-orange-100: #ffedd5;
  --color-orange-200: #fed7aa;
  --color-orange-400: #fb923c;
  --color-orange-500: #f97316;
  --color-orange-600: #ea580c;
  --color-orange-700: #c2410c;
  --color-orange-900: #7c2d12;

  --color-green-50: #f0fdf4;
  --color-green-100: #dcfce7;
  --color-green-200: #bbf7d0;
  --color-green-400: #4ade80;
  --color-green-500: #22c55e;
  --color-green-600: #16a34a;
  --color-green-700: #15803d;
  --color-green-900: #14532d;

  --color-blue-50: #eff6ff;
  --color-blue-100: #dbeafe;
  --color-blue-200: #bfdbfe;
  --color-blue-400: #60a5fa;
  --color-blue-500: #3b82f6;
  --color-blue-600: #2563eb;
  --color-blue-700: #1d4ed8;
  --color-blue-900: #1e3a8a;

  --color-purple-50: #faf5ff;
  --color-purple-100: #f3e8ff;
  --color-purple-200: #e9d5ff;
  --color-purple-400: #c084fc;
  --color-purple-500: #a855f7;
  --color-purple-600: #9333ea;
  --color-purple-700: #7c3aed;
  --color-purple-900: #581c87;

  /* ========== 字体系统 ========== */
  
  /* 字体族 */
  --font-family-sans: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  --font-family-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;

  /* 字体大小 */
  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1rem;     /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-2xl: 1.5rem;    /* 24px */
  --font-size-3xl: 1.875rem;  /* 30px */
  --font-size-4xl: 2.25rem;   /* 36px */

  /* 字体粗细 */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* 行高 */
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;

  /* ========== 间距系统 ========== */
  
  --spacing-0: 0;
  --spacing-1: 0.25rem;   /* 4px */
  --spacing-2: 0.5rem;    /* 8px */
  --spacing-3: 0.75rem;   /* 12px */
  --spacing-4: 1rem;      /* 16px */
  --spacing-5: 1.25rem;   /* 20px */
  --spacing-6: 1.5rem;    /* 24px */
  --spacing-8: 2rem;      /* 32px */
  --spacing-10: 2.5rem;   /* 40px */
  --spacing-12: 3rem;     /* 48px */
  --spacing-16: 4rem;     /* 64px */
  --spacing-20: 5rem;     /* 80px */
  --spacing-24: 6rem;     /* 96px */

  /* ========== 圆角系统 ========== */
  
  --radius-none: 0;
  --radius-sm: 0.125rem;   /* 2px */
  --radius-base: 0.25rem;  /* 4px */
  --radius-md: 0.375rem;   /* 6px */
  --radius-lg: 0.5rem;     /* 8px */
  --radius-xl: 0.75rem;    /* 12px */
  --radius-2xl: 1rem;      /* 16px */
  --radius-full: 9999px;

  /* ========== 阴影系统 ========== */
  
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

  /* ========== 动画系统 ========== */
  
  --duration-75: 75ms;
  --duration-100: 100ms;
  --duration-150: 150ms;
  --duration-200: 200ms;
  --duration-300: 300ms;
  --duration-500: 500ms;
  --duration-700: 700ms;
  --duration-1000: 1000ms;

  --ease-linear: linear;
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);

  /* ========== Z-index 系统 ========== */
  
  --z-index-dropdown: 1000;
  --z-index-sticky: 1020;
  --z-index-fixed: 1030;
  --z-index-modal-backdrop: 1040;
  --z-index-modal: 1050;
  --z-index-popover: 1060;
  --z-index-tooltip: 1070;
  --z-index-toast: 1080;

  /* ========== 语义化令牌 ========== */
  
  /* 主题颜色 */
  --primary: var(--color-primary-600);
  --primary-hover: var(--color-primary-700);
  --primary-active: var(--color-primary-800);
  --primary-light: var(--color-primary-100);
  --primary-dark: var(--color-primary-900);

  /* 背景颜色 */
  --background: var(--color-gray-50);
  --surface: var(--color-gray-100);
  --surface-hover: var(--color-gray-200);

  /* 文本颜色 */
  --text-primary: var(--color-gray-900);
  --text-secondary: var(--color-gray-600);
  --text-tertiary: var(--color-gray-500);
  --text-disabled: var(--color-gray-400);

  /* 边框颜色 */
  --border: var(--color-gray-200);
  --border-hover: var(--color-gray-300);
  --border-focus: var(--primary);

  /* 状态颜色 */
  --success: var(--color-success-600);
  --warning: var(--color-warning-600);
  --error: var(--color-error-600);
  --info: var(--color-info-600);

  /* 组件特定令牌 */
  --button-padding-x: var(--spacing-4);
  --button-padding-y: var(--spacing-2);
  --button-radius: var(--radius-md);
  --button-font-weight: var(--font-weight-medium);

  --input-padding-x: var(--spacing-3);
  --input-padding-y: var(--spacing-2);
  --input-radius: var(--radius-md);
  --input-border-width: 1px;

  /* 模态框组件统一规范 */
  --modal-header-padding: var(--spacing-4) var(--spacing-5); /* 16px 20px */
  --modal-body-padding: var(--spacing-5); /* 20px */
  --modal-footer-padding: var(--spacing-4) var(--spacing-5); /* 16px 20px */
  --modal-header-min-height: 3rem; /* 48px */
  --modal-footer-min-height: 3rem; /* 48px */
  --modal-title-font-size: var(--font-size-lg);
  --modal-title-font-weight: var(--font-weight-semibold);
  --modal-radius: var(--radius-xl);
  --modal-shadow: var(--shadow-2xl);

  --card-padding: var(--spacing-4);
  --card-radius: var(--radius-lg);
  --card-shadow: var(--shadow-base);
}

/* ========== 自动主题（跟随系统） ========== */
@media (prefers-color-scheme: dark) {
  :root:not([data-theme]) {
    /* 只有在没有手动设置主题时才应用系统深色主题 */
    /* 主题颜色调整 */
    --primary: var(--color-primary-500);
    --primary-hover: var(--color-primary-400);
    --primary-active: var(--color-primary-300);
    --primary-light: var(--color-primary-900);
    --primary-dark: var(--color-primary-100);

    /* 背景颜色 */
    --background: var(--color-gray-900);
    --surface: var(--color-gray-800);
    --surface-hover: var(--color-gray-700);

    /* 文本颜色 */
    --text-primary: var(--color-gray-100);
    --text-secondary: var(--color-gray-300);
    --text-tertiary: var(--color-gray-400);
    --text-disabled: var(--color-gray-600);

    /* 边框颜色 */
    --border: var(--color-gray-700);
    --border-hover: var(--color-gray-600);

    /* 自动深色主题下的状态颜色 */
    --success: var(--color-success-400);
    --warning: var(--color-warning-400);
    --error: var(--color-error-400);
    --info: var(--color-info-400);

    /* 阴影调整 */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
    --shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.4), 0 1px 2px 0 rgba(0, 0, 0, 0.3);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.3);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.6);
  }
}

/* ========== 强制主题模式 ========== */
[data-theme="light"] {
  color-scheme: light;
  /* 强制使用浅色主题令牌 */
  --primary: var(--color-primary-600);
  --primary-hover: var(--color-primary-700);
  --primary-active: var(--color-primary-800);
  --primary-light: var(--color-primary-100);
  --primary-dark: var(--color-primary-900);

  --background: var(--color-gray-50);
  --surface: var(--color-gray-100);
  --surface-hover: var(--color-gray-200);

  --text-primary: var(--color-gray-900);
  --text-secondary: var(--color-gray-700);
  --text-tertiary: var(--color-gray-500);
  --text-disabled: var(--color-gray-400);

  --border: var(--color-gray-300);
  --border-hover: var(--color-gray-400);

  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

[data-theme="dark"] {
  color-scheme: dark;
  /* 强制使用深色主题令牌 */
  --primary: var(--color-primary-500);
  --primary-hover: var(--color-primary-400);
  --primary-active: var(--color-primary-300);
  --primary-light: var(--color-primary-900);
  --primary-dark: var(--color-primary-100);

  --background: var(--color-gray-900);
  --surface: var(--color-gray-800);
  --surface-hover: var(--color-gray-700);

  --text-primary: var(--color-gray-100);
  --text-secondary: var(--color-gray-300);
  --text-tertiary: var(--color-gray-400);
  --text-disabled: var(--color-gray-600);

  --border: var(--color-gray-700);
  --border-hover: var(--color-gray-600);

  /* 深色主题下的状态颜色 */
  --success: var(--color-success-400);
  --warning: var(--color-warning-400);
  --error: var(--color-error-400);
  --info: var(--color-info-400);

  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.4), 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.3);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.6);
}
