/**
 * 动画样式模块
 * 基于设计令牌的动画系统
 */

/* ========== 关键帧动画 ========== */

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes slideInUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutUp {
  from {
    transform: translateY(0);
    opacity: 1;
  }
  to {
    transform: translateY(-100%);
    opacity: 0;
  }
}

@keyframes slideOutDown {
  from {
    transform: translateY(0);
    opacity: 1;
  }
  to {
    transform: translateY(100%);
    opacity: 0;
  }
}

@keyframes slideOutLeft {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(-100%);
    opacity: 0;
  }
}

@keyframes slideOutRight {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.8);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes scaleOut {
  from {
    transform: scale(1);
    opacity: 1;
  }
  to {
    transform: scale(0.8);
    opacity: 0;
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
    transform: translate3d(0, -30px, 0);
  }
  70% {
    animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
    transform: translate3d(0, -15px, 0);
  }
  90% {
    transform: translate3d(0, -4px, 0);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-10px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(10px);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes ping {
  75%, 100% {
    transform: scale(2);
    opacity: 0;
  }
}

@keyframes wiggle {
  0%, 100% {
    transform: rotate(-3deg);
  }
  50% {
    transform: rotate(3deg);
  }
}

/* ========== 动画工具类 ========== */

/* 淡入淡出 */
.animate-fade-in {
  animation: fadeIn var(--duration-300) var(--ease-out) forwards;
}

.animate-fade-out {
  animation: fadeOut var(--duration-300) var(--ease-in) forwards;
}

/* 滑动进入 */
.animate-slide-in-up {
  animation: slideInUp var(--duration-300) var(--ease-out) forwards;
}

.animate-slide-in-down {
  animation: slideInDown var(--duration-300) var(--ease-out) forwards;
}

.animate-slide-in-left {
  animation: slideInLeft var(--duration-300) var(--ease-out) forwards;
}

.animate-slide-in-right {
  animation: slideInRight var(--duration-300) var(--ease-out) forwards;
}

/* 滑动退出 */
.animate-slide-out-up {
  animation: slideOutUp var(--duration-300) var(--ease-in) forwards;
}

.animate-slide-out-down {
  animation: slideOutDown var(--duration-300) var(--ease-in) forwards;
}

.animate-slide-out-left {
  animation: slideOutLeft var(--duration-300) var(--ease-in) forwards;
}

.animate-slide-out-right {
  animation: slideOutRight var(--duration-300) var(--ease-in) forwards;
}

/* 缩放 */
.animate-scale-in {
  animation: scaleIn var(--duration-200) var(--ease-out) forwards;
}

.animate-scale-out {
  animation: scaleOut var(--duration-200) var(--ease-in) forwards;
}

/* 特效动画 */
.animate-bounce {
  animation: bounce var(--duration-1000) infinite;
}

.animate-pulse {
  animation: pulse var(--duration-1000) var(--ease-in-out) infinite;
}

.animate-shake {
  animation: shake var(--duration-500) var(--ease-in-out);
}

.animate-spin {
  animation: spin var(--duration-1000) linear infinite;
}

.animate-ping {
  animation: ping var(--duration-1000) cubic-bezier(0, 0, 0.2, 1) infinite;
}

.animate-wiggle {
  animation: wiggle var(--duration-1000) var(--ease-in-out) infinite;
}

/* ========== 悬停动画 ========== */
.hover-lift {
  transition: transform var(--duration-200) var(--ease-out);
}

.hover-lift:hover {
  transform: translateY(-2px);
}

.hover-grow {
  transition: transform var(--duration-200) var(--ease-out);
}

.hover-grow:hover {
  transform: scale(1.05);
}

.hover-shrink {
  transition: transform var(--duration-200) var(--ease-out);
}

.hover-shrink:hover {
  transform: scale(0.95);
}

.hover-rotate {
  transition: transform var(--duration-200) var(--ease-out);
}

.hover-rotate:hover {
  transform: rotate(5deg);
}

.hover-skew {
  transition: transform var(--duration-200) var(--ease-out);
}

.hover-skew:hover {
  transform: skew(-5deg, -5deg);
}

/* ========== 焦点动画 ========== */
.focus-ring {
  transition: box-shadow var(--duration-150) var(--ease-out);
}

.focus-ring:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.3);
}

.focus-scale {
  transition: transform var(--duration-150) var(--ease-out);
}

.focus-scale:focus {
  transform: scale(1.02);
}

/* ========== 加载动画 ========== */
.loading-dots {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-1);
}

.loading-dots::after {
  content: '';
  display: inline-block;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background-color: currentColor;
  animation: loadingDots 1.4s infinite ease-in-out both;
}

.loading-dots::before {
  content: '';
  display: inline-block;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background-color: currentColor;
  animation: loadingDots 1.4s infinite ease-in-out both;
  animation-delay: -0.16s;
  margin-right: var(--spacing-1);
}

@keyframes loadingDots {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* ========== 进度动画 ========== */
.progress-indeterminate {
  position: relative;
  overflow: hidden;
}

.progress-indeterminate::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  animation: progressIndeterminate 2s infinite;
}

@keyframes progressIndeterminate {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* ========== 响应式动画控制 ========== */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* ========== 动画状态类 ========== */
.no-animations * {
  animation: none !important;
  transition: none !important;
}

.paused {
  animation-play-state: paused !important;
}

.running {
  animation-play-state: running !important;
}

/* ========== 延迟动画 ========== */
.delay-75 { animation-delay: var(--duration-75); }
.delay-100 { animation-delay: var(--duration-100); }
.delay-150 { animation-delay: var(--duration-150); }
.delay-200 { animation-delay: var(--duration-200); }
.delay-300 { animation-delay: var(--duration-300); }
.delay-500 { animation-delay: var(--duration-500); }
.delay-700 { animation-delay: var(--duration-700); }
.delay-1000 { animation-delay: var(--duration-1000); }
