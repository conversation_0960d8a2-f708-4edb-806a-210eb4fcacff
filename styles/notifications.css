/**
 * 通知组件样式
 */

/* 通知容器 */
.notification-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 10000;
  display: flex;
  flex-direction: column;
  gap: 12px;
  pointer-events: none;
}

/* 通知基础样式 */
.notification {
  min-width: 300px;
  max-width: 400px;
  background: var(--surface);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-left: 4px solid;
  opacity: 0;
  transform: translateX(100%);
  transition: all 0.3s ease;
  pointer-events: auto;
}

.notification-show {
  opacity: 1;
  transform: translateX(0);
}

.notification-hide {
  opacity: 0;
  transform: translateX(100%);
}

/* 通知类型样式 */
.notification-success {
  border-left-color: var(--success);
}

.notification-error {
  border-left-color: var(--error);
}

.notification-warning {
  border-left-color: var(--warning);
}

.notification-info {
  border-left-color: var(--primary);
}

/* 通知内容 */
.notification-content {
  display: flex;
  align-items: flex-start;
  padding: 16px;
  gap: 12px;
}

.notification-icon {
  font-size: 18px;
  flex-shrink: 0;
  margin-top: 2px;
}

.notification-body {
  flex: 1;
  min-width: 0;
}

.notification-title {
  font-weight: 600;
  font-size: 14px;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.notification-message {
  font-size: 13px;
  color: var(--text-secondary);
  line-height: 1.4;
  word-wrap: break-word;
}

.notification-close {
  background: none;
  border: none;
  font-size: 18px;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s;
  flex-shrink: 0;
}

.notification-close:hover {
  background-color: var(--surface-hover);
  color: var(--text-primary);
}

/* 确认对话框样式 */
.confirm-modal .modal-content {
  width: 400px;
  max-width: 90vw;
}

.confirm-modal-content {
  border-top: 4px solid var(--primary);
}

.confirm-modal-content.confirm-danger {
  border-top-color: var(--error);
}

.confirm-message {
  font-size: 14px;
  color: var(--text-primary);
  line-height: 1.5;
  text-align: center;
  padding: 20px 0;
}

.confirm-modal .modal-footer {
  justify-content: center;
  gap: 16px;
}

.confirm-modal .btn {
  min-width: 80px;
}

/* 输入对话框样式 */
.prompt-modal .modal-content {
  width: 400px;
  max-width: 90vw;
}

.prompt-modal-content {
  border-top: 4px solid var(--primary);
}

.prompt-message {
  font-size: 14px;
  color: var(--text-primary);
  line-height: 1.5;
  margin-bottom: 16px;
}

.prompt-input {
  width: 100%;
  padding: 10px 12px;
  border: 2px solid var(--border);
  border-radius: 6px;
  background-color: var(--background);
  color: var(--text-primary);
  font-size: 14px;
  transition: all 0.2s;
}

.prompt-input:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.prompt-modal .modal-footer {
  justify-content: flex-end;
  gap: 12px;
}

.prompt-modal .btn {
  min-width: 80px;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .notification-container {
    top: 10px;
    right: 10px;
    left: 10px;
  }
  
  .notification {
    min-width: auto;
    max-width: none;
  }
  
  .confirm-modal .modal-content {
    width: 90vw;
    margin: 20px;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .notification {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }
}

/* 动画增强 */
@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideOutRight {
  from {
    opacity: 1;
    transform: translateX(0);
  }
  to {
    opacity: 0;
    transform: translateX(100%);
  }
}

.notification-show {
  animation: slideInRight 0.3s ease;
}

.notification-hide {
  animation: slideOutRight 0.3s ease;
}

/* 悬停效果 */
.notification:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
  transform: translateY(-2px);
}

/* 确认对话框进入动画 */
.confirm-modal {
  animation: fadeIn 0.2s ease;
}

.confirm-modal .modal-content {
  animation: scaleIn 0.2s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes scaleIn {
  from { 
    opacity: 0;
    transform: scale(0.9);
  }
  to { 
    opacity: 1;
    transform: scale(1);
  }
}
