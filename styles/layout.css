/**
 * 布局样式模块
 * 基于设计令牌的布局系统
 */

/* ========== 容器系统 ========== */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-4);
}

.container-sm {
  max-width: 640px;
}

.container-md {
  max-width: 768px;
}

.container-lg {
  max-width: 1024px;
}

.container-xl {
  max-width: 1280px;
}

.container-fluid {
  max-width: none;
}

/* ========== Flexbox 布局 ========== */
.flex {
  display: flex;
}

.inline-flex {
  display: inline-flex;
}

.flex-col {
  flex-direction: column;
}

.flex-row {
  flex-direction: row;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-nowrap {
  flex-wrap: nowrap;
}

/* Flex对齐 */
.justify-start {
  justify-content: flex-start;
}

.justify-center {
  justify-content: center;
}

.justify-end {
  justify-content: flex-end;
}

.justify-between {
  justify-content: space-between;
}

.justify-around {
  justify-content: space-around;
}

.justify-evenly {
  justify-content: space-evenly;
}

.items-start {
  align-items: flex-start;
}

.items-center {
  align-items: center;
}

.items-end {
  align-items: flex-end;
}

.items-stretch {
  align-items: stretch;
}

.items-baseline {
  align-items: baseline;
}

/* Flex增长和收缩 */
.flex-1 {
  flex: 1 1 0%;
}

.flex-auto {
  flex: 1 1 auto;
}

.flex-initial {
  flex: 0 1 auto;
}

.flex-none {
  flex: none;
}

.flex-grow {
  flex-grow: 1;
}

.flex-shrink {
  flex-shrink: 1;
}

/* ========== Grid 布局 ========== */
.grid {
  display: grid;
}

.inline-grid {
  display: inline-grid;
}

/* Grid列 */
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
.grid-cols-5 { grid-template-columns: repeat(5, minmax(0, 1fr)); }
.grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }
.grid-cols-12 { grid-template-columns: repeat(12, minmax(0, 1fr)); }

/* Grid行 */
.grid-rows-1 { grid-template-rows: repeat(1, minmax(0, 1fr)); }
.grid-rows-2 { grid-template-rows: repeat(2, minmax(0, 1fr)); }
.grid-rows-3 { grid-template-rows: repeat(3, minmax(0, 1fr)); }
.grid-rows-4 { grid-template-rows: repeat(4, minmax(0, 1fr)); }
.grid-rows-5 { grid-template-rows: repeat(5, minmax(0, 1fr)); }
.grid-rows-6 { grid-template-rows: repeat(6, minmax(0, 1fr)); }

/* Grid间距 */
.gap-0 { gap: 0; }
.gap-1 { gap: var(--spacing-1); }
.gap-2 { gap: var(--spacing-2); }
.gap-3 { gap: var(--spacing-3); }
.gap-4 { gap: var(--spacing-4); }
.gap-5 { gap: var(--spacing-5); }
.gap-6 { gap: var(--spacing-6); }
.gap-8 { gap: var(--spacing-8); }

.gap-x-0 { column-gap: 0; }
.gap-x-1 { column-gap: var(--spacing-1); }
.gap-x-2 { column-gap: var(--spacing-2); }
.gap-x-3 { column-gap: var(--spacing-3); }
.gap-x-4 { column-gap: var(--spacing-4); }
.gap-x-5 { column-gap: var(--spacing-5); }
.gap-x-6 { column-gap: var(--spacing-6); }
.gap-x-8 { column-gap: var(--spacing-8); }

.gap-y-0 { row-gap: 0; }
.gap-y-1 { row-gap: var(--spacing-1); }
.gap-y-2 { row-gap: var(--spacing-2); }
.gap-y-3 { row-gap: var(--spacing-3); }
.gap-y-4 { row-gap: var(--spacing-4); }
.gap-y-5 { row-gap: var(--spacing-5); }
.gap-y-6 { row-gap: var(--spacing-6); }
.gap-y-8 { row-gap: var(--spacing-8); }

/* ========== 定位系统 ========== */
.static {
  position: static;
}

.fixed {
  position: fixed;
}

.absolute {
  position: absolute;
}

.relative {
  position: relative;
}

.sticky {
  position: sticky;
}

/* 定位值 */
.top-0 { top: 0; }
.top-1 { top: var(--spacing-1); }
.top-2 { top: var(--spacing-2); }
.top-4 { top: var(--spacing-4); }
.top-8 { top: var(--spacing-8); }

.right-0 { right: 0; }
.right-1 { right: var(--spacing-1); }
.right-2 { right: var(--spacing-2); }
.right-4 { right: var(--spacing-4); }
.right-8 { right: var(--spacing-8); }

.bottom-0 { bottom: 0; }
.bottom-1 { bottom: var(--spacing-1); }
.bottom-2 { bottom: var(--spacing-2); }
.bottom-4 { bottom: var(--spacing-4); }
.bottom-8 { bottom: var(--spacing-8); }

.left-0 { left: 0; }
.left-1 { left: var(--spacing-1); }
.left-2 { left: var(--spacing-2); }
.left-4 { left: var(--spacing-4); }
.left-8 { left: var(--spacing-8); }

/* ========== 显示和可见性 ========== */
.block {
  display: block;
}

.inline-block {
  display: inline-block;
}

.inline {
  display: inline;
}

.hidden {
  display: none;
}

.invisible {
  visibility: hidden;
}

.visible {
  visibility: visible;
}

/* ========== 溢出处理 ========== */
.overflow-auto {
  overflow: auto;
}

.overflow-hidden {
  overflow: hidden;
}

.overflow-visible {
  overflow: visible;
}

.overflow-scroll {
  overflow: scroll;
}

.overflow-x-auto {
  overflow-x: auto;
}

.overflow-x-hidden {
  overflow-x: hidden;
}

.overflow-y-auto {
  overflow-y: auto;
}

.overflow-y-hidden {
  overflow-y: hidden;
}

/* ========== Z-index ========== */
.z-0 { z-index: 0; }
.z-10 { z-index: 10; }
.z-20 { z-index: 20; }
.z-30 { z-index: 30; }
.z-40 { z-index: 40; }
.z-50 { z-index: 50; }
.z-auto { z-index: auto; }

/* ========== 响应式断点 ========== */
@media (min-width: 640px) {
  .sm\:block { display: block; }
  .sm\:hidden { display: none; }
  .sm\:flex { display: flex; }
  .sm\:grid { display: grid; }
  .sm\:flex-col { flex-direction: column; }
  .sm\:flex-row { flex-direction: row; }
  .sm\:justify-center { justify-content: center; }
  .sm\:items-center { align-items: center; }
}

@media (min-width: 768px) {
  .md\:block { display: block; }
  .md\:hidden { display: none; }
  .md\:flex { display: flex; }
  .md\:grid { display: grid; }
  .md\:flex-col { flex-direction: column; }
  .md\:flex-row { flex-direction: row; }
  .md\:justify-center { justify-content: center; }
  .md\:items-center { align-items: center; }
  .md\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .md\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .md\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
}

@media (min-width: 1024px) {
  .lg\:block { display: block; }
  .lg\:hidden { display: none; }
  .lg\:flex { display: flex; }
  .lg\:grid { display: grid; }
  .lg\:flex-col { flex-direction: column; }
  .lg\:flex-row { flex-direction: row; }
  .lg\:justify-center { justify-content: center; }
  .lg\:items-center { align-items: center; }
  .lg\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .lg\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  .lg\:grid-cols-5 { grid-template-columns: repeat(5, minmax(0, 1fr)); }
  .lg\:grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }
}
