/**
 * 工具类样式模块
 * 基于设计令牌的原子化CSS工具类
 */

/* ========== 间距工具类 ========== */

/* 外边距 */
.m-0 { margin: 0; }
.m-1 { margin: var(--spacing-1); }
.m-2 { margin: var(--spacing-2); }
.m-3 { margin: var(--spacing-3); }
.m-4 { margin: var(--spacing-4); }
.m-5 { margin: var(--spacing-5); }
.m-6 { margin: var(--spacing-6); }
.m-8 { margin: var(--spacing-8); }
.m-10 { margin: var(--spacing-10); }
.m-12 { margin: var(--spacing-12); }
.m-auto { margin: auto; }

/* 水平外边距 */
.mx-0 { margin-left: 0; margin-right: 0; }
.mx-1 { margin-left: var(--spacing-1); margin-right: var(--spacing-1); }
.mx-2 { margin-left: var(--spacing-2); margin-right: var(--spacing-2); }
.mx-3 { margin-left: var(--spacing-3); margin-right: var(--spacing-3); }
.mx-4 { margin-left: var(--spacing-4); margin-right: var(--spacing-4); }
.mx-5 { margin-left: var(--spacing-5); margin-right: var(--spacing-5); }
.mx-6 { margin-left: var(--spacing-6); margin-right: var(--spacing-6); }
.mx-8 { margin-left: var(--spacing-8); margin-right: var(--spacing-8); }
.mx-auto { margin-left: auto; margin-right: auto; }

/* 垂直外边距 */
.my-0 { margin-top: 0; margin-bottom: 0; }
.my-1 { margin-top: var(--spacing-1); margin-bottom: var(--spacing-1); }
.my-2 { margin-top: var(--spacing-2); margin-bottom: var(--spacing-2); }
.my-3 { margin-top: var(--spacing-3); margin-bottom: var(--spacing-3); }
.my-4 { margin-top: var(--spacing-4); margin-bottom: var(--spacing-4); }
.my-5 { margin-top: var(--spacing-5); margin-bottom: var(--spacing-5); }
.my-6 { margin-top: var(--spacing-6); margin-bottom: var(--spacing-6); }
.my-8 { margin-top: var(--spacing-8); margin-bottom: var(--spacing-8); }

/* 单向外边距 */
.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--spacing-1); }
.mt-2 { margin-top: var(--spacing-2); }
.mt-3 { margin-top: var(--spacing-3); }
.mt-4 { margin-top: var(--spacing-4); }
.mt-5 { margin-top: var(--spacing-5); }
.mt-6 { margin-top: var(--spacing-6); }
.mt-8 { margin-top: var(--spacing-8); }

.mr-0 { margin-right: 0; }
.mr-1 { margin-right: var(--spacing-1); }
.mr-2 { margin-right: var(--spacing-2); }
.mr-3 { margin-right: var(--spacing-3); }
.mr-4 { margin-right: var(--spacing-4); }
.mr-5 { margin-right: var(--spacing-5); }
.mr-6 { margin-right: var(--spacing-6); }
.mr-8 { margin-right: var(--spacing-8); }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-1); }
.mb-2 { margin-bottom: var(--spacing-2); }
.mb-3 { margin-bottom: var(--spacing-3); }
.mb-4 { margin-bottom: var(--spacing-4); }
.mb-5 { margin-bottom: var(--spacing-5); }
.mb-6 { margin-bottom: var(--spacing-6); }
.mb-8 { margin-bottom: var(--spacing-8); }

.ml-0 { margin-left: 0; }
.ml-1 { margin-left: var(--spacing-1); }
.ml-2 { margin-left: var(--spacing-2); }
.ml-3 { margin-left: var(--spacing-3); }
.ml-4 { margin-left: var(--spacing-4); }
.ml-5 { margin-left: var(--spacing-5); }
.ml-6 { margin-left: var(--spacing-6); }
.ml-8 { margin-left: var(--spacing-8); }

/* 内边距 */
.p-0 { padding: 0; }
.p-1 { padding: var(--spacing-1); }
.p-2 { padding: var(--spacing-2); }
.p-3 { padding: var(--spacing-3); }
.p-4 { padding: var(--spacing-4); }
.p-5 { padding: var(--spacing-5); }
.p-6 { padding: var(--spacing-6); }
.p-8 { padding: var(--spacing-8); }
.p-10 { padding: var(--spacing-10); }
.p-12 { padding: var(--spacing-12); }

/* 水平内边距 */
.px-0 { padding-left: 0; padding-right: 0; }
.px-1 { padding-left: var(--spacing-1); padding-right: var(--spacing-1); }
.px-2 { padding-left: var(--spacing-2); padding-right: var(--spacing-2); }
.px-3 { padding-left: var(--spacing-3); padding-right: var(--spacing-3); }
.px-4 { padding-left: var(--spacing-4); padding-right: var(--spacing-4); }
.px-5 { padding-left: var(--spacing-5); padding-right: var(--spacing-5); }
.px-6 { padding-left: var(--spacing-6); padding-right: var(--spacing-6); }
.px-8 { padding-left: var(--spacing-8); padding-right: var(--spacing-8); }

/* 垂直内边距 */
.py-0 { padding-top: 0; padding-bottom: 0; }
.py-1 { padding-top: var(--spacing-1); padding-bottom: var(--spacing-1); }
.py-2 { padding-top: var(--spacing-2); padding-bottom: var(--spacing-2); }
.py-3 { padding-top: var(--spacing-3); padding-bottom: var(--spacing-3); }
.py-4 { padding-top: var(--spacing-4); padding-bottom: var(--spacing-4); }
.py-5 { padding-top: var(--spacing-5); padding-bottom: var(--spacing-5); }
.py-6 { padding-top: var(--spacing-6); padding-bottom: var(--spacing-6); }
.py-8 { padding-top: var(--spacing-8); padding-bottom: var(--spacing-8); }

/* ========== 文本工具类 ========== */

/* 字体大小 */
.text-xs { font-size: var(--font-size-xs); }
.text-sm { font-size: var(--font-size-sm); }
.text-base { font-size: var(--font-size-base); }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }
.text-2xl { font-size: var(--font-size-2xl); }
.text-3xl { font-size: var(--font-size-3xl); }
.text-4xl { font-size: var(--font-size-4xl); }

/* 字体粗细 */
.font-light { font-weight: var(--font-weight-light); }
.font-normal { font-weight: var(--font-weight-normal); }
.font-medium { font-weight: var(--font-weight-medium); }
.font-semibold { font-weight: var(--font-weight-semibold); }
.font-bold { font-weight: var(--font-weight-bold); }

/* 行高 */
.leading-tight { line-height: var(--line-height-tight); }
.leading-normal { line-height: var(--line-height-normal); }
.leading-relaxed { line-height: var(--line-height-relaxed); }

/* 文本对齐 */
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-justify { text-align: justify; }

/* 文本颜色 */
.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-tertiary { color: var(--text-tertiary); }
.text-disabled { color: var(--text-disabled); }
.text-success { color: var(--success); }
.text-warning { color: var(--warning); }
.text-error { color: var(--error); }
.text-info { color: var(--info); }

/* 文本装饰 */
.underline { text-decoration: underline; }
.line-through { text-decoration: line-through; }
.no-underline { text-decoration: none; }

/* 文本转换 */
.uppercase { text-transform: uppercase; }
.lowercase { text-transform: lowercase; }
.capitalize { text-transform: capitalize; }
.normal-case { text-transform: none; }

/* 文本溢出 */
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-ellipsis {
  text-overflow: ellipsis;
}

.text-clip {
  text-overflow: clip;
}

/* ========== 背景工具类 ========== */
.bg-transparent { background-color: transparent; }
.bg-primary { background-color: var(--primary); }
.bg-surface { background-color: var(--surface); }
.bg-background { background-color: var(--background); }
.bg-success { background-color: var(--success); }
.bg-warning { background-color: var(--warning); }
.bg-error { background-color: var(--error); }
.bg-info { background-color: var(--info); }

/* ========== 边框工具类 ========== */
.border { border: 1px solid var(--border); }
.border-0 { border: 0; }
.border-2 { border: 2px solid var(--border); }
.border-4 { border: 4px solid var(--border); }

.border-t { border-top: 1px solid var(--border); }
.border-r { border-right: 1px solid var(--border); }
.border-b { border-bottom: 1px solid var(--border); }
.border-l { border-left: 1px solid var(--border); }

.border-primary { border-color: var(--primary); }
.border-success { border-color: var(--success); }
.border-warning { border-color: var(--warning); }
.border-error { border-color: var(--error); }

/* 圆角 */
.rounded-none { border-radius: var(--radius-none); }
.rounded-sm { border-radius: var(--radius-sm); }
.rounded { border-radius: var(--radius-base); }
.rounded-md { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-2xl { border-radius: var(--radius-2xl); }
.rounded-full { border-radius: var(--radius-full); }

/* ========== 阴影工具类 ========== */
.shadow-none { box-shadow: none; }
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow { box-shadow: var(--shadow-base); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }
.shadow-2xl { box-shadow: var(--shadow-2xl); }

/* ========== 尺寸工具类 ========== */
.w-auto { width: auto; }
.w-full { width: 100%; }
.w-screen { width: 100vw; }
.w-min { width: min-content; }
.w-max { width: max-content; }
.w-fit { width: fit-content; }

.h-auto { height: auto; }
.h-full { height: 100%; }
.h-screen { height: 100vh; }
.h-min { height: min-content; }
.h-max { height: max-content; }
.h-fit { height: fit-content; }

/* 固定尺寸 */
.w-0 { width: 0; }
.w-1 { width: var(--spacing-1); }
.w-2 { width: var(--spacing-2); }
.w-3 { width: var(--spacing-3); }
.w-4 { width: var(--spacing-4); }
.w-5 { width: var(--spacing-5); }
.w-6 { width: var(--spacing-6); }
.w-8 { width: var(--spacing-8); }
.w-10 { width: var(--spacing-10); }
.w-12 { width: var(--spacing-12); }

.h-0 { height: 0; }
.h-1 { height: var(--spacing-1); }
.h-2 { height: var(--spacing-2); }
.h-3 { height: var(--spacing-3); }
.h-4 { height: var(--spacing-4); }
.h-5 { height: var(--spacing-5); }
.h-6 { height: var(--spacing-6); }
.h-8 { height: var(--spacing-8); }
.h-10 { height: var(--spacing-10); }
.h-12 { height: var(--spacing-12); }

/* ========== 透明度工具类 ========== */
.opacity-0 { opacity: 0; }
.opacity-25 { opacity: 0.25; }
.opacity-50 { opacity: 0.5; }
.opacity-75 { opacity: 0.75; }
.opacity-100 { opacity: 1; }

/* ========== 光标工具类 ========== */
.cursor-auto { cursor: auto; }
.cursor-default { cursor: default; }
.cursor-pointer { cursor: pointer; }
.cursor-wait { cursor: wait; }
.cursor-text { cursor: text; }
.cursor-move { cursor: move; }
.cursor-help { cursor: help; }
.cursor-not-allowed { cursor: not-allowed; }

/* ========== 用户选择工具类 ========== */
.select-none { user-select: none; }
.select-text { user-select: text; }
.select-all { user-select: all; }
.select-auto { user-select: auto; }

/* ========== 指针事件工具类 ========== */
.pointer-events-none { pointer-events: none; }
.pointer-events-auto { pointer-events: auto; }

/* ========== 过渡动画工具类 ========== */
.transition-none { transition: none; }
.transition-all { transition: all var(--duration-150) var(--ease-in-out); }
.transition { transition: color var(--duration-150) var(--ease-in-out), 
                        background-color var(--duration-150) var(--ease-in-out), 
                        border-color var(--duration-150) var(--ease-in-out), 
                        text-decoration-color var(--duration-150) var(--ease-in-out), 
                        fill var(--duration-150) var(--ease-in-out), 
                        stroke var(--duration-150) var(--ease-in-out); }
.transition-colors { transition: color var(--duration-150) var(--ease-in-out), 
                               background-color var(--duration-150) var(--ease-in-out), 
                               border-color var(--duration-150) var(--ease-in-out); }
.transition-opacity { transition: opacity var(--duration-150) var(--ease-in-out); }
.transition-shadow { transition: box-shadow var(--duration-150) var(--ease-in-out); }
.transition-transform { transition: transform var(--duration-150) var(--ease-in-out); }

/* 动画持续时间 */
.duration-75 { transition-duration: var(--duration-75); }
.duration-100 { transition-duration: var(--duration-100); }
.duration-150 { transition-duration: var(--duration-150); }
.duration-200 { transition-duration: var(--duration-200); }
.duration-300 { transition-duration: var(--duration-300); }
.duration-500 { transition-duration: var(--duration-500); }
.duration-700 { transition-duration: var(--duration-700); }
.duration-1000 { transition-duration: var(--duration-1000); }

/* 动画缓动函数 */
.ease-linear { transition-timing-function: var(--ease-linear); }
.ease-in { transition-timing-function: var(--ease-in); }
.ease-out { transition-timing-function: var(--ease-out); }
.ease-in-out { transition-timing-function: var(--ease-in-out); }
