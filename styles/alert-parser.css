/* 告警解析器专用样式 - 使用设计令牌系统 */

.alert-parser-modal .modal-content {
  max-width: 50rem; /* 800px equivalent */
  width: 90vw;
  max-height: 90vh;
  overflow-y: auto;
}

.alert-parser-modal .input-section {
  margin-bottom: var(--spacing-4);
}

.alert-parser-modal #alert-input {
  width: 100%;
  min-height: calc(var(--spacing-24) * 1.25); /* ~120px equivalent */
  padding: var(--spacing-3);
  border: var(--input-border-width) solid var(--border);
  border-radius: var(--input-radius);
  font-family: var(--font-family-mono);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-normal);
  resize: vertical;
  background-color: var(--background);
  color: var(--text-primary);
  transition: border-color var(--duration-150) var(--ease-in-out),
              box-shadow var(--duration-150) var(--ease-in-out);
}

.alert-parser-modal #alert-input:focus {
  outline: none;
  border-color: var(--border-focus);
  box-shadow: 0 0 0 2px color-mix(in srgb, var(--primary) 25%, transparent);
}

.alert-parser-modal #alert-input::placeholder {
  color: var(--text-tertiary);
}

.alert-parser-modal .button-group {
  display: flex;
  gap: var(--spacing-2);
  margin-top: var(--spacing-2);
  flex-wrap: nowrap;
}

.alert-parser-modal .button-group .btn {
  flex: 1;
}

.alert-parser-modal .btn {
  padding: var(--button-padding-y) var(--button-padding-x);
  border: none;
  border-radius: var(--button-radius);
  cursor: pointer;
  font-size: var(--font-size-sm);
  font-weight: var(--button-font-weight);
  transition: background-color var(--duration-150) var(--ease-in-out),
              transform var(--duration-75) var(--ease-in-out);
}

.alert-parser-modal .btn:hover {
  transform: translateY(calc(-1 * var(--spacing-1) / 4)); /* -1px equivalent */
}

.alert-parser-modal .btn-primary {
  background-color: var(--primary);
  color: var(--color-gray-50);
}

.alert-parser-modal .btn-primary:hover {
  background-color: var(--primary-hover);
}

.alert-parser-modal .btn-secondary {
  background-color: var(--text-secondary);
  color: var(--color-gray-50);
}

.alert-parser-modal .btn-secondary:hover {
  background-color: var(--text-primary);
}

.alert-parser-modal .btn-success {
  background-color: var(--success);
  color: var(--color-gray-50);
}

.alert-parser-modal .btn-success:hover {
  background-color: var(--color-success-700);
}

.alert-parser-modal .btn:disabled {
  opacity: 0.6; /* 保持标准的禁用透明度 */
  cursor: not-allowed;
  transform: none;
}

.alert-parser-modal .result-section {
  margin-top: var(--spacing-4);
  padding: var(--spacing-4);
  border: var(--input-border-width) solid var(--border);
  border-radius: var(--radius-lg);
  background-color: var(--surface);
}

.alert-parser-modal .result-section h4 {
  margin: var(--spacing-0) var(--spacing-0) var(--spacing-4) var(--spacing-0);
  color: var(--text-primary);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
}

.alert-parser-modal .alert-result {
  background: var(--background);
  border-radius: var(--radius-md);
  padding: var(--spacing-4);
  border: var(--input-border-width) solid var(--border);
}

.alert-parser-modal .summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(12.5rem, 1fr)); /* 200px equivalent */
  gap: var(--spacing-3);
  margin-bottom: var(--spacing-4);
}

.alert-parser-modal .summary-item {
  padding: var(--spacing-3);
  border: var(--input-border-width) solid var(--border);
  border-radius: var(--radius-md);
  background-color: var(--surface);
}

.alert-parser-modal .summary-item.alert-name {
  grid-column: 1 / -1;
  background-color: var(--color-info-50);
  border-color: var(--info);
}

[data-theme="dark"] .alert-parser-modal .summary-item.alert-name {
  background-color: var(--color-info-900);
  border-color: var(--info);
}

.alert-parser-modal .summary-item.current-value {
  background-color: var(--color-warning-50);
  border-color: var(--warning);
}

[data-theme="dark"] .alert-parser-modal .summary-item.current-value {
  background-color: var(--color-warning-900);
  border-color: var(--warning);
}

.alert-parser-modal .summary-label {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-1);
  font-weight: var(--font-weight-medium);
}

.alert-parser-modal .summary-value {
  font-size: var(--font-size-sm);
  color: var(--text-primary);
  font-weight: var(--font-weight-semibold);
  word-break: break-all;
}

.alert-parser-modal .url-section {
  margin-top: var(--spacing-4);
  padding: var(--spacing-4);
  border: var(--input-border-width) solid var(--success);
  border-radius: var(--radius-lg);
  background-color: var(--color-success-50);
}

[data-theme="dark"] .alert-parser-modal .url-section {
  background-color: var(--color-success-900);
  border-color: var(--success);
}

.alert-parser-modal .url-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--color-success-700);
  margin-bottom: var(--spacing-3);
}

[data-theme="dark"] .alert-parser-modal .url-label {
  color: var(--color-success-300);
}

.alert-parser-modal .url-container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

.alert-parser-modal .url-text {
  padding: var(--spacing-2);
  background-color: var(--background);
  border: var(--input-border-width) solid var(--color-success-200);
  border-radius: var(--radius-md);
  font-family: var(--font-family-mono);
  font-size: var(--font-size-xs);
  word-break: break-all;
  color: var(--text-primary);
}

.alert-parser-modal .url-text-truncated {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: normal;
  cursor: help;
  transition: all var(--duration-150) var(--ease-in-out);
}

.alert-parser-modal .url-text-truncated:hover {
  white-space: normal;
  word-break: break-all;
  overflow: visible;
  background-color: var(--surface);
  box-shadow: 0 2px 8px color-mix(in srgb, var(--shadow) 15%, transparent);
  z-index: 10;
  position: relative;
}

[data-theme="dark"] .alert-parser-modal .url-text {
  border-color: var(--color-success-700);
}

.alert-parser-modal .action-buttons {
  display: flex;
  gap: var(--spacing-2);
  flex-wrap: nowrap;
}

.alert-parser-modal .action-buttons .copy-btn,
.alert-parser-modal .action-buttons .open-btn {
  flex: 1;
}

.alert-parser-modal .copy-btn,
.alert-parser-modal .open-btn {
  padding: var(--spacing-1) var(--spacing-3);
  border: none;
  border-radius: var(--radius-md);
  cursor: pointer;
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  transition: background-color var(--duration-150) var(--ease-in-out),
              transform var(--duration-75) var(--ease-in-out);
}

.alert-parser-modal .copy-btn:hover,
.alert-parser-modal .open-btn:hover {
  transform: translateY(calc(-1 * var(--spacing-1) / 4)); /* -1px equivalent */
}

.alert-parser-modal .copy-btn {
  background-color: var(--info);
  color: var(--color-gray-50);
}

.alert-parser-modal .copy-btn:hover {
  background-color: var(--color-info-700);
}

.alert-parser-modal .open-btn {
  background-color: var(--success);
  color: var(--color-gray-50);
}

.alert-parser-modal .open-btn:hover {
  background-color: var(--color-success-700);
}

.alert-parser-modal .result-error {
  padding: var(--spacing-4);
  background-color: var(--color-error-50);
  border: var(--input-border-width) solid var(--color-error-200);
  border-radius: var(--radius-lg);
  color: var(--color-error-700);
}

[data-theme="dark"] .alert-parser-modal .result-error {
  background-color: var(--color-error-900);
  border-color: var(--color-error-700);
  color: var(--color-error-300);
}

.alert-parser-modal .result-error p {
  margin: var(--spacing-0);
  font-size: var(--font-size-sm);
}

.alert-parser-modal .advanced-options {
  border-top: var(--input-border-width) solid var(--border);
  padding-top: var(--spacing-4);
}

.alert-parser-modal .advanced-options summary {
  color: var(--text-primary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--spacing-1);
  transition: color var(--duration-150) var(--ease-in-out);
}

.alert-parser-modal .advanced-options summary:hover {
  color: var(--primary);
}

.alert-parser-modal .advanced-options details[open] summary {
  margin-bottom: var(--spacing-3);
}

.alert-parser-modal .advanced-options .form-control {
  background-color: var(--background);
  border: var(--input-border-width) solid var(--border);
  color: var(--text-primary);
  border-radius: var(--radius-md);
  transition: border-color var(--duration-150) var(--ease-in-out);
}

.alert-parser-modal .advanced-options .form-control:focus {
  outline: none;
  border-color: var(--border-focus);
  box-shadow: 0 0 0 2px color-mix(in srgb, var(--primary) 25%, transparent);
}

.alert-parser-modal .advanced-options input[type="text"]::placeholder {
  color: var(--text-tertiary);
}

.alert-parser-modal .collecter-info {
  margin-top: var(--spacing-4);
  padding: var(--spacing-3);
  background-color: var(--surface);
  border: var(--input-border-width) solid var(--border);
  border-radius: var(--radius-md);
  font-family: var(--font-family-mono);
}

.alert-parser-modal .collecter-info-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-2);
}

.alert-parser-modal .collecter-info-content {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
}

.alert-parser-modal .collecter-info-content div {
  margin-bottom: var(--spacing-1);
  line-height: var(--line-height-normal);
}

.alert-parser-modal .collecter-info-error {
  color: var(--error) !important;
  font-weight: var(--font-weight-medium);
}

/* 响应式设计 */
@media (max-width: 37.5rem) { /* 600px equivalent */
  .alert-parser-modal .summary-grid {
    grid-template-columns: 1fr;
  }

  .alert-parser-modal .button-group {
    flex-wrap: wrap;
  }

  .alert-parser-modal .action-buttons {
    flex-wrap: wrap;
  }
}
