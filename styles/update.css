/**
 * 更新功能专用样式 - 遵循设计令牌系统
 */

/* 更新横幅 */
.update-banner {
  background: linear-gradient(135deg, var(--color-blue-50), var(--color-indigo-50));
  border: 1px solid var(--color-blue-200);
  border-radius: var(--radius-lg);
  margin-bottom: var(--spacing-4);
  animation: slideDown 0.3s ease;
  box-shadow: var(--shadow-sm);
  width: 100%;
}

.update-banner-content {
  display: flex;
  align-items: center;
  padding: var(--spacing-2) var(--spacing-4);
  gap: var(--spacing-2);
}

.update-banner-text {
  flex: 1;
  cursor: pointer;
  transition: opacity var(--duration-150) var(--ease-in-out);
}

.update-banner-text:hover {
  opacity: 0.8;
}

.update-banner-title {
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  font-size: var(--font-size-sm);
}

.update-banner-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

/* 更新徽章 */
.update-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
  color: white;
  font-size: 8px;
  font-weight: var(--font-weight-semibold);
  padding: 2px 4px;
  border-radius: 6px;
  border: 1px solid white;
  z-index: 10;
  animation: updateBadgePulse 2s ease-in-out infinite;
  box-shadow: 0 1px 4px rgba(238, 90, 82, 0.3);
  text-transform: uppercase;
  letter-spacing: 0.3px;
}

/* 更新徽章动画 */
@keyframes updateBadgePulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 1px 4px rgba(238, 90, 82, 0.3);
  }
  50% {
    transform: scale(1.03);
    box-shadow: 0 2px 6px rgba(238, 90, 82, 0.4);
  }
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
  .update-badge {
    background: linear-gradient(135deg, #ff5252, #d32f2f);
    border-color: var(--surface);
    box-shadow: 0 1px 4px rgba(255, 82, 82, 0.4);
  }
}

/* 更新详情模态框 - 遵循统一Modal规范 */
/* 模态框尺寸在components.css中定义，这里只需要特定的更新相关样式 */

.update-detail-container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
  max-height: 60vh;
  overflow-y: auto;
  padding: var(--spacing-1);
}

.update-item {
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  padding: var(--spacing-4);
  background: var(--surface);
  transition: all var(--duration-200) var(--ease-in-out);
  margin-bottom: var(--spacing-3);
}

.update-item:last-child {
  margin-bottom: 0;
}

.update-item:hover {
  border-color: var(--color-blue-300);
  box-shadow: var(--shadow-sm);
}

.update-item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-3);
}

.update-item-title {
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.update-item-meta {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  flex-wrap: wrap;
}

.update-priority {
  font-size: var(--font-size-xs);
  padding: 2px 8px;
  border-radius: var(--radius-base);
  font-weight: var(--font-weight-medium);
}

.priority-low {
  background: var(--color-gray-100);
  color: var(--color-gray-700);
}

.priority-medium {
  background: var(--color-blue-100);
  color: var(--color-blue-700);
}

.priority-high {
  background: var(--color-orange-100);
  color: var(--color-orange-700);
}

.priority-critical {
  background: var(--color-red-100);
  color: var(--color-red-700);
  animation: pulse 2s infinite;
}

.security-badge {
  background: var(--color-red-500);
  color: white;
  font-size: var(--font-size-xs);
  padding: 2px 6px;
  border-radius: var(--radius-base);
  font-weight: var(--font-weight-medium);
}

.download-size {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  background: var(--color-gray-100);
  padding: 2px 6px;
  border-radius: var(--radius-base);
}

.update-item-version {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  margin-bottom: var(--spacing-3);
  font-size: var(--font-size-sm);
}

.version-current {
  color: var(--text-secondary);
  font-family: var(--font-family-mono);
}

.version-arrow {
  color: var(--color-blue-500);
  font-weight: var(--font-weight-bold);
}

.version-latest {
  color: var(--color-green-600);
  font-weight: var(--font-weight-medium);
  font-family: var(--font-family-mono);
}

.update-type {
  background: var(--color-blue-100);
  color: var(--color-blue-700);
  font-size: var(--font-size-xs);
  padding: 2px 6px;
  border-radius: var(--radius-base);
  font-weight: var(--font-weight-medium);
}

.update-item-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
  padding-bottom: 0; /* 移除底部额外间距，因为没有按钮了 */
}

.update-changelog {
  color: var(--text-secondary);
  line-height: var(--line-height-relaxed);
}

.update-changelog p {
  margin: 0 0 var(--spacing-2) 0;
}

.update-changelog p:last-child {
  margin-bottom: 0;
}

.update-date {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  font-size: var(--font-size-sm);
}

.date-label {
  color: var(--text-secondary);
}

.date-value {
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
}

/* 移除了单个更新项的操作按钮，简化布局 */

/* 全量更新按钮样式 */
.full-update-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  font-weight: var(--font-weight-medium);
}

.full-update-btn .btn-icon {
  font-size: var(--font-size-base);
}

/* 工具详情模态框 */
.tool-update-details {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.tool-update-details h4 {
  margin: 0 0 var(--spacing-4) 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  border-bottom: 1px solid var(--border);
  padding-bottom: var(--spacing-2);
}

.detail-section {
  border-bottom: 1px solid var(--border);
  padding-bottom: var(--spacing-3);
}

.detail-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.detail-section h5 {
  margin: 0 0 var(--spacing-2) 0;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.detail-section p {
  margin: 0 0 var(--spacing-1) 0;
  color: var(--text-secondary);
  line-height: var(--line-height-relaxed);
}

.changelog-detail {
  background: var(--color-gray-50);
  border: 1px solid var(--border);
  border-radius: var(--radius-base);
  padding: var(--spacing-3);
  max-height: 200px;
  overflow-y: auto;
  font-size: var(--font-size-sm);
  line-height: var(--line-height-relaxed);
}

/* 动画效果 */
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}



@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .update-banner-content {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-2);
  }

  .update-banner-actions {
    justify-content: center;
  }

  .update-item-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-2);
  }

  .update-item-meta {
    justify-content: flex-start;
  }

  .update-item-version {
    flex-wrap: wrap;
  }

  /* 响应式模态框尺寸调整 */
  .modal-content {
    width: 95vw;
    margin: var(--spacing-2);
  }
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
  .update-banner {
    background: linear-gradient(135deg, var(--color-blue-900), var(--color-indigo-900));
    border-color: var(--color-blue-700);
  }

  .update-banner-icon {
    color: var(--color-blue-400);
  }

  .priority-low {
    background: var(--color-gray-800);
    color: var(--color-gray-300);
  }

  .priority-medium {
    background: var(--color-blue-800);
    color: var(--color-blue-300);
  }

  .priority-high {
    background: var(--color-orange-800);
    color: var(--color-orange-300);
  }

  .priority-critical {
    background: var(--color-red-800);
    color: var(--color-red-300);
  }

  .download-size {
    background: var(--color-gray-800);
    color: var(--color-gray-300);
  }

  .update-type {
    background: var(--color-blue-800);
    color: var(--color-blue-300);
  }

  .changelog-detail {
    background: var(--color-gray-800);
    border-color: var(--color-gray-700);
  }
}
