/**
 * XUID切换助手样式文件
 * 适配fwyy-tools设计系统，支持深色主题
 * 排除小工具、任务列表、方舟课程相关样式
 */

/* XUID工具样式 - 使用fwyy-tools设计令牌 */
:root {
  /* 基础颜色映射到设计令牌 */
  --xuid-bg-primary: var(--background);
  --xuid-bg-secondary: var(--surface);
  --xuid-bg-tertiary: var(--surface-hover);
  --xuid-text-primary: var(--text-primary);
  --xuid-text-secondary: var(--text-secondary);
  --xuid-text-muted: var(--text-tertiary);
  --xuid-border-color: var(--border);
  --xuid-border-accent: var(--primary);
  --xuid-shadow: var(--shadow-sm);
  --xuid-shadow-hover: var(--shadow-md);

  /* 功能色彩映射 */
  --xuid-primary: var(--primary);
  --xuid-success: var(--success);
  --xuid-warning: var(--warning);
  --xuid-danger: var(--error);
  --xuid-info: var(--info);

  /* 集群颜色 */
  --xuid-cluster-tips: var(--warning);
  --xuid-cluster-small: var(--success);
  --xuid-cluster-stable: var(--info);
  --xuid-cluster-online: var(--text-secondary);

  /* 资产类型颜色 */
  --xuid-asset-supervisor: var(--color-purple-600);
  --xuid-asset-tutor: var(--color-blue-600);
  --xuid-asset-both: var(--color-orange-600);
}

/* 深色主题自动继承设计令牌，无需额外定义 */

/* XUID工具模态框基础样式 */
.xuid-modal {
  --modal-width: 28rem; /* 450px -> 28rem */
  --modal-max-height: 37.5rem; /* 600px -> 37.5rem */
}

.xuid-modal .modal-content {
  width: var(--modal-width);
  max-height: var(--modal-max-height);
  background: var(--xuid-bg-primary);
  color: var(--xuid-text-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--xuid-shadow-hover);
}

.xuid-modal .modal-header {
  padding: var(--modal-header-padding);
  border-bottom: var(--input-border-width) solid var(--xuid-border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: var(--modal-header-min-height);
}

.xuid-modal .modal-title {
  font-size: var(--modal-title-font-size);
  font-weight: var(--modal-title-font-weight);
  color: var(--xuid-text-primary);
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.xuid-modal .modal-body {
  padding: var(--modal-body-padding);
  max-height: calc(var(--modal-max-height) - 6rem); /* 减小头部空间占用 */
  overflow-y: auto;
}

.xuid-modal .modal-footer {
  padding: var(--modal-footer-padding);
  border-top: var(--input-border-width) solid var(--xuid-border-color);
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: var(--spacing-3);
  min-height: var(--modal-footer-min-height);
}

/* 状态信息区域 */
.xuid-status-info-section {
  margin-bottom: var(--spacing-4);
}

.xuid-status-info-grid {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-5);
  background: var(--xuid-bg-primary);
  border-left: var(--spacing-1) solid var(--xuid-border-accent);
  padding: var(--spacing-4) var(--spacing-5);
  border-radius: 0 var(--radius-md) var(--radius-md) 0;
  box-shadow: var(--xuid-shadow);
}

.xuid-status-info-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-6);
  align-items: start;
}

.xuid-status-info-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
  min-width: 0;
}

.xuid-status-info-item.full-width {
  grid-column: 1 / -1;
}

.xuid-info-label {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  color: var(--xuid-text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.03125rem; /* 0.5px -> 0.03125rem */
  margin-bottom: var(--spacing-2);
}

.xuid-info-value {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--xuid-text-primary);
  word-break: break-all;
  cursor: pointer;
  transition: color var(--duration-200);
}

.xuid-info-value:hover {
  color: var(--xuid-primary);
}

/* 资产类型颜色 */
.xuid-info-value[data-asset-type="督学"],
.xuid-info-value.asset-supervisor {
  color: var(--xuid-asset-supervisor) !important;
  font-weight: var(--font-weight-semibold);
}

.xuid-info-value[data-asset-type="辅导"],
.xuid-info-value.asset-tutor {
  color: var(--xuid-asset-tutor) !important;
  font-weight: var(--font-weight-semibold);
}

.xuid-info-value[data-asset-type*="督"][data-asset-type*="辅"],
.xuid-info-value.asset-both {
  color: var(--xuid-asset-both) !important;
  font-weight: var(--font-weight-semibold);
}

/* 集群状态样式 */
.xuid-cluster-status {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  text-transform: uppercase;
  letter-spacing: 0.03125rem; /* 0.5px -> 0.03125rem */
}

.xuid-cluster-status.tips {
  color: var(--xuid-cluster-tips) !important;
}

.xuid-cluster-status.small {
  color: var(--xuid-cluster-small) !important;
}

.xuid-cluster-status.stable {
  color: var(--xuid-cluster-stable) !important;
}

.xuid-cluster-status.online {
  color: var(--xuid-cluster-online) !important;
}

.xuid-cluster-status.unknown {
  color: var(--text-tertiary) !important;
}

/* 深色主题下的集群颜色会自动通过CSS变量适应，无需额外定义 */

/* 快速操作区域 */
.xuid-quick-actions-section {
  background: var(--xuid-bg-secondary);
  border: var(--input-border-width) solid var(--xuid-border-color);
  border-radius: var(--radius-md);
  padding: var(--spacing-5);
  margin-bottom: var(--spacing-4);
}

.xuid-quick-actions-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  margin-bottom: var(--spacing-5);
  padding-bottom: var(--spacing-3);
  border-bottom: var(--input-border-width) solid var(--xuid-border-color);
}

.xuid-quick-actions-header h3 {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--xuid-text-primary);
  margin: 0;
}

.xuid-actions-content {
  transition: all var(--duration-300) var(--ease-out);
  overflow: visible; /* 改为 visible 以允许下拉框显示 */
}

.xuid-actions-content.collapsed {
  max-height: 0;
  padding: 0;
  margin: 0;
  overflow: hidden; /* 折叠时保持 hidden */
}

.xuid-manual-switch-row {
  display: flex;
  gap: var(--spacing-3);
  margin-bottom: var(--spacing-4);
  align-items: center;
}

.xuid-manual-input {
  flex: 1;
  height: calc(var(--spacing-8));
  padding: 0 var(--spacing-3);
  border: var(--input-border-width) solid var(--xuid-border-color);
  border-radius: var(--radius-base);
  font-size: var(--font-size-sm);
  background: var(--xuid-bg-primary);
  color: var(--xuid-text-primary);
  transition: border-color var(--duration-200);
}

.xuid-manual-input:focus {
  outline: none;
  border-color: var(--xuid-primary);
}

.xuid-manual-switch-btn {
  width: calc(var(--spacing-20));
  height: calc(var(--spacing-8));
  background: var(--xuid-primary);
  color: var(--color-gray-50);
  border: none;
  border-radius: var(--radius-base);
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: background-color var(--duration-200);
}

.xuid-manual-switch-btn:hover {
  background: var(--primary-hover);
}

.xuid-manual-switch-btn:disabled {
  background: var(--xuid-text-muted);
  cursor: not-allowed;
}

.xuid-action-buttons-row {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: var(--spacing-3);
  align-items: center;
}

/* XUID管理区域内的按钮行需要额外的上边距 */
.xuid-management-section .xuid-action-buttons-row {
  grid-template-columns: 1fr 1fr;
  margin-top: var(--spacing-4);
}

.xuid-action-btn {
  height: calc(var(--spacing-8));
  padding: 0 var(--spacing-3);
  border: none;
  border-radius: var(--radius-base);
  font-size: var(--font-size-xs);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-1);
  transition: background-color var(--duration-200);
}

.xuid-action-btn.primary {
  background: var(--xuid-primary);
  color: var(--color-gray-50);
}

.xuid-action-btn.primary:hover {
  background: var(--primary-hover);
}

.xuid-action-btn.warning {
  background: var(--xuid-warning);
  color: var(--color-gray-50);
}

.xuid-action-btn.warning:hover {
  background: var(--color-warning-700);
}

.xuid-action-btn.cluster {
  background: var(--xuid-success);
  color: var(--color-gray-50);
  position: relative;
  /* 确保按钮占满容器宽度，与其他按钮大小一致 */
  width: 100%;
}

.xuid-action-btn.cluster:hover {
  background: var(--color-success-700);
}

.xuid-action-btn.cluster::after {
  content: "▼";
  font-size: var(--font-size-xs);
  opacity: 0.8;
  transition: transform var(--duration-200);
  margin-left: var(--spacing-1);
}

.xuid-action-btn.cluster.active::after {
  transform: rotate(180deg);
}

.xuid-action-btn:disabled {
  background: var(--xuid-text-muted);
  color: var(--color-gray-50);
  cursor: not-allowed;
}

/* 集群下拉菜单 */
.xuid-cluster-dropdown {
  position: relative;
  /* 确保容器不影响Grid布局中的按钮大小 */
  display: flex;
  width: 100%;
}

.xuid-cluster-dropdown-menu {
  position: absolute;
  bottom: 100%;
  left: 0;
  right: 0;
  background: var(--xuid-bg-primary);
  border: var(--input-border-width) solid var(--xuid-border-color);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-lg);
  z-index: var(--z-index-dropdown);
  display: none;
  margin-bottom: var(--spacing-1);
  min-width: 120px;
  max-height: 200px;
  overflow-y: auto;
}

.xuid-cluster-option {
  padding: var(--spacing-3) var(--spacing-4);
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  font-size: var(--font-size-sm);
  color: var(--xuid-text-primary);
  transition: all var(--duration-200);
  border-bottom: 1px solid transparent;
  white-space: nowrap;
}

.xuid-cluster-option:hover {
  background: var(--xuid-bg-secondary);
  border-bottom-color: var(--xuid-border-color);
}

.xuid-cluster-option.active {
  background: var(--xuid-primary);
  color: var(--color-gray-50);
  font-weight: var(--font-weight-semibold);
}

.xuid-cluster-option:first-child {
  border-top-left-radius: var(--radius-md);
  border-top-right-radius: var(--radius-md);
}

.xuid-cluster-option:last-child {
  border-bottom-left-radius: var(--radius-md);
  border-bottom-right-radius: var(--radius-md);
  border-bottom: none;
}

.xuid-cluster-color-dot {
  width: var(--spacing-2);
  height: var(--spacing-2);
  border-radius: var(--radius-full);
}

.xuid-cluster-color-dot.tips {
  background: var(--xuid-cluster-tips);
}

.xuid-cluster-color-dot.small {
  background: var(--xuid-cluster-small);
}

.xuid-cluster-color-dot.stable {
  background: var(--xuid-cluster-stable);
}

.xuid-cluster-color-dot.online {
  background: var(--xuid-cluster-online);
}

/* XUID管理区域 */
.xuid-management-section {
  background: var(--xuid-bg-primary);
  border-left: var(--spacing-1) solid var(--xuid-asset-supervisor);
  padding: var(--spacing-5);
  margin-bottom: var(--spacing-4);
  border-radius: 0 var(--radius-md) var(--radius-md) 0;
  box-shadow: var(--xuid-shadow);
}

.xuid-management-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-4);
  padding-bottom: var(--spacing-3);
  border-bottom: calc(var(--input-border-width) * 2) solid var(--xuid-border-color);
}

.xuid-management-header h3 {
  margin: 0;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--xuid-asset-supervisor);
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.xuid-batch-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.xuid-checkbox-label {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  font-size: var(--font-size-xs);
  color: var(--xuid-text-secondary);
  cursor: pointer;
  padding: var(--spacing-1);
}

.xuid-checkbox-label input[type="checkbox"] {
  margin: 0;
}

/* XUID列表标题 */
.xuid-list-header {
  display: flex;
  align-items: center;
  padding: var(--spacing-3) var(--spacing-4);
  background: var(--xuid-bg-secondary);
  border: var(--input-border-width) solid var(--xuid-border-color);
  border-bottom: none;
  border-radius: var(--radius-md) var(--radius-md) 0 0;
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  color: var(--xuid-text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.03125rem;
  margin-bottom: 0;
  margin-top: var(--spacing-4);
}

.xuid-header-checkbox {
  width: calc(var(--spacing-8) + var(--spacing-2));
  text-align: center;
}

.xuid-header-realname {
  flex: 1;
  min-width: var(--spacing-20);
}

.xuid-header-asset {
  width: calc(var(--spacing-10) + var(--spacing-2));
  text-align: center;
}

.xuid-header-xuid {
  width: calc(var(--spacing-20) + var(--spacing-5));
  text-align: center;
}

/* XUID列表容器 */
.xuid-list-container {
  max-height: calc(var(--spacing-24) * 3.125);
  overflow-y: auto;
  margin-bottom: var(--spacing-6);
  border: var(--input-border-width) solid var(--xuid-border-color);
  border-top: none;
  border-radius: 0 0 var(--radius-md) var(--radius-md);
  background: var(--xuid-bg-tertiary);
}

.xuid-empty-state {
  text-align: center;
  padding: var(--spacing-10) var(--spacing-5);
  color: var(--xuid-text-muted);
  background: var(--xuid-bg-primary);
}

.xuid-empty-state .empty-icon {
  font-size: calc(var(--font-size-4xl) * 1.33);
  margin-bottom: var(--spacing-4);
  opacity: 0.5;
}

.xuid-empty-state .empty-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-2);
  color: var(--xuid-text-secondary);
}

.xuid-empty-state .empty-text {
  font-size: var(--font-size-sm);
  color: var(--xuid-text-muted);
}

/* 域名分组 */
.xuid-domain-group {
  border-bottom: var(--input-border-width) solid var(--xuid-border-color);
}

.xuid-domain-group:last-child {
  border-bottom: none;
}

.xuid-domain-header {
  background: linear-gradient(135deg, var(--xuid-bg-secondary) 0%, var(--xuid-bg-tertiary) 100%);
  padding: var(--spacing-3) var(--spacing-4);
  font-weight: var(--font-weight-semibold);
  color: var(--xuid-text-primary);
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: var(--input-border-width) solid var(--xuid-border-color);
  transition: background-color var(--duration-200);
}

.xuid-domain-header:hover {
  background: var(--xuid-bg-tertiary);
}

.xuid-domain-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  flex: 1;
}

.xuid-domain-toggle {
  font-size: var(--font-size-xs);
  color: var(--xuid-text-secondary);
  transition: transform var(--duration-200);
  user-select: none;
}

.xuid-domain-toggle.expanded {
  transform: rotate(90deg);
}

.xuid-domain-name {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--xuid-text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: calc(var(--spacing-24) * 2.08);
}

.xuid-domain-count {
  font-size: var(--font-size-xs);
  color: var(--xuid-text-muted);
  background: var(--xuid-bg-primary);
  padding: calc(var(--spacing-1) / 2) var(--spacing-2);
  border-radius: var(--radius-lg);
  margin-left: auto;
}

.xuid-list {
  background: var(--xuid-bg-primary);
  display: none; /* 默认折叠 */
}

.xuid-list.expanded {
  display: block;
}

.xuid-item {
  padding: var(--spacing-3) var(--spacing-4);
  border-bottom: var(--input-border-width) solid var(--xuid-bg-secondary);
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: background-color var(--duration-200);
}

.xuid-item:hover {
  background: var(--xuid-bg-secondary);
}

.xuid-item:last-child {
  border-bottom: none;
}

.xuid-item.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.xuid-item.disabled:hover {
  background: transparent;
}

.xuid-checkbox {
  width: calc(var(--spacing-8) + var(--spacing-2));
  display: flex;
  align-items: center;
  justify-content: center;
}

.xuid-checkbox input[type="checkbox"] {
  margin: 0;
}

.xuid-checkbox input[type="checkbox"]:disabled {
  opacity: 0.5;
}

.xuid-realname {
  flex: 1;
  min-width: var(--spacing-20);
  font-size: var(--font-size-xs);
  color: var(--xuid-text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding-right: var(--spacing-3);
}

.xuid-asset {
  width: calc(var(--spacing-10) + var(--spacing-2));
  text-align: center;
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  color: var(--xuid-text-secondary);
}

.xuid-asset.asset-supervisor {
  color: var(--xuid-asset-supervisor);
}

.xuid-asset.asset-tutor {
  color: var(--xuid-asset-tutor);
}

.xuid-asset.asset-both {
  color: var(--xuid-asset-both);
}

.xuid-value {
  width: calc(var(--spacing-20) + var(--spacing-5));
  text-align: center;
  font-size: var(--font-size-xs);
  font-family: var(--font-family-mono);
  font-weight: 600;
  color: var(--color-primary-600);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 2px 4px;
  border-radius: 3px;
  user-select: none; /* 防止文本选择干扰点击 */
}

.xuid-value:hover {
  background: var(--color-primary-50);
  color: var(--color-primary-700);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 滚动条样式 */
.xuid-list-container::-webkit-scrollbar {
  width: var(--spacing-2);
}

.xuid-list-container::-webkit-scrollbar-track {
  background: var(--xuid-bg-tertiary);
}

.xuid-list-container::-webkit-scrollbar-thumb {
  background: var(--xuid-text-muted);
  border-radius: var(--radius-sm);
}

.xuid-list-container::-webkit-scrollbar-thumb:hover {
  background: var(--xuid-text-secondary);
}

/* 域名不支持提示 */
.xuid-domain-unsupported-message {
  background: var(--color-error-50);
  border-radius: var(--radius-lg);
  padding: var(--spacing-5);
  margin: var(--spacing-5) 0;
  border: var(--input-border-width) solid var(--error);
  box-shadow: var(--shadow-base);
  text-align: center;
}

[data-theme="dark"] .xuid-domain-unsupported-message {
  background: var(--color-error-900);
  border-color: var(--color-error-400);
}

.xuid-unsupported-icon {
  font-size: calc(var(--font-size-4xl) * 1.33);
  margin-bottom: var(--spacing-4);
  opacity: 0.7;
}

.xuid-unsupported-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--error);
  margin-bottom: var(--spacing-2);
}

.xuid-unsupported-text {
  font-size: var(--font-size-sm);
  color: var(--xuid-text-primary);
  margin-bottom: var(--spacing-2);
}

.xuid-unsupported-hint {
  font-size: var(--font-size-xs);
  color: var(--xuid-text-secondary);
}

/* 响应式设计 */
@media (max-width: 25rem) {
  .xuid-modal {
    --modal-width: 95vw;
  }

  .xuid-status-info-row {
    grid-template-columns: 1fr;
    gap: var(--spacing-4);
  }

  .xuid-action-buttons-row {
    grid-template-columns: 1fr;
    gap: var(--spacing-3);
  }

  .xuid-manual-switch-row {
    flex-direction: column;
    gap: var(--spacing-3);
  }

  .xuid-manual-switch-btn {
    width: 100%;
  }

  .xuid-domain-name {
    max-width: calc(var(--spacing-24) * 1.5625);
  }
}

@media (max-width: 21.875rem) {
  .xuid-action-btn {
    font-size: var(--font-size-xs);
    padding: var(--spacing-1) var(--spacing-2);
  }

  .xuid-header-realname,
  .xuid-realname {
    min-width: calc(var(--spacing-12) + var(--spacing-3));
  }

  .xuid-header-xuid,
  .xuid-value {
    width: var(--spacing-20);
  }
}

/* 加载状态 */
.xuid-loading {
  display: inline-block;
  width: var(--spacing-4);
  height: var(--spacing-4);
  border: calc(var(--input-border-width) * 2) solid var(--xuid-border-color);
  border-radius: var(--radius-full);
  border-top-color: var(--xuid-primary);
  animation: xuid-spin var(--duration-1000) var(--ease-in-out) infinite;
}

@keyframes xuid-spin {
  to {
    transform: rotate(360deg);
  }
}

/* 成功/错误状态 */
.xuid-success {
  color: var(--xuid-success);
}

.xuid-error {
  color: var(--xuid-danger);
}

.xuid-warning {
  color: var(--xuid-warning);
}

.xuid-info {
  color: var(--xuid-info);
}

/* 工具提示 */
.xuid-tooltip {
  position: relative;
}

.xuid-tooltip::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: var(--xuid-text-primary);
  color: var(--xuid-bg-primary);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-base);
  font-size: var(--font-size-xs);
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity var(--duration-200);
  z-index: var(--z-index-tooltip);
}

.xuid-tooltip:hover::after {
  opacity: 1;
}

/* 动画效果 */
.xuid-fade-in {
  animation: xuid-fadeIn var(--duration-300) var(--ease-in-out);
}

@keyframes xuid-fadeIn {
  from {
    opacity: 0;
    transform: translateY(calc(-1 * var(--spacing-3)));
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.xuid-slide-down {
  animation: xuid-slideDown var(--duration-300) var(--ease-in-out);
}

@keyframes xuid-slideDown {
  from {
    max-height: 0;
    opacity: 0;
  }
  to {
    max-height: calc(var(--spacing-24) * 3.125);
    opacity: 1;
  }
}

/* 辅助类 */
.xuid-text-center {
  text-align: center;
}

.xuid-text-left {
  text-align: left;
}

.xuid-text-right {
  text-align: right;
}

.xuid-hidden {
  display: none !important;
}

.xuid-visible {
  display: block !important;
}

.xuid-flex {
  display: flex;
}

.xuid-flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.xuid-gap-8 {
  gap: var(--spacing-2);
}

.xuid-gap-12 {
  gap: var(--spacing-3);
}

.xuid-gap-16 {
  gap: var(--spacing-4);
}

.xuid-mb-8 {
  margin-bottom: var(--spacing-2);
}

.xuid-mb-12 {
  margin-bottom: var(--spacing-3);
}

.xuid-mb-16 {
  margin-bottom: var(--spacing-4);
}

.xuid-mt-8 {
  margin-top: var(--spacing-2);
}

.xuid-mt-12 {
  margin-top: var(--spacing-3);
}

.xuid-mt-16 {
  margin-top: var(--spacing-4);
}

/* 加载状态和错误状态样式 */
.xuid-loading-state,
.xuid-error-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-4);
  text-align: center;
  color: var(--xuid-text-secondary);
  font-size: var(--font-size-sm);
  min-height: 4rem;
}

.xuid-loading-state {
  background: var(--xuid-bg-secondary);
  border: 1px dashed var(--xuid-border-color);
  border-radius: var(--radius-md);
}

.xuid-loading-state::before {
  content: "⏳";
  margin-right: var(--spacing-2);
  animation: pulse 1.5s ease-in-out infinite;
}

.xuid-error-state {
  background: var(--surface-error);
  border: 1px solid var(--error);
  border-radius: var(--radius-md);
  color: var(--error);
}

.xuid-error-state::before {
  content: "⚠️";
  margin-right: var(--spacing-2);
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
