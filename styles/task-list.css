/**
 * 任务列表工具样式
 * 基于设计令牌系统的任务列表组件样式
 */

/* ========== 任务列表表单样式 ========== */
.task-list-form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
  padding: 0;
}

.task-list-form .form-group {
  margin-bottom: var(--spacing-4);
}

.task-list-form .form-label {
  display: block;
  margin-bottom: var(--spacing-2);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.task-list-form .form-control {
  display: block;
  width: 100%;
  padding: var(--input-padding-y) var(--input-padding-x);
  border: var(--input-border-width) solid var(--border);
  border-radius: var(--input-radius);
  background-color: var(--background);
  color: var(--text-primary);
  font-family: inherit;
  font-size: var(--font-size-sm);
  line-height: var(--line-height-normal);
  transition: border-color var(--duration-150) var(--ease-in-out),
              box-shadow var(--duration-150) var(--ease-in-out);
}

.task-list-form .form-control:focus {
  outline: none;
  border-color: var(--border-focus);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.task-list-form .form-control::placeholder {
  color: var(--text-tertiary);
}

/* 必填字段标识 */
.task-list-form .text-error {
  color: var(--error);
  font-weight: var(--font-weight-medium);
}

/* ========== 提示信息样式 ========== */
.form-tips {
  background-color: var(--color-blue-50);
  border: 1px solid var(--color-blue-200);
  border-radius: var(--radius-md);
  padding: var(--spacing-3);
  margin-top: var(--spacing-2);
}

[data-theme="dark"] .form-tips {
  background-color: var(--color-blue-900);
  border-color: var(--color-blue-700);
}

.tips-header {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-blue-700);
  margin-bottom: var(--spacing-2);
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
}

[data-theme="dark"] .tips-header {
  color: var(--color-blue-300);
}

.tips-list {
  margin: 0;
  padding-left: var(--spacing-4);
  font-size: var(--font-size-sm);
  color: var(--color-blue-600);
  line-height: var(--line-height-relaxed);
}

[data-theme="dark"] .tips-list {
  color: var(--color-blue-400);
}

.tips-list li {
  margin-bottom: var(--spacing-1);
}

.tips-list li:last-child {
  margin-bottom: 0;
}

/* ========== 模态框底部按钮样式 ========== */
.task-list-form .modal-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: var(--spacing-3);
  padding-top: var(--spacing-4);
  margin-top: var(--spacing-4);
  border-top: 1px solid var(--border);
}

.task-list-form .btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  padding: var(--button-padding-y) var(--button-padding-x);
  border: 1px solid transparent;
  border-radius: var(--button-radius);
  font-family: inherit;
  font-size: var(--font-size-sm);
  font-weight: var(--button-font-weight);
  line-height: var(--line-height-tight);
  text-decoration: none;
  cursor: pointer;
  transition: all var(--duration-150) var(--ease-in-out);
  user-select: none;
  white-space: nowrap;
  min-width: 80px;
}

.task-list-form .btn:focus {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

.task-list-form .btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* 主要按钮样式 */
.task-list-form .btn-primary {
  background-color: var(--primary);
  color: white;
  border-color: var(--primary);
}

.task-list-form .btn-primary:hover:not(:disabled) {
  background-color: var(--primary-hover);
  border-color: var(--primary-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.task-list-form .btn-primary:active:not(:disabled) {
  background-color: var(--primary-active);
  border-color: var(--primary-active);
  transform: translateY(0);
}

/* 次要按钮样式 */
.task-list-form .btn-secondary {
  background-color: var(--surface);
  color: var(--text-primary);
  border-color: var(--border);
}

.task-list-form .btn-secondary:hover:not(:disabled) {
  background-color: var(--surface-hover);
  border-color: var(--border-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

/* ========== 表单验证状态 ========== */
.task-list-form .form-control.is-valid {
  border-color: var(--success);
  box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
}

.task-list-form .form-control.is-invalid {
  border-color: var(--error);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.task-list-form .form-feedback {
  margin-top: var(--spacing-1);
  font-size: var(--font-size-xs);
  line-height: var(--line-height-tight);
}

.task-list-form .form-feedback.valid-feedback {
  color: var(--success);
}

.task-list-form .form-feedback.invalid-feedback {
  color: var(--error);
}

/* ========== 响应式设计 ========== */
@media (max-width: 480px) {
  .task-list-form .modal-footer {
    flex-direction: column-reverse;
    gap: var(--spacing-2);
  }
  
  .task-list-form .btn {
    width: 100%;
    justify-content: center;
  }
}

/* ========== 加载状态 ========== */
.task-list-form .btn.loading {
  position: relative;
  color: transparent;
}

.task-list-form .btn.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 16px;
  height: 16px;
  margin: -8px 0 0 -8px;
  border: 2px solid transparent;
  border-top-color: currentColor;
  border-radius: 50%;
  animation: spin var(--duration-1000) linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* ========== 动画效果 ========== */
.task-list-form {
  animation: fadeInUp var(--duration-300) var(--ease-out);
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ========== 深色主题适配 ========== */
[data-theme="dark"] .task-list-form .form-control {
  background-color: var(--surface);
  border-color: var(--border);
  color: var(--text-primary);
}

[data-theme="dark"] .task-list-form .form-control:focus {
  border-color: var(--border-focus);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
}

/* ========== 无障碍支持 ========== */
@media (prefers-reduced-motion: reduce) {
  .task-list-form,
  .task-list-form .btn,
  .task-list-form .form-control {
    animation: none !important;
    transition: none !important;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .task-list-form .form-control {
    border-width: 2px;
  }
  
  .task-list-form .btn {
    border-width: 2px;
  }
  
  .form-tips {
    border-width: 2px;
  }
}
