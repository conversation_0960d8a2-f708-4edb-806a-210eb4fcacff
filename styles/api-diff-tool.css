/**
 * API Diff Tool 专用样式
 * 基于项目设计令牌系统的完整页面布局和组件样式
 */

/* ========== 工具容器 ========== */
.api-diff-tool {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  height: 100vh;
  background-color: var(--background);
  color: var(--text-primary);
  font-family: var(--font-family-sans, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif);
  overflow: hidden;
  position: relative;
}

/* ========== Header Bar ========== */
.tool-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 48px;
  padding: 0 var(--spacing-5);
  background-color: var(--surface);
  border-bottom: 1px solid var(--border);
  box-shadow: var(--shadow-sm);
  z-index: var(--z-index-sticky);
}

.header-left {
  display: flex;
  align-items: center;
}

.tool-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.tool-icon {
  font-size: var(--font-size-xl);
}

.tool-name {
  color: var(--text-primary);
}

.header-right {
  display: flex;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: var(--spacing-2);
}

.header-btn {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-1);
  padding: var(--spacing-2) var(--spacing-3);
  background-color: transparent;
  border: 1px solid var(--border);
  border-radius: var(--radius-md);
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--duration-150) var(--ease-in-out);
}

.header-btn:hover {
  background-color: var(--surface-hover);
  border-color: var(--border-hover);
  color: var(--text-primary);
}

.header-btn:focus {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

/* ========== Request Builder Area ========== */
.request-builder-area {
  flex-shrink: 0;
  min-height: 300px;
  max-height: 50vh;
  background-color: var(--surface);
  border-bottom: 1px solid var(--border);
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.request-builder-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  gap: var(--spacing-3);
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid var(--border);
  border-top: 2px solid var(--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Request Builder 顶部行 */
.request-top-row {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-3) var(--spacing-5);
  background-color: var(--background);
  border-bottom: 1px solid var(--border);
}

.method-selector {
  flex-shrink: 0;
  width: 120px;
}

.url-input-group {
  display: flex;
  flex: 1;
  gap: var(--spacing-3);
}

.url-input {
  flex: 1;
}

.url-input .form-control {
  font-family: var(--font-family-mono);
  font-size: var(--font-size-sm);
}

.action-buttons {
  display: flex;
  gap: var(--spacing-2);
  flex-shrink: 0;
}

.compare-btn {
  background-color: var(--primary);
  color: white;
  border: none;
  padding: var(--spacing-2) var(--spacing-4);
  border-radius: var(--radius-md);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: background-color var(--duration-150) var(--ease-in-out);
}

.compare-btn:hover {
  background-color: var(--primary-hover);
}

.compare-btn:disabled {
  background-color: var(--color-gray-400);
  cursor: not-allowed;
}

/* Request Builder Tabs */
.request-tabs {
  display: flex;
  background-color: var(--surface);
  border-bottom: 1px solid var(--border);
}

.request-tab {
  padding: var(--spacing-3) var(--spacing-4);
  background-color: transparent;
  border: none;
  border-bottom: 2px solid transparent;
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--duration-150) var(--ease-in-out);
}

.request-tab:hover {
  color: var(--text-primary);
  background-color: var(--surface-hover);
}

.request-tab.active {
  color: var(--primary);
  border-bottom-color: var(--primary);
  background-color: var(--background);
}

.request-tab-content {
  padding: var(--spacing-5);
  flex: 1;
  overflow-y: auto;
  min-height: 200px;
}

/* ========== Results Area ========== */
.results-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.results-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background-color: var(--background);
}

.placeholder-content {
  text-align: center;
  color: var(--text-secondary);
}

.placeholder-icon {
  font-size: 3rem;
  margin-bottom: var(--spacing-4);
}

.placeholder-content h3 {
  margin: 0 0 var(--spacing-2) 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.placeholder-content p {
  margin: 0;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

/* Results Status Bar */
.results-status-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 40px;
  padding: 0 var(--spacing-5);
  background-color: var(--surface);
  border-bottom: 1px solid var(--border);
  font-size: var(--font-size-sm);
}

.status-section {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
}

.status-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
}

.status-success {
  color: var(--success);
}

.status-error {
  color: var(--error);
}

.status-warning {
  color: var(--warning);
}

/* Results Three-Column Layout */
.results-columns {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  height: 100%;
  overflow: hidden;
}

.result-column {
  display: flex;
  flex-direction: column;
  border-right: 1px solid var(--border);
  overflow: hidden;
}

.result-column:last-child {
  border-right: none;
}

.column-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-3) var(--spacing-4);
  background-color: var(--surface);
  border-bottom: 1px solid var(--border);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.column-title {
  color: var(--text-primary);
}

.column-controls {
  display: flex;
  gap: var(--spacing-1);
}

.column-btn {
  padding: var(--spacing-1);
  background-color: transparent;
  border: none;
  border-radius: var(--radius-sm);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--duration-150) var(--ease-in-out);
}

.column-btn:hover {
  background-color: var(--surface-hover);
  color: var(--text-primary);
}

.column-content {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-4);
}

/* ========== 键值对编辑器 ========== */
.key-value-editor {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

.key-value-row {
  display: grid;
  grid-template-columns: 1fr 1fr auto;
  gap: var(--spacing-2);
  align-items: center;
}

.key-value-input {
  font-family: var(--font-family-mono, 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace);
  font-size: var(--font-size-sm);
  padding: var(--spacing-2);
  border: 1px solid var(--border);
  border-radius: var(--radius-md);
  background-color: var(--background);
  color: var(--text-primary);
  transition: border-color var(--duration-150) var(--ease-in-out);
}

.key-value-input:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.add-row-btn {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-1);
  padding: var(--spacing-2) var(--spacing-3);
  background-color: transparent;
  border: 1px dashed var(--border);
  border-radius: var(--radius-md);
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: all var(--duration-150) var(--ease-in-out);
}

.add-row-btn:hover {
  border-color: var(--primary);
  color: var(--primary);
  background-color: var(--primary-light);
}

/* ========== 表单控件 ========== */
.form-control {
  width: 100%;
  padding: var(--spacing-2) var(--spacing-3);
  border: 1px solid var(--border);
  border-radius: var(--radius-md);
  background-color: var(--background);
  color: var(--text-primary);
  font-size: var(--font-size-sm);
  font-family: inherit;
  transition: border-color var(--duration-150) var(--ease-in-out);
}

.form-control:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-control:disabled {
  background-color: var(--surface);
  color: var(--text-disabled);
  cursor: not-allowed;
}

.form-label {
  display: block;
  margin-bottom: var(--spacing-1);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.form-group {
  margin-bottom: var(--spacing-4);
}

.json-textarea {
  font-family: var(--font-family-mono, 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace);
  resize: vertical;
  min-height: 120px;
}

.remove-btn {
  padding: var(--spacing-1);
  background-color: transparent;
  border: 1px solid var(--border);
  border-radius: var(--radius-sm);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--duration-150) var(--ease-in-out);
}

.remove-btn:hover {
  background-color: var(--color-error-50);
  border-color: var(--error);
  color: var(--error);
}

.add-row-btn {
  align-self: flex-start;
  padding: var(--spacing-2) var(--spacing-3);
  background-color: transparent;
  border: 1px dashed var(--border);
  border-radius: var(--radius-md);
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: all var(--duration-150) var(--ease-in-out);
}

.add-row-btn:hover {
  border-color: var(--primary);
  color: var(--primary);
  background-color: var(--primary-light);
}

/* ========== JSON 编辑器 ========== */
.json-editor {
  position: relative;
}

.json-textarea {
  width: 100%;
  min-height: 150px;
  padding: var(--spacing-3);
  border: 1px solid var(--border);
  border-radius: var(--radius-md);
  background-color: var(--background);
  color: var(--text-primary);
  font-family: var(--font-family-mono);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-relaxed);
  resize: vertical;
  transition: border-color var(--duration-150) var(--ease-in-out);
}

.json-textarea:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px var(--primary-light);
}

.json-error {
  margin-top: var(--spacing-2);
  padding: var(--spacing-2) var(--spacing-3);
  background-color: var(--color-error-50);
  border: 1px solid var(--error);
  border-radius: var(--radius-md);
  color: var(--error);
  font-size: var(--font-size-xs);
  font-family: var(--font-family-mono);
}

/* ========== 响应式设计 ========== */

/* 平板设备 (768px - 1199px) */
@media (max-width: 1199px) and (min-width: 768px) {
  .results-columns {
    grid-template-columns: 1fr 1fr 1fr;
  }
  
  .column-content {
    padding: var(--spacing-3);
  }
  
  .request-tab-content {
    padding: var(--spacing-4);
  }
}

/* 移动设备 (< 768px) */
@media (max-width: 767px) {
  .api-diff-tool {
    height: 100vh;
  }
  
  .tool-header {
    padding: 0 var(--spacing-3);
  }
  
  .header-actions {
    gap: var(--spacing-1);
  }
  
  .header-btn {
    padding: var(--spacing-1) var(--spacing-2);
    font-size: var(--font-size-xs);
  }
  
  .request-top-row {
    flex-direction: column;
    gap: var(--spacing-2);
    padding: var(--spacing-3);
  }
  
  .url-input-group {
    flex-direction: column;
  }
  
  .method-selector {
    width: 100%;
  }
  
  .request-tab-content {
    padding: var(--spacing-3);
  }
  
  /* 移动端使用 Tab 切换而不是三列布局 */
  .results-columns {
    display: none;
  }
  
  .results-mobile-tabs {
    display: flex;
    background-color: var(--surface);
    border-bottom: 1px solid var(--border);
  }
  
  .results-mobile-tab {
    flex: 1;
    padding: var(--spacing-3);
    background-color: transparent;
    border: none;
    border-bottom: 2px solid transparent;
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: all var(--duration-150) var(--ease-in-out);
  }
  
  .results-mobile-tab.active {
    color: var(--primary);
    border-bottom-color: var(--primary);
  }
  
  .results-mobile-content {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-3);
  }
}

/* 大屏设备优化 (>= 1200px) */
@media (min-width: 1200px) {
  .tool-header {
    padding: 0 var(--spacing-6);
  }
  
  .request-top-row {
    padding: var(--spacing-4) var(--spacing-6);
  }
  
  .request-tab-content {
    padding: var(--spacing-6);
  }
  
  .column-content {
    padding: var(--spacing-5);
  }
}
