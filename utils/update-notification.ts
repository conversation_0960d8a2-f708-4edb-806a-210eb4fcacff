/**
 * 更新通知管理器
 */
import { notificationManager } from './notification-manager';
import { updateManager, UpdateState } from './update-manager';
import { Modal } from './ui-components';
import { UpdateDetailModal } from './update-components';

export class UpdateNotificationManager {
  private static instance: UpdateNotificationManager;
  private bannerElement: HTMLElement | null = null;

  static getInstance(): UpdateNotificationManager {
    if (!UpdateNotificationManager.instance) {
      UpdateNotificationManager.instance = new UpdateNotificationManager();
    }
    return UpdateNotificationManager.instance;
  }

  /**
   * 显示更新横幅
   */
  showUpdateBanner(updateCount: number): void {
    if (this.bannerElement) {
      this.hideBanner();
    }

    this.bannerElement = this.createBanner(updateCount);
    const container = document.querySelector('.app-container') || document.body;
    container.insertBefore(this.bannerElement, container.firstChild);
  }

  /**
   * 隐藏更新横幅
   */
  hideBanner(): void {
    if (this.bannerElement && this.bannerElement.parentNode) {
      this.bannerElement.parentNode.removeChild(this.bannerElement);
      this.bannerElement = null;
    }
  }

  /**
   * 创建更新横幅
   */
  private createBanner(updateCount: number): HTMLElement {
    const banner = document.createElement('div');
    banner.className = 'update-banner';
    banner.innerHTML = `
      <div class="update-banner-content">
        <div class="update-banner-text">
          <span class="update-banner-title">🔔 发现 ${updateCount} 个工具更新，点击查看详情</span>
        </div>
        <div class="update-banner-actions">
          <button class="btn btn-sm btn-primary update-banner-details">查看详情</button>
          <button class="btn btn-sm btn-ghost update-banner-dismiss">稍后提醒</button>
          <button class="btn btn-sm btn-ghost update-banner-close">×</button>
        </div>
      </div>
    `;

    this.bindBannerEvents(banner);
    return banner;
  }

  /**
   * 绑定横幅事件
   */
  private bindBannerEvents(banner: HTMLElement): void {
    // 查看详情按钮
    const detailsBtn = banner.querySelector('.update-banner-details');
    detailsBtn?.addEventListener('click', () => {
      this.showUpdateDetails();
    });

    // 稍后提醒按钮
    const dismissBtn = banner.querySelector('.update-banner-dismiss');
    dismissBtn?.addEventListener('click', () => {
      this.dismissAllUpdates();
      this.hideBanner();
    });

    // 关闭按钮
    const closeBtn = banner.querySelector('.update-banner-close');
    closeBtn?.addEventListener('click', () => {
      this.hideBanner();
    });

    // 点击横幅主体也可以查看详情
    const content = banner.querySelector('.update-banner-text');
    content?.addEventListener('click', () => {
      this.showUpdateDetails();
    });
  }

  /**
   * 显示更新详情
   */
  private showUpdateDetails(): void {
    const updateStates = updateManager.getAllUpdateStates();
    const availableUpdates = new Map();

    // 筛选出有更新且未被忽略的工具，并转换为UpdateCheckResult格式
    for (const [toolId, state] of updateStates) {
      if (state.hasUpdate && !state.userDismissed && state.updateInfo) {
        // 使用新的忽略逻辑检查
        if (!updateManager.isUpdateIgnored(state, state.updateInfo)) {
          availableUpdates.set(toolId, state.updateInfo);
        }
      }
    }

    if (availableUpdates.size === 0) {
      notificationManager.info('暂无可用更新');
      return;
    }

    // 使用统一的UpdateDetailModal组件
    const modal = new UpdateDetailModal(availableUpdates);
    modal.show();
  }







  /**
   * 暂时关闭所有更新通知
   */
  private async dismissAllUpdates(): Promise<void> {
    await updateManager.dismissAllUpdates();
    notificationManager.info('已暂时关闭更新提醒');
  }

  /**
   * 检查并显示更新通知
   */
  checkAndShowNotifications(): void {
    const updateCount = updateManager.getUpdateCount();
    
    if (updateCount > 0) {
      this.showUpdateBanner(updateCount);
    } else {
      this.hideBanner();
    }
  }
}

// 导出单例实例
export const updateNotificationManager = UpdateNotificationManager.getInstance();
