/**
 * UI组件库 - JavaScript部分
 * 提供可复用的UI组件创建和管理功能
 */

export interface ModalOptions {
  title?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  closable?: boolean;
  backdrop?: boolean;
  keyboard?: boolean;
}

export interface ToastOptions {
  type?: 'success' | 'warning' | 'error' | 'info';
  duration?: number;
  closable?: boolean;
}

export interface DropdownOptions {
  trigger?: 'click' | 'hover';
  placement?: 'bottom' | 'top' | 'left' | 'right';
  offset?: number;
}

/**
 * 模态框组件
 */
export class Modal {
  private element: HTMLElement;
  private options: ModalOptions;
  private isOpen = false;

  constructor(content: string | HTMLElement, options: ModalOptions = {}) {
    this.options = {
      size: 'md',
      closable: true,
      backdrop: true,
      keyboard: true,
      ...options
    };

    this.element = this.createElement(content);
    this.bindEvents();
  }

  private createElement(content: string | HTMLElement): HTMLElement {
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.style.display = 'none';

    const modalContent = document.createElement('div');
    modalContent.className = `modal-content modal-${this.options.size}`;

    // 创建头部
    if (this.options.title || this.options.closable) {
      const header = document.createElement('div');
      header.className = 'modal-header';

      if (this.options.title) {
        const title = document.createElement('h3');
        title.className = 'modal-title';
        title.textContent = this.options.title;
        header.appendChild(title);
      }

      if (this.options.closable) {
        const closeBtn = document.createElement('button');
        closeBtn.className = 'modal-close';
        closeBtn.innerHTML = '&times;';
        closeBtn.addEventListener('click', () => this.close());
        header.appendChild(closeBtn);
      }

      modalContent.appendChild(header);
    }

    // 创建主体
    const body = document.createElement('div');
    body.className = 'modal-body';
    
    if (typeof content === 'string') {
      body.innerHTML = content;
    } else {
      body.appendChild(content);
    }

    modalContent.appendChild(body);
    modal.appendChild(modalContent);

    return modal;
  }

  private bindEvents(): void {
    // 点击背景关闭
    if (this.options.backdrop) {
      this.element.addEventListener('click', (e) => {
        if (e.target === this.element) {
          this.close();
        }
      });
    }

    // ESC键关闭
    if (this.options.keyboard) {
      document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && this.isOpen) {
          this.close();
        }
      });
    }
  }

  open(): void {
    if (this.isOpen) return;

    document.body.appendChild(this.element);
    this.element.style.display = 'flex';
    this.isOpen = true;

    // 触发打开事件
    this.element.dispatchEvent(new CustomEvent('modal:open'));
  }

  close(): void {
    if (!this.isOpen) return;

    this.element.style.display = 'none';
    if (this.element.parentNode) {
      this.element.parentNode.removeChild(this.element);
    }
    this.isOpen = false;

    // 触发关闭事件
    this.element.dispatchEvent(new CustomEvent('modal:close'));
  }

  getElement(): HTMLElement {
    return this.element;
  }

  setContent(content: string | HTMLElement): void {
    const body = this.element.querySelector('.modal-body');
    if (body) {
      if (typeof content === 'string') {
        body.innerHTML = content;
      } else {
        body.innerHTML = '';
        body.appendChild(content);
      }
    }
  }

  addFooter(content: string | HTMLElement): void {
    const modalContent = this.element.querySelector('.modal-content');
    if (modalContent) {
      const footer = document.createElement('div');
      footer.className = 'modal-footer';
      
      if (typeof content === 'string') {
        footer.innerHTML = content;
      } else {
        footer.appendChild(content);
      }
      
      modalContent.appendChild(footer);
    }
  }
}

/**
 * 通知组件
 */
export class Toast {
  private static container: HTMLElement | null = null;
  private element: HTMLElement;
  private options: ToastOptions;
  private timer?: number;

  constructor(message: string, options: ToastOptions = {}) {
    this.options = {
      type: 'info',
      duration: 5000,
      closable: true,
      ...options
    };

    this.element = this.createElement(message);
    this.bindEvents();
  }

  private static getContainer(): HTMLElement {
    if (!Toast.container) {
      Toast.container = document.createElement('div');
      Toast.container.className = 'toast-container';
      Toast.container.style.cssText = `
        position: fixed;
        top: 1rem;
        right: 1rem;
        z-index: 1080;
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
      `;
      document.body.appendChild(Toast.container);
    }
    return Toast.container;
  }

  private createElement(message: string): HTMLElement {
    const toast = document.createElement('div');
    toast.className = `toast toast-${this.options.type}`;

    const header = document.createElement('div');
    header.className = 'toast-header';

    const title = document.createElement('div');
    title.className = 'toast-title';
    title.textContent = this.getTypeTitle();
    header.appendChild(title);

    if (this.options.closable) {
      const closeBtn = document.createElement('button');
      closeBtn.className = 'toast-close';
      closeBtn.innerHTML = '&times;';
      closeBtn.addEventListener('click', () => this.close());
      header.appendChild(closeBtn);
    }

    const body = document.createElement('div');
    body.className = 'toast-body';
    body.textContent = message;

    toast.appendChild(header);
    toast.appendChild(body);

    return toast;
  }

  private getTypeTitle(): string {
    const titles = {
      success: '成功',
      warning: '警告',
      error: '错误',
      info: '信息'
    };
    return titles[this.options.type!] || '通知';
  }

  private bindEvents(): void {
    if (this.options.duration && this.options.duration > 0) {
      this.timer = window.setTimeout(() => {
        this.close();
      }, this.options.duration);
    }
  }

  show(): void {
    const container = Toast.getContainer();
    container.appendChild(this.element);
    
    // 触发动画
    setTimeout(() => {
      this.element.classList.add('show');
    }, 10);
  }

  close(): void {
    if (this.timer) {
      clearTimeout(this.timer);
    }

    this.element.classList.remove('show');
    
    setTimeout(() => {
      if (this.element.parentNode) {
        this.element.parentNode.removeChild(this.element);
      }
    }, 300);
  }

  static success(message: string, options?: Omit<ToastOptions, 'type'>): Toast {
    const toast = new Toast(message, { ...options, type: 'success' });
    toast.show();
    return toast;
  }

  static warning(message: string, options?: Omit<ToastOptions, 'type'>): Toast {
    const toast = new Toast(message, { ...options, type: 'warning' });
    toast.show();
    return toast;
  }

  static error(message: string, options?: Omit<ToastOptions, 'type'>): Toast {
    const toast = new Toast(message, { ...options, type: 'error' });
    toast.show();
    return toast;
  }

  static info(message: string, options?: Omit<ToastOptions, 'type'>): Toast {
    const toast = new Toast(message, { ...options, type: 'info' });
    toast.show();
    return toast;
  }
}

/**
 * 下拉菜单组件
 */
export class Dropdown {
  private trigger: HTMLElement;
  private menu: HTMLElement;
  private options: DropdownOptions;
  private isOpen = false;

  constructor(trigger: HTMLElement, menu: HTMLElement, options: DropdownOptions = {}) {
    this.trigger = trigger;
    this.menu = menu;
    this.options = {
      trigger: 'click',
      placement: 'bottom',
      offset: 4,
      ...options
    };

    this.init();
  }

  private init(): void {
    this.trigger.classList.add('dropdown-trigger');
    this.menu.classList.add('dropdown-menu');
    
    // 设置初始位置
    this.menu.style.position = 'absolute';
    this.menu.style.display = 'none';

    this.bindEvents();
  }

  private bindEvents(): void {
    if (this.options.trigger === 'click') {
      this.trigger.addEventListener('click', (e) => {
        e.stopPropagation();
        this.toggle();
      });

      document.addEventListener('click', () => {
        if (this.isOpen) {
          this.close();
        }
      });
    } else if (this.options.trigger === 'hover') {
      this.trigger.addEventListener('mouseenter', () => this.open());
      this.trigger.addEventListener('mouseleave', () => this.close());
      this.menu.addEventListener('mouseenter', () => this.open());
      this.menu.addEventListener('mouseleave', () => this.close());
    }
  }

  private updatePosition(): void {
    const triggerRect = this.trigger.getBoundingClientRect();
    const menuRect = this.menu.getBoundingClientRect();
    
    let top = 0;
    let left = 0;

    switch (this.options.placement) {
      case 'bottom':
        top = triggerRect.bottom + this.options.offset!;
        left = triggerRect.left;
        break;
      case 'top':
        top = triggerRect.top - menuRect.height - this.options.offset!;
        left = triggerRect.left;
        break;
      case 'left':
        top = triggerRect.top;
        left = triggerRect.left - menuRect.width - this.options.offset!;
        break;
      case 'right':
        top = triggerRect.top;
        left = triggerRect.right + this.options.offset!;
        break;
    }

    this.menu.style.top = `${top}px`;
    this.menu.style.left = `${left}px`;
  }

  open(): void {
    if (this.isOpen) return;

    this.menu.style.display = 'block';
    this.updatePosition();
    this.trigger.classList.add('dropdown-open');
    this.isOpen = true;
  }

  close(): void {
    if (!this.isOpen) return;

    this.menu.style.display = 'none';
    this.trigger.classList.remove('dropdown-open');
    this.isOpen = false;
  }

  toggle(): void {
    if (this.isOpen) {
      this.close();
    } else {
      this.open();
    }
  }
}

/**
 * 工具函数
 */
export const UIUtils = {
  /**
   * 创建按钮元素
   */
  createButton(text: string, variant: string = 'primary', size: string = 'md'): HTMLButtonElement {
    const button = document.createElement('button');
    button.className = `btn btn-${variant} btn-${size}`;
    button.textContent = text;
    return button;
  },

  /**
   * 创建输入框元素
   */
  createInput(type: string = 'text', placeholder: string = '', size: string = 'md'): HTMLInputElement {
    const input = document.createElement('input');
    input.type = type;
    input.className = `form-control form-control-${size}`;
    input.placeholder = placeholder;
    return input;
  },

  /**
   * 创建表单组
   */
  createFormGroup(label: string, input: HTMLElement): HTMLElement {
    const group = document.createElement('div');
    group.className = 'form-group';

    const labelEl = document.createElement('label');
    labelEl.className = 'form-label';
    labelEl.textContent = label;

    group.appendChild(labelEl);
    group.appendChild(input);

    return group;
  },

  /**
   * 创建加载器
   */
  createSpinner(size: string = 'md'): HTMLElement {
    const spinner = document.createElement('div');
    spinner.className = `spinner spinner-${size}`;
    return spinner;
  },

  /**
   * 创建徽章
   */
  createBadge(text: string, variant: string = 'primary'): HTMLElement {
    const badge = document.createElement('span');
    badge.className = `badge badge-${variant}`;
    badge.textContent = text;
    return badge;
  }
};
