/**
 * XUID切换助手 - 存储管理器 (TypeScript版本)
 * 负责数据的存储、读取和管理
 * 支持固定域名分组和跨子域名管理
 */

// 类型定义
export interface XuidRecord {
  value: string;
  cookieName: string;
  username?: string;
  assistantUid?: string;
}

export interface XuidSettings {
  maxXuidCount: number;
  expandedDomains: string[];
  xuidCookieNames: string[];
}

export interface AssetInfo {
  type?: string;
  assistantUid?: string;
  [key: string]: any;
}

export interface XuidData {
  xuids: Record<string, XuidRecord[]>;
  settings: XuidSettings;
  assetInfo: Record<string, AssetInfo>;
  assetInfoList: Record<string, AssetInfo>;
}

export interface DomainGroup {
  pattern: RegExp;
  displayName: string;
}

export interface GroupedXuidData {
  [groupKey: string]: {
    displayName: string;
    xuids: (XuidRecord & { sourceDomain: string })[];
  };
}

export interface OperationResult {
  success: boolean;
  message?: string;
}

export class XuidStorageManager {
  private defaultData: XuidData = {
    xuids: {},
    settings: {
      maxXuidCount: 100,
      expandedDomains: [],
      xuidCookieNames: ['XUID', 'xuid', 'Xuid', 'XUid', 'xuId', 'xUid', 'xUId']
    },
    assetInfo: {},
    assetInfoList: {}
  };

  // 固定域名分组配置
  private domainGroups: Record<string, DomainGroup> = {
    'zuoyebang.cc': {
      pattern: /(^|\.)(zuoyebang\.cc)$/,
      displayName: 'zuoyebang.cc'
    },
    'suanshubang.cc': {
      pattern: /(^|\.)(suanshubang\.cc)$/,
      displayName: 'suanshubang.cc'
    }
  };

  /**
   * 加载存储的数据
   */
  async loadData(): Promise<XuidData> {
    try {
      const result = await chrome.storage.local.get(['xuids', 'settings', 'assetInfo', 'assetInfoList']);

      const data: XuidData = {
        xuids: result.xuids || {},
        settings: { ...this.defaultData.settings, ...(result.settings || {}) },
        assetInfo: result.assetInfo || {},
        assetInfoList: result.assetInfoList || {}
      };

      return data;
    } catch (error) {
      console.error('加载数据失败:', error);
      return this.defaultData;
    }
  }

  /**
   * 保存数据到存储
   */
  async saveData(data: XuidData): Promise<boolean> {
    try {
      await chrome.storage.local.set({
        xuids: data.xuids,
        settings: data.settings,
        assetInfo: data.assetInfo || {},
        assetInfoList: data.assetInfoList || {}
      });
      return true;
    } catch (error) {
      console.error('保存数据失败:', error);
      throw error;
    }
  }

  /**
   * 获取域名所属的分组
   */
  getDomainGroup(domain: string): string | null {
    for (const [groupKey, groupConfig] of Object.entries(this.domainGroups)) {
      if (groupConfig.pattern.test(domain)) {
        return groupKey;
      }
    }
    return null;
  }

  /**
   * 检查XUID是否已存在于同一分组的其他域名中
   */
  isXuidExistsInGroup(xuidValue: string, domain: string, currentData: XuidData): boolean {
    const domainGroup = this.getDomainGroup(domain);
    if (!domainGroup) return false;

    // 遍历同一分组内的所有域名
    for (const [existingDomain, xuids] of Object.entries(currentData.xuids)) {
      if (this.getDomainGroup(existingDomain) === domainGroup) {
        const exists = xuids.some(item => {
          const value = typeof item === 'string' ? item : item.value;
          return value === xuidValue;
        });
        if (exists) return true;
      }
    }
    return false;
  }

  /**
   * 验证XUID格式
   */
  validateXuidFormat(xuid: string): boolean {
    if (!xuid || typeof xuid !== 'string') {
      return false;
    }

    // 去除空白字符
    xuid = xuid.trim();
    
    // 检查是否为纯数字
    return /^\d+$/.test(xuid) && xuid.length > 0;
  }

  /**
   * 获取总XUID数量
   */
  getTotalXuidCount(data: XuidData): number {
    let total = 0;
    Object.values(data.xuids).forEach(xuids => {
      if (Array.isArray(xuids)) {
        total += xuids.length;
      }
    });
    return total;
  }

  /**
   * 添加XUID记录
   */
  async addXuidRecord(
    domain: string,
    xuidValue: string,
    cookieName: string,
    currentData: XuidData,
    username?: string,
    assistantUid?: string
  ): Promise<OperationResult> {
    try {
      // 检查是否已存在
      if (!currentData.xuids[domain]) {
        currentData.xuids[domain] = [];
      }

      // 检查当前域名下是否已存在相同的XUID
      const existingXuid = currentData.xuids[domain].find(item => {
        const value = typeof item === 'string' ? item : item.value;
        return value === xuidValue;
      });

      if (existingXuid) {
        // 如果XUID已存在但有新的用户名或assistantUid信息，更新信息
        if (typeof existingXuid === 'object') {
          let updated = false;
          if (username && !existingXuid.username) {
            existingXuid.username = username;
            updated = true;
          }
          if (assistantUid && !existingXuid.assistantUid) {
            existingXuid.assistantUid = assistantUid;
            updated = true;
          }
          if (updated) {
            await this.saveData(currentData);
            return { success: true, message: 'XUID已存在，信息已更新' };
          }
        }
        return { success: false, message: '该XUID已经存在' };
      }

      // 检查是否已存在于同一分组的其他域名中，如果存在则跳过
      if (this.isXuidExistsInGroup(xuidValue, domain, currentData)) {
        console.log(`XUID ${xuidValue} 已存在于同一分组的其他域名中，跳过记录`);
        return { success: true, message: 'XUID记录成功' };
      }

      // 检查总数限制
      const totalCount = this.getTotalXuidCount(currentData);
      if (totalCount >= currentData.settings.maxXuidCount) {
        return {
          success: false,
          message: `已达到最大限制(${currentData.settings.maxXuidCount}个)`
        };
      }

      // 添加XUID记录
      const xuidRecord: XuidRecord = {
        value: xuidValue,
        cookieName: cookieName
      };

      if (username) {
        xuidRecord.username = username;
      }

      if (assistantUid) {
        xuidRecord.assistantUid = assistantUid;
      }

      currentData.xuids[domain].push(xuidRecord);

      await this.saveData(currentData);
      return { success: true, message: 'XUID记录成功' };

    } catch (error) {
      console.error('添加XUID记录失败:', error);
      return { success: false, message: '添加XUID记录失败' };
    }
  }

  /**
   * 删除单个XUID记录 - 删除所有子域名下的相同XUID
   */
  async deleteXuidRecord(domain: string, xuidValue: string, currentData: XuidData): Promise<OperationResult> {
    try {
      const domainGroup = this.getDomainGroup(domain);
      let deletedCount = 0;

      if (domainGroup) {
        // 删除同一分组内所有域名下的相同XUID
        for (const [existingDomain, xuids] of Object.entries(currentData.xuids)) {
          if (this.getDomainGroup(existingDomain) === domainGroup) {
            const originalLength = xuids.length;

            // 过滤掉要删除的XUID
            currentData.xuids[existingDomain] = xuids.filter(item => {
              const value = typeof item === 'string' ? item : item.value;
              return value !== xuidValue;
            });

            if (currentData.xuids[existingDomain].length < originalLength) {
              deletedCount++;
            }

            // 如果域名下没有XUID了，删除整个域名分组
            if (currentData.xuids[existingDomain].length === 0) {
              delete currentData.xuids[existingDomain];
            }
          }
        }
      } else {
        // 不属于已知分组，只删除当前域名下的XUID
        if (!currentData.xuids[domain]) {
          return { success: false, message: '域名不存在' };
        }

        const originalLength = currentData.xuids[domain].length;

        // 过滤掉要删除的XUID
        currentData.xuids[domain] = currentData.xuids[domain].filter(item => {
          const value = typeof item === 'string' ? item : item.value;
          return value !== xuidValue;
        });

        if (currentData.xuids[domain].length < originalLength) {
          deletedCount = 1;
        }

        // 如果域名下没有XUID了，删除整个域名分组
        if (currentData.xuids[domain].length === 0) {
          delete currentData.xuids[domain];
        }
      }

      if (deletedCount === 0) {
        return { success: false, message: 'XUID不存在' };
      }

      await this.saveData(currentData);
      return {
        success: true,
        message: domainGroup ?
          `XUID删除成功，共删除${deletedCount}个域名下的记录` :
          'XUID删除成功'
      };

    } catch (error) {
      console.error('删除XUID记录失败:', error);
      return { success: false, message: '删除XUID记录失败' };
    }
  }

  /**
   * 批量删除XUID记录
   */
  async deleteXuidRecords(deleteGroups: Record<string, string[]>, currentData: XuidData): Promise<OperationResult> {
    try {
      Object.entries(deleteGroups).forEach(([domain, values]) => {
        if (currentData.xuids[domain]) {
          currentData.xuids[domain] = currentData.xuids[domain].filter(item => {
            const itemValue = typeof item === 'string' ? item : item.value;
            return !values.includes(itemValue);
          });

          // 如果该域名下没有XUID了，删除整个域名
          if (currentData.xuids[domain].length === 0) {
            delete currentData.xuids[domain];
          }
        }
      });

      await this.saveData(currentData);
      return { success: true };

    } catch (error) {
      console.error('删除XUID记录失败:', error);
      return { success: false, message: '删除XUID记录失败' };
    }
  }

  /**
   * 更新域名展开状态
   */
  async updateExpandedDomains(domain: string, isExpanded: boolean, currentData: XuidData): Promise<OperationResult> {
    try {
      if (isExpanded) {
        if (!currentData.settings.expandedDomains.includes(domain)) {
          currentData.settings.expandedDomains.push(domain);
        }
      } else {
        const index = currentData.settings.expandedDomains.indexOf(domain);
        if (index > -1) {
          currentData.settings.expandedDomains.splice(index, 1);
        }
      }

      await this.saveData(currentData);
      return { success: true };

    } catch (error) {
      console.error('更新展开状态失败:', error);
      return { success: false };
    }
  }

  /**
   * 数据格式兼容性处理
   */
  normalizeXuidData(xuids: Record<string, any>): Record<string, XuidRecord[]> {
    const normalized: Record<string, XuidRecord[]> = {};

    Object.entries(xuids).forEach(([domain, domainXuids]) => {
      if (Array.isArray(domainXuids)) {
        normalized[domain] = domainXuids.map(item => {
          if (typeof item === 'string') {
            // 旧格式兼容
            return {
              value: item,
              cookieName: 'XUID'
            };
          }
          // 确保新格式包含所有必要字段
          return {
            value: item.value,
            cookieName: item.cookieName || 'XUID',
            username: item.username || undefined
          };
        });
      }
    });

    return normalized;
  }

  /**
   * 更新XUID记录的用户名
   */
  async updateXuidUsername(domain: string, xuidValue: string, username: string, currentData: XuidData): Promise<OperationResult> {
    try {
      if (!currentData.xuids[domain]) {
        return { success: false, message: '域名不存在' };
      }

      const xuidRecord = currentData.xuids[domain].find(item => {
        const value = typeof item === 'string' ? item : item.value;
        return value === xuidValue;
      });

      if (!xuidRecord) {
        return { success: false, message: 'XUID记录不存在' };
      }

      // 更新用户名
      if (typeof xuidRecord === 'object') {
        xuidRecord.username = username;
        await this.saveData(currentData);
        return { success: true, message: '用户名更新成功' };
      }

      return { success: false, message: '无法更新旧格式数据' };

    } catch (error) {
      console.error('更新用户名失败:', error);
      return { success: false, message: '更新用户名失败' };
    }
  }

  /**
   * 批量更新用户名绑定
   */
  async batchUpdateUsernames(usernameMap: Map<string, string>, currentData: XuidData): Promise<OperationResult> {
    try {
      let hasChanges = false;

      // 遍历所有域名和XUID记录
      Object.keys(currentData.xuids).forEach(domain => {
        if (currentData.xuids[domain] && Array.isArray(currentData.xuids[domain])) {
          currentData.xuids[domain].forEach(xuidRecord => {
            if (typeof xuidRecord === 'object' && xuidRecord.value) {
              const xuidValue = xuidRecord.value;

              // 如果有对应的用户名且当前记录没有用户名
              if (usernameMap.has(xuidValue) && !xuidRecord.username) {
                xuidRecord.username = usernameMap.get(xuidValue);
                hasChanges = true;
                console.log(`批量更新用户名: ${xuidValue} -> ${xuidRecord.username}`);
              }
            }
          });
        }
      });

      if (hasChanges) {
        await this.saveData(currentData);
        return { success: true, message: '批量用户名更新成功' };
      }

      return { success: false, message: '没有需要更新的用户名' };

    } catch (error) {
      console.error('批量更新用户名失败:', error);
      return { success: false, message: '批量更新用户名失败' };
    }
  }

  /**
   * 查找所有缺少用户名的XUID
   */
  findXuidsWithoutUsername(currentData: XuidData): string[] {
    const xuidsWithoutUsername = new Set<string>();

    Object.values(currentData.xuids).forEach(domainXuids => {
      if (Array.isArray(domainXuids)) {
        domainXuids.forEach(xuidRecord => {
          if (typeof xuidRecord === 'object' && xuidRecord.value && !xuidRecord.username) {
            xuidsWithoutUsername.add(xuidRecord.value);
          }
        });
      }
    });

    return Array.from(xuidsWithoutUsername);
  }

  /**
   * 获取按固定分组整理的XUID数据
   */
  getGroupedXuidData(currentData: XuidData): GroupedXuidData {
    const groupedData: GroupedXuidData = {};

    // 初始化固定分组
    Object.keys(this.domainGroups).forEach(groupKey => {
      groupedData[groupKey] = {
        displayName: this.domainGroups[groupKey].displayName,
        xuids: []
      };
    });

    // 使用Map来去重，key为XUID值，value为XUID记录
    const groupMaps: Record<string, Map<string, XuidRecord & { sourceDomain: string }>> = {};
    Object.keys(this.domainGroups).forEach(groupKey => {
      groupMaps[groupKey] = new Map();
    });

    // 遍历所有域名数据，按分组归类
    Object.entries(currentData.xuids).forEach(([domain, xuids]) => {
      const domainGroup = this.getDomainGroup(domain);

      if (domainGroup && groupedData[domainGroup]) {
        xuids.forEach(xuidRecord => {
          const xuidValue = typeof xuidRecord === 'string' ? xuidRecord : xuidRecord.value;

          // 去重：如果XUID已存在，保留有用户名的记录
          if (!groupMaps[domainGroup].has(xuidValue)) {
            groupMaps[domainGroup].set(xuidValue, {
              ...xuidRecord,
              sourceDomain: domain
            } as XuidRecord & { sourceDomain: string });
          } else {
            // 如果新记录有用户名而旧记录没有，则更新
            const existingRecord = groupMaps[domainGroup].get(xuidValue)!;
            const newRecord = typeof xuidRecord === 'string' ? { value: xuidRecord, cookieName: 'XUID' } : xuidRecord;

            if (newRecord.username && !existingRecord.username) {
              groupMaps[domainGroup].set(xuidValue, {
                ...newRecord,
                sourceDomain: domain
              } as XuidRecord & { sourceDomain: string });
            }
          }
        });
      }
    });

    // 转换Map为Array
    Object.keys(groupedData).forEach(groupKey => {
      groupedData[groupKey].xuids = Array.from(groupMaps[groupKey].values());
    });

    return groupedData;
  }

  /**
   * 获取当前域名所属分组的展开状态
   */
  getCurrentDomainGroupExpanded(currentDomain: string, expandedDomains: string[]): string[] {
    const domainGroup = this.getDomainGroup(currentDomain);
    if (!domainGroup) return [];

    // 检查当前分组内是否有任何域名被展开
    const groupExpanded = expandedDomains.some(domain =>
      this.getDomainGroup(domain) === domainGroup
    );

    return groupExpanded ? [domainGroup] : [];
  }

  /**
   * 检查XUID是否属于当前域名分组
   */
  isXuidInCurrentDomainGroup(currentDomain: string, targetGroupKey: string): boolean {
    const currentDomainGroup = this.getDomainGroup(currentDomain);
    return currentDomainGroup === targetGroupKey;
  }

  /**
   * 获取当前域名所属分组的所有XUID（用于域名限制功能）
   */
  getCurrentDomainGroupXuids(currentDomain: string, currentData: XuidData): (XuidRecord & { sourceDomain: string })[] {
    const currentDomainGroup = this.getDomainGroup(currentDomain);
    if (!currentDomainGroup) return [];

    const groupedData = this.getGroupedXuidData(currentData);
    return groupedData[currentDomainGroup] ? groupedData[currentDomainGroup].xuids : [];
  }

  /**
   * 清理过期或无效数据
   */
  async cleanupData(currentData: XuidData): Promise<XuidData> {
    try {
      let hasChanges = false;

      // 清理空的域名分组
      Object.keys(currentData.xuids).forEach(domain => {
        if (!currentData.xuids[domain] || currentData.xuids[domain].length === 0) {
          delete currentData.xuids[domain];
          hasChanges = true;
        }
      });

      // 清理无效的展开域名
      const validDomains = Object.keys(currentData.xuids);
      currentData.settings.expandedDomains = currentData.settings.expandedDomains.filter(
        domain => validDomains.includes(domain)
      );

      if (hasChanges) {
        await this.saveData(currentData);
      }

      return currentData;

    } catch (error) {
      console.error('清理数据失败:', error);
      return currentData;
    }
  }

  /**
   * 保存状态信息资产数据（精确匹配）
   */
  async saveAssetInfo(xuid: string, assetInfo: AssetInfo): Promise<boolean> {
    try {
      const currentData = await this.loadData();
      currentData.assetInfo[xuid] = assetInfo;
      await this.saveData(currentData);
      return true;
    } catch (error) {
      console.error('保存状态信息资产数据失败:', error);
      return false;
    }
  }

  /**
   * 获取状态信息资产数据（精确匹配）
   */
  async getAssetInfo(xuid: string): Promise<AssetInfo | null> {
    try {
      const currentData = await this.loadData();
      return currentData.assetInfo[xuid] || null;
    } catch (error) {
      console.error('获取状态信息资产数据失败:', error);
      return null;
    }
  }

  /**
   * 保存XUID管理资产数据（列表分析）
   */
  async saveAssetInfoForList(xuid: string, assetInfo: AssetInfo): Promise<boolean> {
    try {
      const currentData = await this.loadData();
      currentData.assetInfoList[xuid] = assetInfo;
      await this.saveData(currentData);
      return true;
    } catch (error) {
      console.error('保存XUID管理资产数据失败:', error);
      return false;
    }
  }

  /**
   * 获取XUID管理资产数据（列表分析）
   */
  async getAssetInfoForList(xuid: string): Promise<AssetInfo | null> {
    try {
      const currentData = await this.loadData();
      return currentData.assetInfoList[xuid] || null;
    } catch (error) {
      console.error('获取XUID管理资产数据失败:', error);
      return null;
    }
  }

  /**
   * 批量保存XUID管理资产数据
   */
  async batchSaveAssetInfoForList(assetInfoMap: Record<string, AssetInfo>): Promise<boolean> {
    try {
      const currentData = await this.loadData();
      Object.assign(currentData.assetInfoList, assetInfoMap);
      await this.saveData(currentData);
      return true;
    } catch (error) {
      console.error('批量保存XUID管理资产数据失败:', error);
      return false;
    }
  }
}
