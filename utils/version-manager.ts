/**
 * 版本管理服务
 * 负责工具版本检查、更新等功能
 */

import { Tool, ToolVersion, VersionUtils } from './tool-template';

export interface UpdateCheckResult {
  hasUpdate: boolean;
  currentVersion: ToolVersion;
  latestVersion?: ToolVersion;
  changelog?: string;
  releaseDate?: string;
  updateType?: 'major' | 'minor' | 'patch';
  priority?: 'low' | 'medium' | 'high' | 'critical';
}

export interface UpdateResult {
  success: boolean;
  newVersion?: ToolVersion;
  error?: string;
}

export class VersionManager {
  private static instance: VersionManager;
  private updateCheckCache = new Map<string, UpdateCheckResult>();
  private cacheExpiry = 5 * 60 * 1000; // 5分钟缓存

  static getInstance(): VersionManager {
    if (!VersionManager.instance) {
      VersionManager.instance = new VersionManager();
    }
    return VersionManager.instance;
  }

  /**
   * 检查工具是否有更新
   */
  async checkForUpdate(tool: Tool): Promise<UpdateCheckResult> {
    const cacheKey = `${tool.id}-${VersionUtils.versionToString(tool.version)}`;
    
    // 检查缓存
    const cached = this.updateCheckCache.get(cacheKey);
    if (cached && Date.now() - (cached as any).timestamp < this.cacheExpiry) {
      return cached;
    }

    try {
      // 这里将来会调用真实的API
      const result = await this.fetchUpdateInfo(tool);
      
      // 缓存结果
      (result as any).timestamp = Date.now();
      this.updateCheckCache.set(cacheKey, result);
      
      return result;
    } catch (error) {
      console.error(`检查工具 ${tool.id} 更新失败:`, error);
      return {
        hasUpdate: false,
        currentVersion: tool.version
      };
    }
  }

  /**
   * 批量检查多个工具的更新
   */
  async checkMultipleUpdates(tools: Tool[]): Promise<Map<string, UpdateCheckResult>> {
    const results = new Map<string, UpdateCheckResult>();
    
    // 并发检查，但限制并发数
    const batchSize = 3;
    for (let i = 0; i < tools.length; i += batchSize) {
      const batch = tools.slice(i, i + batchSize);
      const promises = batch.map(tool => 
        this.checkForUpdate(tool).then(result => ({ toolId: tool.id, result }))
      );
      
      const batchResults = await Promise.all(promises);
      batchResults.forEach(({ toolId, result }) => {
        results.set(toolId, result);
      });
    }
    
    return results;
  }

  /**
   * 清除指定工具的缓存
   */
  private clearToolCache(toolId: string) {
    const keysToDelete = Array.from(this.updateCheckCache.keys())
      .filter(key => key.startsWith(`${toolId}-`));
    
    keysToDelete.forEach(key => {
      this.updateCheckCache.delete(key);
    });
  }

  /**
   * 模拟API调用 - 检查更新信息
   * 将来这里会替换为真实的API调用
   */
  private async fetchUpdateInfo(tool: Tool): Promise<UpdateCheckResult> {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 1000));

    // 为了测试版本基础忽略功能，生成一个递增的版本号
    // 使用完整的时间戳确保版本号始终递增
    const timestamp = Math.floor(Date.now() / 1000); // 完整的时间戳
    const latestVersion: ToolVersion = {
      major: tool.version.major,
      minor: tool.version.minor,
      patch: timestamp // 使用时间戳作为patch版本，确保递增
    };

    console.log(`🔄 模拟检查 ${tool.id} 更新: ${VersionUtils.versionToString(tool.version)} -> ${VersionUtils.versionToString(latestVersion)}`);

    // 根据版本变化确定更新类型
    const updateType = this.determineUpdateType(tool.version, latestVersion);

    // 根据更新类型确定优先级
    const priority = this.determinePriority(updateType);

    return {
      hasUpdate: true,
      currentVersion: tool.version,
      latestVersion,
      changelog: `修复了一些问题，提升了性能 (v${VersionUtils.versionToString(latestVersion)})`,
      releaseDate: new Date().toISOString(),
      updateType,
      priority
    };
  }

  /**
   * 根据版本变化确定更新类型
   */
  private determineUpdateType(currentVersion: ToolVersion, latestVersion: ToolVersion): 'major' | 'minor' | 'patch' {
    if (latestVersion.major > currentVersion.major) {
      return 'major';
    } else if (latestVersion.minor > currentVersion.minor) {
      return 'minor';
    } else {
      return 'patch';
    }
  }

  /**
   * 根据更新类型确定优先级
   */
  private determinePriority(updateType: 'major' | 'minor' | 'patch'): 'low' | 'medium' | 'high' | 'critical' {
    switch (updateType) {
      case 'major':
        return 'high';
      case 'minor':
        return 'medium';
      case 'patch':
        return 'low';
      default:
        return 'medium';
    }
  }
}

// 导出单例实例
export const versionManager = VersionManager.getInstance();
