/**
 * XUID切换助手 - 网络监控器 (TypeScript版本)
 * 负责监控网络请求以获取用户名信息
 */

// 类型定义
export interface UserInfo {
  xuid: string;
  username: string;
  assistantUid?: string;
}

export interface NetworkAssetInfo {
  assistantUid: string;
  name: string;
  kpAscriptionVal: number;
}

export interface ApiResponse {
  success: boolean;
  data?: any;
  error?: string;
  apiUrl?: string;
}

export interface AssetApiData {
  list?: NetworkAssetInfo[];
  data?: {
    list?: NetworkAssetInfo[];
  };
  result?: {
    list?: NetworkAssetInfo[];
  };
}

export type UserInfoDetectedCallback = (userInfo: UserInfo) => void;

export class XuidNetworkMonitor {
  private isMonitoring: boolean = false;
  private fullUserInfoCache: Map<string, UserInfo> = new Map(); // 完整用户信息缓存
  private onUserInfoDetected: UserInfoDetectedCallback | null = null;
  private isPageRefreshMonitoring: boolean = false;
  private refreshTimeoutId: number | null = null;
  private debugMode: boolean = true;
  private apiPath: string = '/assistantdesk/user/userinfo';
  private assetApiPath: string = '/assistantweb/userprofile/user/getbindassistantlist';

  // localStorage键名
  private readonly USER_INFO_STORAGE_KEY = 'xuid_user_info_cache';
  private readonly ASSET_INFO_STORAGE_KEY = 'xuid_asset_info_cache';

  // 资产信息缓存 - 分离状态信息和XUID管理的缓存
  private assetInfoCache: Map<string, string> = new Map(); // 状态信息用：精确匹配缓存
  private assetInfoListCache: Map<string, string> = new Map(); // XUID管理用：列表分析缓存

  constructor() {
    // 从localStorage加载缓存数据
    this.loadUserInfoFromStorage();
    this.loadAssetInfoFromStorage();
  }

  /**
   * 开始监控网络请求
   */
  startMonitoring(onUserInfoDetected: UserInfoDetectedCallback): void {
    if (this.isMonitoring) {
      this.log('网络监控已在运行中');
      return;
    }

    this.onUserInfoDetected = onUserInfoDetected;
    this.isMonitoring = true;

    // 启动页面刷新监控
    this.startPageRefreshMonitoring();

    this.log('网络监控已启动，支持所有域名');
    this.log('API路径:', this.apiPath);
  }

  /**
   * 停止监控网络请求
   */
  stopMonitoring(): void {
    if (!this.isMonitoring) {
      return;
    }

    this.isMonitoring = false;

    // 停止页面刷新监控
    this.stopPageRefreshMonitoring();

    console.log('网络监控已停止');
  }

  /**
   * 从页面提取用户信息（已废弃，使用直接API调用）
   */
  async extractUserInfoFromPage(tabId: number): Promise<void> {
    this.log('extractUserInfoFromPage已废弃，使用fetchUserInfoFromApi替代');
    // 直接调用API获取用户信息
    const userInfo = await this.fetchUserInfoFromApi(tabId);
    if (userInfo && userInfo.xuid && userInfo.username) {
      // 缓存完整用户信息
      this.setCachedUserInfo(userInfo.xuid, userInfo);

      // 通知主程序
      if (this.onUserInfoDetected) {
        this.onUserInfoDetected(userInfo);
      }

      console.log('检测到用户信息:', userInfo);
    }
  }

  /**
   * 在页面中执行的脚本，用于提取用户信息（已废弃）
   */
  private extractUserInfoScript(): () => UserInfo | null {
    // 此方法已废弃，不再使用页面脚本注入
    return function(): UserInfo | null {
      console.log('XUID扩展: extractUserInfoScript已废弃，请使用直接API调用');
      return null;
    };
  }

  /**
   * 获取当前标签页
   */
  async getCurrentTab(): Promise<any | null> {
    try {
      const [tab] = await browser.tabs.query({ active: true, currentWindow: true });

      if (tab) {
        this.log(`当前活动标签页: id=${tab.id}, url=${tab.url}, title=${tab.title}`);
      } else {
        this.log('❌ 无法获取当前活动标签页');
      }

      return tab;
    } catch (error) {
      this.log('获取当前标签页失败:', error);
      return null;
    }
  }

  /**
   * 手动检测当前页面的用户信息
   */
  async detectCurrentUserInfo(): Promise<UserInfo | null> {
    try {
      const tab = await this.getCurrentTab();
      if (!tab) {
        return null;
      }

      // 首先尝试从当前页面获取XUID
      const currentXuid = await this.getCurrentXuidFromTab(tab);
      if (currentXuid) {
        // 检查内存缓存
        let cachedUserInfo = this.fullUserInfoCache.get(currentXuid);

        // 如果内存缓存中没有，尝试从localStorage加载该XUID的缓存
        if (!cachedUserInfo) {
          cachedUserInfo = this.loadUserInfoFromStorageByXuid(currentXuid);
          if (cachedUserInfo) {
            // 将从localStorage加载的数据放入内存缓存
            this.fullUserInfoCache.set(currentXuid, cachedUserInfo);
            this.log('从localStorage加载特定XUID的用户信息:', cachedUserInfo);
          }
        }

        if (cachedUserInfo) {
          this.log('使用缓存的完整用户信息:', cachedUserInfo);
          return cachedUserInfo;
        }
      }

      this.log('缓存中无用户信息，开始API调用...');

      // 直接从API获取完整用户信息
      const apiUserInfo = await this.fetchUserInfoFromApi(tab.id!);
      if (apiUserInfo && apiUserInfo.xuid && apiUserInfo.username) {
        this.log('API获取用户信息成功:', apiUserInfo);
        // 缓存完整用户信息
        this.setCachedUserInfo(apiUserInfo.xuid, apiUserInfo);
        return apiUserInfo;
      }

      this.log('API获取用户信息失败');
      this.log('所有方式都无法获取用户信息');
      return null;
    } catch (error) {
      this.log('手动检测用户信息失败:', error);
      return null;
    }
  }

  /**
   * 从标签页获取当前XUID（使用扩展cookies API）
   */
  async getCurrentXuidFromTab(tab: any): Promise<string | null> {
    try {
      if (!tab.url) {
        this.log('标签页URL为空');
        return null;
      }

      // 尝试多个可能的XUID cookie名称
      const cookieNames = ['XUID', 'xuid', 'Xuid'];

      for (const cookieName of cookieNames) {
        try {
          const cookie = await browser.cookies.get({
            url: tab.url,
            name: cookieName
          });

          if (cookie && cookie.value) {
            this.log(`从cookie ${cookieName} 获取到XUID:`, cookie.value);
            return cookie.value;
          }
        } catch (cookieError) {
          this.log(`获取cookie ${cookieName} 失败:`, cookieError);
        }
      }

      this.log('未找到XUID cookie');
      return null;
    } catch (error) {
      this.log('获取XUID失败:', error);
      return null;
    }
  }

  /**
   * 获取缓存的用户名
   */
  getCachedUsername(xuid: string): string | null {
    const userInfo = this.fullUserInfoCache.get(xuid);
    return userInfo?.username || null;
  }

  /**
   * 获取缓存的完整用户信息
   */
  getCachedUserInfo(xuid: string): UserInfo | null {
    return this.fullUserInfoCache.get(xuid) || null;
  }

  /**
   * 设置完整用户信息缓存
   */
  setCachedUserInfo(xuid: string, userInfo: UserInfo): void {
    this.fullUserInfoCache.set(xuid, userInfo);
    // 保存到localStorage
    this.saveUserInfoToStorage();
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.fullUserInfoCache.clear();
    this.assetInfoCache.clear();
    this.assetInfoListCache.clear();
  }

  /**
   * 从localStorage加载用户信息缓存
   */
  private loadUserInfoFromStorage(): void {
    try {
      const stored = localStorage.getItem(this.USER_INFO_STORAGE_KEY);
      if (stored) {
        const userInfoMap = JSON.parse(stored);
        Object.entries(userInfoMap).forEach(([xuid, userInfo]) => {
          this.fullUserInfoCache.set(xuid, userInfo as UserInfo);
        });
        this.log('从localStorage加载用户信息缓存:', Object.keys(userInfoMap).length, '条记录');
      }
    } catch (error) {
      this.log('加载localStorage用户信息缓存失败:', error);
    }
  }

  /**
   * 从localStorage加载特定XUID的用户信息缓存
   */
  private loadUserInfoFromStorageByXuid(xuid: string): UserInfo | null {
    try {
      const stored = localStorage.getItem(this.USER_INFO_STORAGE_KEY);
      if (stored) {
        const userInfoMap = JSON.parse(stored);
        const userInfo = userInfoMap[xuid];
        if (userInfo) {
          this.log('从localStorage加载特定XUID用户信息:', { xuid, userInfo });
          return userInfo as UserInfo;
        }
      }
      return null;
    } catch (error) {
      this.log('从localStorage加载特定XUID用户信息失败:', error);
      return null;
    }
  }

  /**
   * 从localStorage加载特定缓存键的资产信息
   */
  private loadAssetInfoFromStorageByKey(cacheKey: string): string | null {
    try {
      const stored = localStorage.getItem(this.ASSET_INFO_STORAGE_KEY);
      if (stored) {
        const assetInfoMap = JSON.parse(stored);
        const assetInfo = assetInfoMap[cacheKey];
        if (assetInfo) {
          this.log('从localStorage加载特定资产信息:', { cacheKey, assetInfo });
          return assetInfo as string;
        }
      }
      return null;
    } catch (error) {
      this.log('从localStorage加载特定资产信息失败:', error);
      return null;
    }
  }

  /**
   * 保存用户信息缓存到localStorage
   */
  private saveUserInfoToStorage(): void {
    try {
      const userInfoMap: Record<string, UserInfo> = {};
      this.fullUserInfoCache.forEach((userInfo, xuid) => {
        userInfoMap[xuid] = userInfo;
      });
      localStorage.setItem(this.USER_INFO_STORAGE_KEY, JSON.stringify(userInfoMap));
      this.log('保存用户信息缓存到localStorage:', Object.keys(userInfoMap).length, '条记录');
    } catch (error) {
      this.log('保存localStorage用户信息缓存失败:', error);
    }
  }

  /**
   * 从localStorage加载资产信息缓存
   */
  private loadAssetInfoFromStorage(): void {
    try {
      const stored = localStorage.getItem(this.ASSET_INFO_STORAGE_KEY);
      if (stored) {
        const assetInfoMap = JSON.parse(stored);
        Object.entries(assetInfoMap).forEach(([key, assetInfo]) => {
          this.assetInfoCache.set(key, assetInfo as string);
        });
        this.log('从localStorage加载资产信息缓存:', Object.keys(assetInfoMap).length, '条记录');
      }
    } catch (error) {
      this.log('加载localStorage资产信息缓存失败:', error);
    }
  }

  /**
   * 保存资产信息缓存到localStorage
   */
  private saveAssetInfoToStorage(): void {
    try {
      const assetInfoMap: Record<string, string> = {};
      this.assetInfoCache.forEach((assetInfo, key) => {
        assetInfoMap[key] = assetInfo;
      });
      localStorage.setItem(this.ASSET_INFO_STORAGE_KEY, JSON.stringify(assetInfoMap));
      this.log('保存资产信息缓存到localStorage:', Object.keys(assetInfoMap).length, '条记录');
    } catch (error) {
      this.log('保存localStorage资产信息缓存失败:', error);
    }
  }

  /**
   * 获取缓存的资产信息（状态信息用：精确匹配）
   */
  getCachedAssetInfo(xuid: string): string | null {
    const cacheKey = `${xuid}_precise`;
    return this.assetInfoCache.get(cacheKey) || null;
  }

  /**
   * 设置资产信息缓存（状态信息用：精确匹配）
   */
  setCachedAssetInfo(xuid: string, assetInfo: string): void {
    const cacheKey = `${xuid}_precise`;
    this.assetInfoCache.set(cacheKey, assetInfo);
    // 保存到localStorage
    this.saveAssetInfoToStorage();
  }

  /**
   * 通过缓存键获取资产信息
   */
  getAssetInfoFromCache(cacheKey: string): string | null {
    // 首先检查内存缓存
    let cachedAssetInfo = this.assetInfoCache.get(cacheKey);

    // 如果内存缓存中没有，尝试从localStorage加载
    if (!cachedAssetInfo) {
      cachedAssetInfo = this.loadAssetInfoFromStorageByKey(cacheKey);
      if (cachedAssetInfo) {
        // 将从localStorage加载的数据放入内存缓存
        this.assetInfoCache.set(cacheKey, cachedAssetInfo);
        this.log('从localStorage加载特定资产信息:', { cacheKey, assetInfo: cachedAssetInfo });
      }
    }

    return cachedAssetInfo || null;
  }

  /**
   * 获取缓存的资产信息（XUID管理用：列表分析）
   */
  getCachedAssetInfoForList(xuid: string): string | null {
    const cacheKey = `${xuid}_list`;
    const cached = this.assetInfoListCache.get(cacheKey);
    if (cached) {
      return cached;
    }

    // 如果缓存中没有，尝试从存储中获取
    this.getAssetInfoFromStorage(xuid);
    return null;
  }

  /**
   * 从存储中异步获取资产信息并设置到缓存
   */
  private async getAssetInfoFromStorage(xuid: string): Promise<void> {
    try {
      const result = await browser.storage.local.get(['assetInfoList']);
      if (result.assetInfoList && result.assetInfoList[xuid]) {
        const assetInfo = result.assetInfoList[xuid];
        const assetType = typeof assetInfo === 'string' ? assetInfo : assetInfo.type;
        if (assetType) {
          const cacheKey = `${xuid}_list`;
          this.assetInfoListCache.set(cacheKey, assetType);
          this.log('从存储中获取到资产信息:', { xuid, assetType });
        }
      }
    } catch (error) {
      this.log('从存储中获取资产信息失败:', error);
    }
  }

  /**
   * 设置资产信息缓存（XUID管理用：列表分析）
   */
  setCachedAssetInfoForList(xuid: string, assetInfo: string): void {
    const cacheKey = `${xuid}_list`;
    this.assetInfoListCache.set(cacheKey, assetInfo);
  }

  /**
   * 获取资产信息 - 状态信息用（精确匹配）
   */
  async fetchAssetInfoForStatus(assistantUid: string): Promise<string | null> {
    try {
      this.log('获取状态信息资产数据，assistantUid:', assistantUid);

      if (!assistantUid) {
        this.log('assistantUid为空，跳过资产信息获取');
        return null;
      }

      // 检查缓存
      const cacheKey = `${assistantUid}_precise`;
      const cached = this.assetInfoCache.get(cacheKey);
      if (cached) {
        this.log('使用缓存的状态信息资产数据:', cached);
        return cached;
      }

      // 调用资产API
      const assetList = await this.fetchAssetListFromApi();
      if (!assetList || !Array.isArray(assetList)) {
        this.log('资产API调用失败或返回数据无效');
        return null;
      }

      // 精确匹配assistantUid
      const matchedAsset = assetList.find(asset => asset.assistantUid == assistantUid);
      if (!matchedAsset) {
        this.log('未找到匹配的资产信息，assistantUid:', assistantUid);
        return null;
      }

      // 映射资产类型
      const assetType = this.mapAssetType(matchedAsset.kpAscriptionVal);
      this.log('匹配到资产信息:', { assistantUid, assetType, kpAscriptionVal: matchedAsset.kpAscriptionVal });

      // 缓存结果
      this.assetInfoCache.set(cacheKey, assetType);
      // 保存到localStorage
      this.saveAssetInfoToStorage();
      return assetType;

    } catch (error) {
      this.log('获取状态信息资产数据失败:', error);
      return null;
    }
  }

  /**
   * 获取资产信息 - XUID管理用（列表分析）
   */
  async fetchAssetInfoForList(xuid: string): Promise<string | null> {
    try {
      console.log('[FETCH-ASSET-LIST] ========== 开始获取XUID管理资产数据 ==========');
      console.log('[FETCH-ASSET-LIST] 目标XUID:', xuid);

      if (!xuid) {
        console.log('[FETCH-ASSET-LIST] xuid为空，跳过资产信息获取');
        return null;
      }

      // 检查缓存
      const cacheKey = `${xuid}_list`;
      const cached = this.assetInfoListCache.get(cacheKey);
      if (cached) {
        console.log('[FETCH-ASSET-LIST] 使用缓存的XUID管理资产数据:', cached);
        return cached;
      }

      // 检查这是否是当前XUID，只有当前XUID才能获取资产列表
      const currentTab = await this.getCurrentTab();
      console.log('[FETCH-ASSET-LIST] 获取当前标签页:', currentTab?.url);

      if (!currentTab) {
        console.log('[FETCH-ASSET-LIST] 无法获取当前标签页');
        return null;
      }

      const currentXuid = await this.getCurrentXuidFromTab(currentTab);
      console.log('[FETCH-ASSET-LIST] 当前页面XUID:', currentXuid);
      console.log('[FETCH-ASSET-LIST] 目标XUID:', xuid);
      console.log('[FETCH-ASSET-LIST] XUID匹配:', currentXuid === xuid);

      if (currentXuid !== xuid) {
        console.log('[FETCH-ASSET-LIST] 目标XUID与当前XUID不匹配，无法获取资产信息');
        return null;
      }

      console.log('[FETCH-ASSET-LIST] 这是当前XUID，可以获取资产列表');

      // 调用资产API
      console.log('[FETCH-ASSET-LIST] 调用资产API...');
      const assetList = await this.fetchAssetListFromApi();
      console.log('[FETCH-ASSET-LIST] 资产API返回结果:', assetList);

      if (!assetList || !Array.isArray(assetList)) {
        console.log('[FETCH-ASSET-LIST] 资产API调用失败或返回数据无效');
        return null;
      }

      // 分析当前用户的所有资产（资产列表API返回的就是当前用户的所有绑定资产）
      console.log('[FETCH-ASSET-LIST] 分析当前用户的所有资产');
      console.log('[FETCH-ASSET-LIST] 资产列表总数:', assetList.length);
      console.log('[FETCH-ASSET-LIST] 资产列表详情:', assetList.map(asset => ({
        assistantUid: asset.assistantUid,
        name: asset.name,
        kpAscriptionVal: asset.kpAscriptionVal
      })));

      if (assetList.length === 0) {
        console.log('[FETCH-ASSET-LIST] 当前用户没有绑定任何资产');
        return null;
      }

      // 分析该用户的所有资产类型
      console.log('[FETCH-ASSET-LIST] 开始分析资产类型...');
      const assetTypes = new Set<string>();
      assetList.forEach(asset => {
        console.log('[FETCH-ASSET-LIST] 处理资产:', { name: asset.name, kpAscriptionVal: asset.kpAscriptionVal });
        if (asset.kpAscriptionVal === 1) {
          assetTypes.add('辅导');
          console.log('[FETCH-ASSET-LIST] 添加资产类型: 辅导');
        } else if (asset.kpAscriptionVal === 4) {
          assetTypes.add('督学');
          console.log('[FETCH-ASSET-LIST] 添加资产类型: 督学');
        } else {
          console.log('[FETCH-ASSET-LIST] 未知资产类型:', asset.kpAscriptionVal);
        }
      });

      console.log('[FETCH-ASSET-LIST] 收集到的资产类型:', Array.from(assetTypes));

      // 格式化显示
      let displayText = '';
      if (assetTypes.has('督学') && assetTypes.has('辅导')) {
        displayText = '辅/督';
        console.log('[FETCH-ASSET-LIST] 同时拥有督学和辅导，显示: 辅/督');
      } else if (assetTypes.has('督学')) {
        displayText = '督';
        console.log('[FETCH-ASSET-LIST] 只有督学，显示: 督');
      } else if (assetTypes.has('辅导')) {
        displayText = '辅';
        console.log('[FETCH-ASSET-LIST] 只有辅导，显示: 辅');
      } else {
        console.log('[FETCH-ASSET-LIST] 没有识别到有效的资产类型');
      }

      console.log('[FETCH-ASSET-LIST] 最终显示文本:', displayText);
      console.log('[FETCH-ASSET-LIST] 分析结果汇总:', { xuid, displayText, assetTypes: Array.from(assetTypes), totalAssets: assetList.length });

      // 缓存结果
      this.assetInfoListCache.set(cacheKey, displayText);
      console.log('[FETCH-ASSET-LIST] ========== 获取XUID管理资产数据完成 ==========');
      return displayText;

    } catch (error) {
      this.log('获取XUID管理资产数据失败:', error);
      return null;
    }
  }

  /**
   * 获取XUID对应的assistantUid
   */
  private async getAssistantUidForXuid(xuid: string): Promise<string | null> {
    try {
      this.log('获取XUID对应的assistantUid:', xuid);

      // 方法1: 检查当前API用户信息缓存
      // 如果当前XUID就是要查询的XUID，直接使用当前的assistantUid
      const currentTab = await this.getCurrentTab();
      if (currentTab) {
        const currentXuid = await this.getCurrentXuidFromTab(currentTab);
        if (currentXuid === xuid) {
          // 尝试从页面获取用户信息
          const userInfo = await this.fetchUserInfoFromApi(currentTab.id!);
          if (userInfo && userInfo.assistantUid) {
            this.log('从当前页面获取到assistantUid:', userInfo.assistantUid);
            // 缓存完整用户信息
            if (userInfo.xuid && userInfo.username) {
              this.setCachedUserInfo(userInfo.xuid, userInfo);
            }
            return userInfo.assistantUid;
          }
        }
      }

      // 方法2: 从存储的XUID记录中查找assistantUid
      try {
        const result = await browser.storage.local.get(['xuids']);
        if (result.xuids) {
          for (const [domain, xuids] of Object.entries(result.xuids)) {
            const xuidRecord = (xuids as any[]).find(item => {
              const value = typeof item === 'string' ? item : item.value;
              return value === xuid;
            });
            if (xuidRecord && typeof xuidRecord === 'object' && xuidRecord.assistantUid) {
              this.log('从存储记录中获取到assistantUid:', xuidRecord.assistantUid);
              return xuidRecord.assistantUid;
            }
          }
        }
      } catch (storageError) {
        this.log('从存储中查找assistantUid失败:', storageError);
      }

      this.log('无法获取XUID对应的assistantUid');
      return null;

    } catch (error) {
      this.log('获取XUID对应的assistantUid失败:', error);
      return null;
    }
  }

  /**
   * 映射资产类型枚举值到显示文本
   */
  private mapAssetType(kpAscriptionVal: number): string {
    switch (kpAscriptionVal) {
      case 1:
        return '辅导';
      case 4:
        return '督学';
      default:
        this.log('未知的资产类型枚举值:', kpAscriptionVal);
        return '';
    }
  }

  /**
   * 调用资产列表API（使用扩展直接调用）
   */
  async fetchAssetListFromApi(): Promise<NetworkAssetInfo[] | null> {
    try {
      console.log('[ASSET-API] ========== 开始调用资产列表API ==========');

      const currentTab = await this.getCurrentTab();
      if (!currentTab) {
        console.log('[ASSET-API] 无法获取当前标签页');
        return null;
      }

      console.log('[ASSET-API] 当前标签页URL:', currentTab.url);

      // 检查是否是目标域名
      if (!this.isTargetDomain(currentTab.url!)) {
        console.log('[ASSET-API] 非目标域名，跳过资产API调用:', currentTab.url);
        return null;
      }

      // 构建资产API URL
      const apiUrl = this.buildAssetApiUrl(currentTab.url!);
      if (!apiUrl) {
        console.log('[ASSET-API] 无法构建资产API URL');
        return null;
      }

      console.log('[ASSET-API] 调用资产API:', apiUrl);

      // 获取当前页面的cookies
      const cookies = await this.getCookiesForUrl(currentTab.url!);
      const cookieHeader = this.buildCookieHeader(cookies);

      // 直接调用API
      console.log('[ASSET-API] 扩展直接调用API...');
      const response = await fetch(apiUrl, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'Cookie': cookieHeader
        }
      });

      if (!response.ok) {
        console.log('[ASSET-API] API调用失败:', response.status, response.statusText);
        return null;
      }

      const apiData = await response.json();
      console.log('[ASSET-API] API响应数据:', apiData);

      // 解析资产列表
      console.log('[ASSET-API] 开始解析API数据...');
      const assetList = this.parseAssetListFromApi(apiData);
      console.log('[ASSET-API] 解析得到的资产列表:', assetList);
      console.log('[ASSET-API] ========== 资产列表API调用完成 ==========');

      return assetList;

    } catch (error) {
      this.log('调用资产列表API失败:', error);
      return null;
    }
  }

  /**
   * 构建资产API URL
   */
  private buildAssetApiUrl(tabUrl: string): string | null {
    try {
      const url = new URL(tabUrl);
      const hostname = url.hostname;
      const protocol = url.protocol;
      const port = url.port;

      let apiUrl: string;
      if (port && port !== '80' && port !== '443') {
        apiUrl = `${protocol}//${hostname}:${port}${this.assetApiPath}`;
      } else {
        apiUrl = `${protocol}//${hostname}${this.assetApiPath}`;
      }

      this.log(`构建的资产API URL: ${apiUrl}`);
      return apiUrl;
    } catch (error) {
      this.log('构建资产API URL失败:', error);
      return null;
    }
  }

  /**
   * 获取指定URL的所有cookies
   */
  private async getCookiesForUrl(url: string): Promise<any[]> {
    try {
      const cookies = await browser.cookies.getAll({ url });
      this.log(`获取到 ${cookies.length} 个cookies`);
      return cookies;
    } catch (error) {
      this.log('获取cookies失败:', error);
      return [];
    }
  }

  /**
   * 构建Cookie请求头
   */
  private buildCookieHeader(cookies: any[]): string {
    return cookies.map(cookie => `${cookie.name}=${cookie.value}`).join('; ');
  }

  /**
   * 构建用户信息API URL
   */
  private buildUserInfoApiUrl(tabUrl: string): string | null {
    try {
      const url = new URL(tabUrl);
      const hostname = url.hostname;
      const protocol = url.protocol;
      const port = url.port;

      let apiUrl: string;
      if (port && port !== '80' && port !== '443') {
        apiUrl = `${protocol}//${hostname}:${port}${this.apiPath}`;
      } else {
        apiUrl = `${protocol}//${hostname}${this.apiPath}`;
      }

      this.log(`构建的用户信息API URL: ${apiUrl}`);
      return apiUrl;
    } catch (error) {
      this.log('构建用户信息API URL失败:', error);
      return null;
    }
  }

  /**
   * 在页面中执行的资产API调用脚本（已废弃）
   */
  private fetchAssetListScript(): (apiUrl: string) => Promise<ApiResponse> {
    // 此方法已废弃，不再使用页面脚本注入
    return function(apiUrl: string): Promise<ApiResponse> {
      console.log('XUID扩展: fetchAssetListScript已废弃，请使用扩展直接API调用');
      return Promise.resolve({
        success: false,
        error: '方法已废弃'
      });
    };
  }

  /**
   * 解析资产API响应数据
   */
  parseAssetListFromApi(apiData: AssetApiData): NetworkAssetInfo[] | null {
    try {
      this.log('解析资产API数据:', apiData);

      // 尝试多种数据结构
      let assetList: NetworkAssetInfo[] | null = null;

      // 方式1: 直接从根级别的list字段获取
      if (apiData.list && Array.isArray(apiData.list)) {
        assetList = apiData.list;
        this.log('从根级别list字段获取到资产数据');
      }
      // 方式2: 从data.list字段获取
      else if (apiData.data && apiData.data.list && Array.isArray(apiData.data.list)) {
        assetList = apiData.data.list;
        this.log('从data.list字段获取到资产数据');
      }
      // 方式3: 从result.list字段获取
      else if (apiData.result && apiData.result.list && Array.isArray(apiData.result.list)) {
        assetList = apiData.result.list;
        this.log('从result.list字段获取到资产数据');
      }

      if (!assetList || !Array.isArray(assetList)) {
        this.log('资产API响应中未找到有效的list数组，完整数据:', JSON.stringify(apiData, null, 2));
        return null;
      }

      // 验证数据结构
      const validAssets = assetList.filter(asset =>
        asset &&
        asset.assistantUid &&
        asset.name &&
        typeof asset.kpAscriptionVal === 'number'
      );

      this.log(`资产列表验证结果: 总数=${assetList.length}, 有效数=${validAssets.length}`);

      return validAssets;

    } catch (error) {
      this.log('解析资产API数据失败:', error);
      return null;
    }
  }

  /**
   * 检查是否是目标域名
   */
  private isTargetDomain(url: string): boolean {
    try {
      const urlObj = new URL(url);
      const hostname = urlObj.hostname;

      // 支持zuoyebang.cc和suanshubang.cc域名
      return hostname.includes('zuoyebang.cc') || hostname.includes('suanshubang.cc');
    } catch (error) {
      this.log('URL解析失败:', error);
      return false;
    }
  }

  /**
   * 启动页面刷新监控
   */
  private startPageRefreshMonitoring(): void {
    if (this.isPageRefreshMonitoring) {
      this.log('页面刷新监控已在运行中');
      return;
    }

    this.isPageRefreshMonitoring = true;

    // 监听标签页更新事件
    if (browser.tabs && browser.tabs.onUpdated) {
      browser.tabs.onUpdated.addListener(this.handleTabUpdated.bind(this));
      this.log('tabs.onUpdated监听已启动');
    } else {
      this.log('tabs权限不可用');
    }

    this.log('页面刷新监控已启动');
  }

  /**
   * 停止页面刷新监控
   */
  private stopPageRefreshMonitoring(): void {
    if (!this.isPageRefreshMonitoring) {
      return;
    }

    this.isPageRefreshMonitoring = false;

    if (browser.tabs && browser.tabs.onUpdated) {
      browser.tabs.onUpdated.removeListener(this.handleTabUpdated.bind(this));
    }

    // 清除延时器
    if (this.refreshTimeoutId) {
      clearTimeout(this.refreshTimeoutId);
      this.refreshTimeoutId = null;
    }

    console.log('页面刷新监控已停止');
  }

  /**
   * 处理标签页更新事件
   */
  private async handleTabUpdated(tabId: number, changeInfo: any, tab: any): Promise<void> {
    try {
      this.log(`标签页更新事件: tabId=${tabId}, status=${changeInfo.status}, url=${tab.url}`);

      // 只处理页面加载完成的事件
      if (changeInfo.status !== 'complete') {
        return;
      }

      // 检查是否是目标域名
      if (!tab.url || !this.isTargetDomain(tab.url)) {
        this.log('非目标域名，跳过监控:', tab.url);
        return;
      }

      // 获取当前活动标签页
      const currentTab = await this.getCurrentTab();
      if (!currentTab || currentTab.id !== tabId) {
        this.log('不是当前活动标签页，跳过监控');
        return;
      }

      this.log('检测到目标页面加载完成，开始延时检测用户信息');

      // 清除之前的延时器
      if (this.refreshTimeoutId) {
        clearTimeout(this.refreshTimeoutId);
      }

      // 延时检测用户信息（等待页面完全加载和API调用完成）
      this.refreshTimeoutId = window.setTimeout(async () => {
        try {
          this.log('开始从API获取用户信息...');
          const userInfo = await this.fetchUserInfoFromApi(tabId);
          if (userInfo && userInfo.xuid && userInfo.username) {
            // 缓存完整用户信息
            this.setCachedUserInfo(userInfo.xuid, userInfo);

            // 通知主程序 - 传递完整的用户信息
            if (this.onUserInfoDetected) {
              this.onUserInfoDetected(userInfo);
            }

            this.log('检测到用户信息:', userInfo);
          } else {
            this.log('API获取用户信息失败');
          }
        } catch (error) {
          this.log('延时检测用户信息失败:', error);
        }
      }, 2000); // 延时2秒

    } catch (error) {
      this.log('处理标签页更新事件失败:', error);
    }
  }

  /**
   * 批量获取用户名信息
   */
  async batchFetchUsernames(xuids: string[]): Promise<Map<string, string>> {
    const usernameMap = new Map<string, string>();

    try {
      this.log('开始批量获取用户名信息，XUID数量:', xuids.length);

      // 检查缓存
      const uncachedXuids: string[] = [];
      xuids.forEach(xuid => {
        const cachedUsername = this.getCachedUsername(xuid);
        if (cachedUsername) {
          usernameMap.set(xuid, cachedUsername);
        } else {
          uncachedXuids.push(xuid);
        }
      });

      this.log(`缓存命中: ${usernameMap.size}, 需要获取: ${uncachedXuids.length}`);

      if (uncachedXuids.length === 0) {
        return usernameMap;
      }

      // 尝试从当前页面获取用户信息
      const currentUserInfo = await this.detectCurrentUserInfo();
      if (currentUserInfo && uncachedXuids.includes(currentUserInfo.xuid)) {
        usernameMap.set(currentUserInfo.xuid, currentUserInfo.username);
        this.setCachedUserInfo(currentUserInfo.xuid, currentUserInfo);
      }

      this.log('批量获取用户名完成，总获取数量:', usernameMap.size);
      return usernameMap;

    } catch (error) {
      this.log('批量获取用户名失败:', error);
      return usernameMap;
    }
  }

  /**
   * 批量获取资产信息
   */
  async batchFetchAssetInfo(xuids: string[]): Promise<Map<string, string>> {
    const assetInfoMap = new Map<string, string>();

    try {
      this.log('开始批量获取资产信息，XUID数量:', xuids.length);

      for (const xuid of xuids) {
        const assetInfo = await this.fetchAssetInfoForList(xuid);
        if (assetInfo) {
          assetInfoMap.set(xuid, assetInfo);
        }
      }

      this.log('批量获取资产信息完成，获取数量:', assetInfoMap.size);
      return assetInfoMap;

    } catch (error) {
      this.log('批量获取资产信息失败:', error);
      return assetInfoMap;
    }
  }

  /**
   * 从API获取用户信息（使用扩展直接调用）
   */
  async fetchUserInfoFromApi(tabId: number): Promise<UserInfo | null> {
    try {
      this.log('开始从API获取用户信息，tabId:', tabId);

      // 获取当前标签页
      const currentTab = await this.getCurrentTab();
      if (!currentTab || !currentTab.url) {
        this.log('无法获取当前标签页或URL');
        return null;
      }

      // 构建用户信息API URL
      const apiUrl = this.buildUserInfoApiUrl(currentTab.url);
      if (!apiUrl) {
        this.log('无法构建用户信息API URL');
        return null;
      }

      this.log('调用用户信息API:', apiUrl);

      // 获取当前页面的cookies
      const cookies = await this.getCookiesForUrl(currentTab.url);
      const cookieHeader = this.buildCookieHeader(cookies);

      // 直接调用API
      const response = await fetch(apiUrl, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'Cookie': cookieHeader
        }
      });

      if (!response.ok) {
        this.log('用户信息API调用失败:', response.status, response.statusText);
        return null;
      }

      const apiData = await response.json();
      this.log('用户信息API响应数据:', apiData);

      // 解析用户信息
      const userInfo = this.parseUserInfoFromApi(apiData);
      this.log('解析的用户信息:', userInfo);

      return userInfo;

    } catch (error) {
      this.log('从API获取用户信息失败:', error);
      return null;
    }
  }

  /**
   * 检测用户信息API URL（已废弃）
   */
  async detectUserInfoApiUrl(tabId: number): Promise<string | null> {
    this.log('detectUserInfoApiUrl已废弃，使用buildUserInfoApiUrl替代');
    return null;
  }

  /**
   * 在页面中执行的用户信息API调用脚本（已废弃）
   */
  private fetchUserInfoScript(): (apiUrl: string) => Promise<ApiResponse> {
    // 此方法已废弃，不再使用页面脚本注入
    return function(apiUrl: string): Promise<ApiResponse> {
      console.log('XUID扩展: fetchUserInfoScript已废弃，请使用扩展直接API调用');
      return Promise.resolve({
        success: false,
        error: '方法已废弃'
      });
    };
  }

  /**
   * 解析用户信息API响应数据
   */
  parseUserInfoFromApi(apiData: any): UserInfo | null {
    try {
      this.log('开始解析用户信息API数据:', apiData);

      // 尝试多种数据结构
      let personUid: string | undefined, uname: string | undefined, assistantUid: string | undefined;

      // 方式1: 直接从根级别获取
      if (apiData.personUid && apiData.uname) {
        personUid = apiData.personUid;
        uname = apiData.uname;
        assistantUid = apiData.assistantUid;
        this.log('从根级别获取到数据');
      }
      // 方式2: 从data字段获取
      else if (apiData.data && apiData.data.personUid && apiData.data.uname) {
        personUid = apiData.data.personUid;
        uname = apiData.data.uname;
        assistantUid = apiData.data.assistantUid;
        this.log('从data字段获取到数据');
      }
      // 方式3: 从result字段获取
      else if (apiData.result && apiData.result.personUid && apiData.result.uname) {
        personUid = apiData.result.personUid;
        uname = apiData.result.uname;
        assistantUid = apiData.result.assistantUid;
        this.log('从result字段获取到数据');
      }

      if (!personUid || !uname) {
        this.log('用户信息API响应中缺少必要字段，完整数据:', JSON.stringify(apiData, null, 2));
        return null;
      }

      const result: UserInfo = {
        xuid: String(personUid),
        username: String(uname),
        assistantUid: assistantUid ? String(assistantUid) : undefined
      };

      this.log('解析结果:', result);
      return result;
    } catch (error) {
      this.log('解析用户信息API数据失败:', error);
      return null;
    }
  }

  /**
   * 调试日志输出
   */
  private log(...args: any[]): void {
    if (this.debugMode) {
      console.log('[NETWORK-MONITOR]', ...args);
    }
  }
}
