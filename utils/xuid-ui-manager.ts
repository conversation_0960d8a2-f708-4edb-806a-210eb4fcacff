/**
 * XUID切换助手 - UI管理器 (TypeScript版本)
 * 负责界面渲染和DOM操作
 * 排除小工具、任务列表、方舟课程相关UI逻辑
 */

import { XuidStorageManager, XuidData, XuidRecord, GroupedXuidData } from './xuid-storage-manager';
import { XuidNetworkMonitor } from './xuid-network-monitor';
import { notificationManager } from './notification-manager';

// 类型定义
export interface ClusterSwitchHandler {
  (cluster: 'tips' | 'small' | 'stable' | 'online'): void;
}

export interface XuidSwitchHandler {
  (domain: string, xuidValue: string): void;
}

export interface XuidDeleteHandler {
  (domain: string, xuidValue: string): void;
}

export class XuidUIManager {
  private selectedXuids: string[] = [];
  private currentDomain: string = '';
  private currentXuid: string = '';
  private actionsCollapsed: boolean = true;
  private collapseInitialized: boolean = false;

  // 事件处理器
  private onClusterSwitch?: ClusterSwitchHandler;
  private onXuidSwitch?: XuidSwitchHandler;
  private onXuidDelete?: XuidDeleteHandler;

  constructor(
    private networkMonitor?: XuidNetworkMonitor
  ) {}

  /**
   * 设置事件处理器
   */
  setEventHandlers(handlers: {
    onClusterSwitch?: ClusterSwitchHandler;
    onXuidSwitch?: XuidSwitchHandler;
    onXuidDelete?: XuidDeleteHandler;
  }): void {
    this.onClusterSwitch = handlers.onClusterSwitch;
    this.onXuidSwitch = handlers.onXuidSwitch;
    this.onXuidDelete = handlers.onXuidDelete;
  }

  /**
   * 更新当前状态显示
   */
  updateCurrentStatus(
    domain: string, 
    xuid: string, 
    clusterName?: string | null, 
    userName?: string | null, 
    assistantUid?: string | null
  ): void {
    this.currentDomain = domain;
    this.currentXuid = xuid;

    const xuidElement = document.getElementById('currentXuid');
    const userNameElement = document.getElementById('currentUserName');
    const clusterStatusElement = document.getElementById('clusterStatus');
    const assetTypeElement = document.getElementById('currentAssetType');
    const assistantUidElement = document.getElementById('currentAssistantUid');

    // 更新真人ID显示
    if (xuidElement) {
      xuidElement.textContent = xuid || '未找到';
      // 移除之前的事件监听器
      const newXuidElement = xuidElement.cloneNode(true) as HTMLElement;
      xuidElement.parentNode?.replaceChild(newXuidElement, xuidElement);

      // 添加点击复制功能
      if (xuid) {
        newXuidElement.addEventListener('click', () => {
          this.copyXuidToClipboard(xuid);
        });
      }
    }

    // 更新用户名显示
    if (userNameElement) {
      const displayName = userName || '未知用户';
      userNameElement.textContent = displayName;
      userNameElement.title = displayName;

      // 设置用户名颜色（基于资产类型）
      const assetType = (window as any).currentAssetType;
      if (assetType) {
        userNameElement.setAttribute('data-asset-type', assetType);
      } else {
        userNameElement.removeAttribute('data-asset-type');
      }
    }

    // 更新资产类型显示
    if (assetTypeElement) {
      const assetType = (window as any).currentAssetType;
      if (assetType) {
        assetTypeElement.textContent = assetType;
        assetTypeElement.setAttribute('data-asset-type', assetType);

        // 设置资产类型颜色
        assetTypeElement.classList.remove('asset-supervisor', 'asset-tutor', 'asset-both');
        const assetTypeStr = String(assetType || '');
        if (assetTypeStr === '督学') {
          assetTypeElement.classList.add('asset-supervisor');
        } else if (assetTypeStr === '辅导') {
          assetTypeElement.classList.add('asset-tutor');
        } else if (assetTypeStr.includes('督') && assetTypeStr.includes('辅')) {
          assetTypeElement.classList.add('asset-both');
        }
      } else {
        assetTypeElement.textContent = '-';
        assetTypeElement.removeAttribute('data-asset-type');
        assetTypeElement.classList.remove('asset-supervisor', 'asset-tutor', 'asset-both');
      }
    }

    // 更新资产ID显示
    if (assistantUidElement) {
      if (assistantUid) {
        assistantUidElement.textContent = assistantUid;
        assistantUidElement.title = `资产ID: ${assistantUid}`;

        // 添加点击复制功能
        assistantUidElement.style.cursor = 'pointer';
        assistantUidElement.onclick = () => {
          this.copyAssistantUidToClipboard(assistantUid);
        };
      } else {
        assistantUidElement.textContent = '-';
        assistantUidElement.title = '资产ID未获取';
        assistantUidElement.style.cursor = 'default';
        assistantUidElement.onclick = null;
      }
    }

    // 更新集群状态显示
    if (clusterStatusElement && clusterName !== null && clusterName !== undefined) {
      const clusterDisplayNames: Record<string, string> = {
        'tips': 'Tips',
        'small': 'Small',
        'stable': 'Stable',
        'online': 'Online',
        'unknown': '未知'
      };

      const displayName = clusterDisplayNames[clusterName] || clusterName;
      clusterStatusElement.textContent = displayName;

      // 移除旧的状态类
      clusterStatusElement.classList.remove('enabled', 'disabled', 'detecting', 'error', 'tips', 'small', 'stable', 'online', 'unknown');
      // 添加新的状态类
      clusterStatusElement.classList.add(clusterName);

      // 更新集群切换按钮的显示
      this.updateClusterSwitchButton(clusterName);
    }
  }

  /**
   * 更新集群切换按钮显示
   */
  updateClusterSwitchButton(clusterName: string): void {
    const clusterSwitchBtn = document.getElementById('clusterSwitchBtn');
    const clusterSwitchText = document.getElementById('clusterSwitchText');

    if (clusterSwitchBtn && clusterSwitchText) {
      const clusterDisplayNames: Record<string, string> = {
        'tips': 'Tips',
        'small': 'Small',
        'stable': 'Stable',
        'online': 'Online',
        'unknown': '未知'
      };

      const clusterColors: Record<string, string> = {
        'tips': '#ff9800',
        'small': '#28a745',
        'stable': '#007bff',
        'online': '#6c757d',
        'unknown': '#6c757d'
      };

      const displayName = clusterDisplayNames[clusterName] || clusterName;
      const color = clusterColors[clusterName] || '#6c757d';

      clusterSwitchText.textContent = displayName;
      (clusterSwitchBtn as HTMLElement).style.backgroundColor = color;

      // 更新下拉菜单中的活跃状态
      this.updateClusterDropdownActive(clusterName);
    }
  }

  /**
   * 更新集群下拉菜单中的活跃状态
   */
  updateClusterDropdownActive(activeCluster: string): void {
    const clusterOptions = document.querySelectorAll('.xuid-cluster-option');
    clusterOptions.forEach(option => {
      const cluster = option.getAttribute('data-cluster');
      if (cluster === activeCluster) {
        option.classList.add('active');
      } else {
        option.classList.remove('active');
      }
    });
  }

  /**
   * 复制XUID到剪贴板
   */
  private async copyXuidToClipboard(xuid: string): Promise<void> {
    try {
      await navigator.clipboard.writeText(xuid);
      this.showMessage('XUID已复制到剪贴板', 'success');
    } catch (error) {
      console.error('复制XUID失败:', error);
      this.showMessage('复制失败', 'error');
    }
  }

  /**
   * 复制资产ID到剪贴板
   */
  private async copyAssistantUidToClipboard(assistantUid: string): Promise<void> {
    try {
      await navigator.clipboard.writeText(assistantUid);
      this.showMessage('资产ID已复制到剪贴板', 'success');
    } catch (error) {
      console.error('复制资产ID失败:', error);
      this.showMessage('复制失败', 'error');
    }
  }

  /**
   * 初始化集群下拉菜单
   */
  initClusterDropdown(): void {
    const clusterSwitchBtn = document.getElementById('clusterSwitchBtn');
    const clusterDropdown = document.getElementById('clusterDropdown');
    const clusterOptions = document.querySelectorAll('.xuid-cluster-option');

    if (!clusterSwitchBtn || !clusterDropdown) {
      console.warn('集群下拉菜单元素未找到');
      return;
    }

    console.log('集群下拉菜单初始化:', {
      clusterSwitchBtn: !!clusterSwitchBtn,
      clusterDropdown: !!clusterDropdown,
      clusterOptionsCount: clusterOptions.length
    });

    // 点击按钮切换下拉菜单显示
    clusterSwitchBtn.addEventListener('click', (e) => {
      e.stopPropagation();
      const dropdown = clusterDropdown as HTMLElement;
      const isVisible = dropdown.style.display === 'block' ||
                       (!dropdown.style.display && getComputedStyle(dropdown).display !== 'none');

      console.log('集群下拉菜单点击事件:', {
        isVisible,
        currentDisplay: dropdown.style.display,
        computedDisplay: getComputedStyle(dropdown).display
      });

      if (isVisible) {
        dropdown.style.display = 'none';
        clusterSwitchBtn.classList.remove('active');
        console.log('隐藏集群下拉菜单');
      } else {
        dropdown.style.display = 'block';
        clusterSwitchBtn.classList.add('active');
        console.log('显示集群下拉菜单');
      }
    });

    // 点击选项进行集群切换
    clusterOptions.forEach(option => {
      option.addEventListener('click', (e) => {
        e.stopPropagation();
        const cluster = option.getAttribute('data-cluster') as 'tips' | 'small' | 'stable' | 'online';

        console.log('集群选项点击:', {
          cluster,
          hasCallback: !!this.onClusterSwitch
        });

        // 隐藏下拉菜单
        (clusterDropdown as HTMLElement).style.display = 'none';
        clusterSwitchBtn.classList.remove('active');

        // 触发集群切换事件
        if (this.onClusterSwitch && cluster) {
          console.log('触发集群切换回调:', cluster);
          this.onClusterSwitch(cluster);
        } else {
          console.warn('集群切换回调未设置或集群值无效:', { cluster, hasCallback: !!this.onClusterSwitch });
        }
      });
    });

    // 点击其他地方关闭下拉菜单
    document.addEventListener('click', () => {
      (clusterDropdown as HTMLElement).style.display = 'none';
      clusterSwitchBtn.classList.remove('active');
    });
  }

  /**
   * 渲染XUID列表
   */
  renderXuidList(
    xuidData: Record<string, XuidRecord[]>,
    expandedDomains: string[],
    currentDomain?: string | null,
    storageManager?: XuidStorageManager
  ): void {
    console.log('[RENDER-XUID-LIST] ========== 开始渲染XUID列表 ==========');
    console.log('[RENDER-XUID-LIST] 输入参数:', {
      xuidDataKeys: Object.keys(xuidData),
      expandedDomains,
      currentDomain,
      hasStorageManager: !!storageManager
    });

    const container = document.getElementById('xuidListContainer');
    let emptyState = document.getElementById('emptyState');

    console.log('[RENDER-XUID-LIST] DOM元素检查:', {
      container: !!container,
      emptyState: !!emptyState,
      containerParent: container?.parentElement?.className,
      modalExists: !!document.querySelector('.xuid-modal')
    });

    if (!container) {
      console.error('[RENDER-XUID-LIST] XUID列表容器未找到，可能模态框尚未创建');
      console.error('[RENDER-XUID-LIST] 当前DOM状态:', {
        allContainers: Array.from(document.querySelectorAll('[id*="Container"]')).map(el => el.id),
        allEmptyStates: Array.from(document.querySelectorAll('[id*="empty"]')).map(el => el.id),
        modalContent: !!document.querySelector('.modal-content'),
        modalBody: !!document.querySelector('.modal-body')
      });
      return;
    }

    // 如果emptyState不存在，重新创建它
    if (!emptyState) {
      console.log('[RENDER-XUID-LIST] emptyState元素不存在，重新创建...');
      emptyState = document.createElement('div');
      emptyState.className = 'xuid-empty-state';
      emptyState.id = 'emptyState';
      emptyState.style.display = 'none';
      emptyState.innerHTML = `
        <div class="empty-icon">📝</div>
        <div class="empty-title">暂无XUID记录</div>
        <div class="empty-text">点击"记录XUID"按钮开始记录</div>
      `;
      container.appendChild(emptyState);
      console.log('[RENDER-XUID-LIST] emptyState元素已重新创建');
    }

    // 使用固定分组数据
    if (!storageManager) {
      console.error('[RENDER-XUID-LIST] StorageManager未提供，无法使用固定分组功能');
      return;
    }

    console.log('[RENDER-XUID-LIST] 获取分组数据...');
    const groupedData = storageManager.getGroupedXuidData({ xuids: xuidData } as XuidData);
    console.log('[RENDER-XUID-LIST] 分组数据:', groupedData);

    // 检查是否有数据
    const hasData = Object.values(groupedData).some(group => group.xuids.length > 0);
    console.log('[RENDER-XUID-LIST] 是否有数据:', hasData);

    if (!hasData) {
      console.log('[RENDER-XUID-LIST] 没有数据，显示空状态');
      (emptyState as HTMLElement).style.display = 'block';
      container.innerHTML = '';
      return;
    }

    // 隐藏空状态
    console.log('[RENDER-XUID-LIST] 隐藏空状态，清空容器');
    (emptyState as HTMLElement).style.display = 'none';

    // 清空容器，但保留空状态元素
    const children = Array.from(container.children);
    children.forEach(child => {
      if (child.id !== 'emptyState') {
        container.removeChild(child);
      }
    });

    // 智能展开逻辑：展开当前域名所属的分组
    const smartExpandedGroups: string[] = [];
    if (currentDomain && storageManager) {
      const currentDomainGroup = storageManager.getDomainGroup(currentDomain);
      if (currentDomainGroup) {
        smartExpandedGroups.push(currentDomainGroup);
        console.log(`[RENDER-XUID-LIST] 智能展开当前域名分组: ${currentDomainGroup}`);
      }
    }
    console.log('[RENDER-XUID-LIST] 智能展开分组:', smartExpandedGroups);

    // 按固定分组渲染
    console.log('[RENDER-XUID-LIST] 开始渲染分组...');
    Object.entries(groupedData).forEach(([groupKey, groupData]) => {
      if (groupData.xuids && groupData.xuids.length > 0) {
        console.log('[RENDER-XUID-LIST] 渲染分组:', { groupKey, displayName: groupData.displayName, xuidCount: groupData.xuids.length });
        const domainGroup = this.createFixedDomainGroup(
          groupKey,
          groupData.displayName,
          groupData.xuids,
          smartExpandedGroups,
          currentDomain,
          storageManager
        );
        container.appendChild(domainGroup);
      }
    });

    // 恢复选中状态（重新渲染后DOM状态会丢失，需要根据内存状态恢复）
    console.log('[RENDER-XUID-LIST] 恢复选中状态:', this.selectedXuids);
    this.restoreSelectionState();

    // 更新按钮状态
    console.log('[RENDER-XUID-LIST] 更新按钮状态');
    this.updateButtonStates();
    console.log('[RENDER-XUID-LIST] ========== XUID列表渲染完成 ==========');
  }

  /**
   * 创建固定域名分组元素
   */
  createFixedDomainGroup(
    groupKey: string,
    displayName: string,
    xuids: (XuidRecord & { sourceDomain: string })[],
    expandedGroups: string[],
    currentDomain?: string | null,
    storageManager?: XuidStorageManager
  ): HTMLElement {
    const groupDiv = document.createElement('div');
    groupDiv.className = 'domain-group';
    groupDiv.dataset.groupKey = groupKey;

    // 检查是否应该展开
    const isExpanded = expandedGroups.includes(groupKey);

    // 域名标题
    const headerDiv = document.createElement('div');
    headerDiv.className = 'domain-header';
    headerDiv.innerHTML = `
        <div class="domain-info">
            <span class="domain-toggle ${isExpanded ? 'expanded' : ''}">▶</span>
            <span class="domain-name" title="${displayName}">${displayName}</span>
            <span class="domain-count">(${xuids.length})</span>
        </div>
    `;

    // 绑定展开/折叠事件
    const domainInfo = headerDiv.querySelector('.domain-info');
    domainInfo?.addEventListener('click', () => {
      this.toggleFixedDomainGroup(groupKey, groupDiv);
    });

    // XUID列表
    const listDiv = document.createElement('div');
    listDiv.className = `xuid-list ${isExpanded ? 'expanded' : ''}`;

    xuids.forEach(xuidData => {
      const itemDiv = this.createFixedGroupXuidItem(groupKey, xuidData, currentDomain, storageManager);
      listDiv.appendChild(itemDiv);
    });

    groupDiv.appendChild(headerDiv);
    groupDiv.appendChild(listDiv);

    return groupDiv;
  }

  /**
   * 切换固定域名分组的展开状态
   */
  toggleFixedDomainGroup(groupKey: string, groupDiv: HTMLElement): void {
    const toggle = groupDiv.querySelector('.domain-toggle');
    const list = groupDiv.querySelector('.xuid-list');

    if (toggle && list) {
      const isExpanded = list.classList.contains('expanded');

      if (isExpanded) {
        list.classList.remove('expanded');
        toggle.classList.remove('expanded');
      } else {
        list.classList.add('expanded');
        toggle.classList.add('expanded');
      }
    }
  }

  /**
   * 更新按钮状态
   */
  updateButtonStates(): void {
    const switchBtn = document.getElementById('switchXuidBtn') as HTMLButtonElement;
    const deleteBtn = document.getElementById('deleteXuidBtn') as HTMLButtonElement;

    console.log(`[BUTTON-STATES] 更新按钮状态`, {
      switchBtnExists: !!switchBtn,
      deleteBtnExists: !!deleteBtn,
      selectedCount: this.selectedXuids.length,
      selectedXuids: this.selectedXuids.slice()
    });

    if (switchBtn && deleteBtn) {
      const selectedCount = this.selectedXuids.length;
      const hasSelected = selectedCount > 0;
      const canSwitch = selectedCount === 1; // 只有选中单个XUID时才能切换
      const wasDisabled = { switch: switchBtn.disabled, delete: deleteBtn.disabled };

      // 切换按钮：只有选中单个XUID时才启用
      switchBtn.disabled = !canSwitch;
      // 删除按钮：选中任意数量的XUID都可以删除
      deleteBtn.disabled = !hasSelected;

      // 更新按钮文本提示
      if (selectedCount === 0) {
        switchBtn.title = '请先选择要切换的XUID';
      } else if (selectedCount === 1) {
        switchBtn.title = `切换到XUID: ${this.selectedXuids[0]}`;
      } else {
        switchBtn.title = `一次只能切换一个XUID，当前选中${selectedCount}个`;
      }

      console.log(`[BUTTON-STATES] 按钮状态更新完成`, {
        selectedCount,
        hasSelected,
        canSwitch,
        switchBtn: { was: wasDisabled.switch, now: switchBtn.disabled, title: switchBtn.title },
        deleteBtn: { was: wasDisabled.delete, now: deleteBtn.disabled }
      });
    } else {
      console.warn(`[BUTTON-STATES] 按钮元素未找到`, {
        switchBtn: !!switchBtn,
        deleteBtn: !!deleteBtn
      });
    }
  }

  /**
   * 创建固定分组的XUID项目元素
   */
  createFixedGroupXuidItem(
    groupKey: string,
    xuidData: XuidRecord & { sourceDomain: string },
    currentDomain?: string | null,
    storageManager?: XuidStorageManager
  ): HTMLElement {
    const itemDiv = document.createElement('div');
    itemDiv.className = 'xuid-item';

    const xuidValue = typeof xuidData === 'string' ? xuidData : xuidData.value;
    const username = typeof xuidData === 'object' ? xuidData.username : null;
    const cookieName = typeof xuidData === 'object' ? xuidData.cookieName : 'XUID';

    // 检查是否可以切换（域名限制）
    const canSwitch = currentDomain && storageManager ?
      storageManager.isXuidInCurrentDomainGroup(currentDomain, groupKey) : true;

    // 获取资产信息
    const assetInfo = this.networkMonitor?.getCachedAssetInfoForList(xuidValue) || '';

    itemDiv.innerHTML = `
        <div class="xuid-checkbox">
            <input type="checkbox" id="xuid-${xuidValue}" value="${xuidValue}" ${!canSwitch ? 'disabled' : ''}>
        </div>
        <div class="xuid-realname" title="${username || '未知用户'}">${username || '未知用户'}</div>
        <div class="xuid-asset" title="${assetInfo || '-'}" data-xuid="${xuidValue}">${assetInfo || '-'}</div>
        <div class="xuid-value" title="点击复制 ${xuidValue}" style="cursor: pointer;">${xuidValue}</div>
    `;

    // 异步更新资产信息
    this.updateAssetInfoAsync(xuidValue);

    // 如果不能切换，添加禁用样式
    if (!canSwitch) {
      itemDiv.classList.add('disabled');
      itemDiv.title = '当前域名不支持切换到此XUID';
    }

    // 绑定复选框事件
    const checkbox = itemDiv.querySelector('input[type="checkbox"]') as HTMLInputElement;
    if (checkbox) {
      console.log(`[XUID-CHECKBOX] 绑定复选框事件: ${xuidValue}`, { disabled: checkbox.disabled, canSwitch });
      checkbox.addEventListener('change', (e) => {
        e.stopPropagation(); // 防止事件冒泡
        console.log(`[XUID-CHECKBOX] 复选框状态变化: ${xuidValue}`, {
          checked: checkbox.checked,
          disabled: checkbox.disabled,
          eventTarget: e.target,
          eventType: e.type,
          timeStamp: e.timeStamp
        });
        this.handleXuidSelection(xuidValue, checkbox.checked);
      });
    } else {
      console.warn(`[XUID-CHECKBOX] 复选框元素未找到: ${xuidValue}`);
    }

    // 绑定XUID值点击复制事件
    const xuidValueElement = itemDiv.querySelector('.xuid-value') as HTMLElement;
    if (xuidValueElement) {
      xuidValueElement.addEventListener('click', (e) => {
        e.stopPropagation(); // 防止触发其他事件
        console.log(`[XUID-COPY] 点击复制XUID: ${xuidValue}`);
        this.copyXuidToClipboard(xuidValue);
      });
    }

    // 只为XUID值元素绑定双击切换事件，避免整行双击的意外触发
    if (canSwitch) {
      const xuidValueElement = itemDiv.querySelector('.xuid-value') as HTMLElement;
      if (xuidValueElement) {
        let lastClickTime = 0;
        xuidValueElement.addEventListener('dblclick', (e) => {
          e.stopPropagation(); // 防止事件冒泡
          const currentTime = Date.now();
          console.log(`[XUID-DBLCLICK] XUID值双击事件触发: ${xuidValue}`, {
            currentTime,
            lastClickTime,
            timeDiff: currentTime - lastClickTime,
            eventTarget: e.target,
            eventType: e.type,
            canSwitch,
            hasCallback: !!this.onXuidSwitch,
            currentDomain,
            stackTrace: new Error().stack
          });

          // 防抖：避免快速连续触发
          if (currentTime - lastClickTime < 300) {
            console.warn(`[XUID-DBLCLICK] 双击事件被防抖拦截: ${xuidValue}`);
            return;
          }
          lastClickTime = currentTime;

          if (this.onXuidSwitch && currentDomain) {
            console.log(`[XUID-DBLCLICK] 执行XUID切换回调: ${xuidValue}`);
            this.onXuidSwitch(currentDomain, xuidValue);
          } else {
            console.warn(`[XUID-DBLCLICK] 无法执行XUID切换: ${xuidValue}`, {
              hasCallback: !!this.onXuidSwitch,
              currentDomain
            });
          }
        });

        // 为XUID值元素添加双击提示
        xuidValueElement.title = `点击复制，双击切换 ${xuidValue}`;
      }
    }

    return itemDiv;
  }

  /**
   * 处理XUID选择
   */
  handleXuidSelection(xuidValue: string, selected: boolean): void {
    console.log(`[XUID-SELECTION] 处理XUID选择: ${xuidValue}`, {
      selected,
      currentSelected: this.selectedXuids.slice(),
      alreadySelected: this.selectedXuids.includes(xuidValue)
    });

    if (selected) {
      if (!this.selectedXuids.includes(xuidValue)) {
        this.selectedXuids.push(xuidValue);
        console.log(`[XUID-SELECTION] 添加XUID到选中列表: ${xuidValue}`);
      } else {
        console.log(`[XUID-SELECTION] XUID已在选中列表中: ${xuidValue}`);
      }
    } else {
      const index = this.selectedXuids.indexOf(xuidValue);
      if (index > -1) {
        this.selectedXuids.splice(index, 1);
        console.log(`[XUID-SELECTION] 从选中列表移除XUID: ${xuidValue}`);
      } else {
        console.log(`[XUID-SELECTION] XUID不在选中列表中: ${xuidValue}`);
      }
    }

    console.log(`[XUID-SELECTION] 更新后的选中列表:`, this.selectedXuids);
    this.updateButtonStates();
    console.log(`[XUID-SELECTION] 按钮状态已更新`);
  }

  /**
   * 获取选中的XUID列表
   */
  getSelectedXuids(): string[] {
    return [...this.selectedXuids];
  }

  /**
   * 恢复选中状态（重新渲染后根据内存状态恢复DOM状态）
   */
  restoreSelectionState(): void {
    console.log('[RESTORE-SELECTION] 开始恢复选中状态:', this.selectedXuids);

    this.selectedXuids.forEach(xuid => {
      const checkbox = document.getElementById(`xuid-${xuid}`) as HTMLInputElement;
      if (checkbox) {
        checkbox.checked = true;
        console.log(`[RESTORE-SELECTION] 恢复选中状态: ${xuid}`);
      } else {
        console.warn(`[RESTORE-SELECTION] 复选框未找到: ${xuid}`);
      }
    });

    console.log('[RESTORE-SELECTION] 选中状态恢复完成');
  }

  /**
   * 清除选中状态
   */
  clearSelection(): void {
    this.selectedXuids = [];

    // 取消所有复选框的选中状态
    const checkboxes = document.querySelectorAll('.xuid-item input[type="checkbox"]') as NodeListOf<HTMLInputElement>;
    checkboxes.forEach(checkbox => {
      checkbox.checked = false;
    });

    this.updateButtonStates();
  }

  /**
   * 设置域名不支持时的UI状态
   */
  setDomainUnsupportedState(): void {
    // 隐藏所有功能区域
    const statusSection = document.querySelector('.status-info-section') as HTMLElement;
    const actionsSection = document.querySelector('.quick-actions-section') as HTMLElement;
    const managementSection = document.querySelector('.xuid-management-section') as HTMLElement;

    if (statusSection) statusSection.style.display = 'none';
    if (actionsSection) actionsSection.style.display = 'none';
    if (managementSection) managementSection.style.display = 'none';

    // 显示域名不支持提示
    this.showDomainUnsupportedMessage();
  }

  /**
   * 恢复正常UI状态
   */
  restoreNormalState(): void {
    // 显示所有功能区域
    const statusSection = document.querySelector('.status-info-section') as HTMLElement;
    const actionsSection = document.querySelector('.quick-actions-section') as HTMLElement;
    const managementSection = document.querySelector('.xuid-management-section') as HTMLElement;

    if (statusSection) statusSection.style.display = 'block';
    if (actionsSection) actionsSection.style.display = 'block';
    if (managementSection) managementSection.style.display = 'block';

    // 隐藏域名不支持提示
    this.hideDomainUnsupportedMessage();
  }

  /**
   * 显示域名不支持提示
   */
  private showDomainUnsupportedMessage(): void {
    let messageDiv = document.getElementById('domain-unsupported-message');
    if (!messageDiv) {
      messageDiv = document.createElement('div');
      messageDiv.id = 'domain-unsupported-message';
      messageDiv.className = 'domain-unsupported-message';
      messageDiv.innerHTML = `
        <div class="unsupported-icon">🚫</div>
        <div class="unsupported-title">域名不支持</div>
        <div class="unsupported-text">当前域名不支持XUID切换功能</div>
        <div class="unsupported-hint">请在zuoyebang.cc或suanshubang.cc域名下使用</div>
      `;

      const container = document.querySelector('.container');
      if (container) {
        container.appendChild(messageDiv);
      }
    }
    messageDiv.style.display = 'block';
  }

  /**
   * 隐藏域名不支持提示
   */
  private hideDomainUnsupportedMessage(): void {
    const messageDiv = document.getElementById('domain-unsupported-message');
    if (messageDiv) {
      messageDiv.style.display = 'none';
    }
  }

  /**
   * 显示消息提示
   */
  showMessage(message: string, type: 'success' | 'error' | 'info' = 'info'): void {
    console.log(`[${type.toUpperCase()}] ${message}`);

    // 使用通知管理器显示页面内通知
    switch (type) {
      case 'success':
        notificationManager.success(message);
        break;
      case 'error':
        notificationManager.error(message);
        break;
      case 'info':
      default:
        notificationManager.info(message);
        break;
    }
  }

  /**
   * 异步更新资产信息
   */
  private async updateAssetInfoAsync(xuid: string): Promise<void> {
    try {
      // 从存储中获取资产信息
      const result = await chrome.storage.local.get(['assetInfoList']);
      if (result.assetInfoList && result.assetInfoList[xuid]) {
        const assetInfo = result.assetInfoList[xuid];
        const assetType = typeof assetInfo === 'string' ? assetInfo : assetInfo.type;

        if (assetType) {
          // 更新UI中的资产信息显示
          const assetElement = document.querySelector(`[data-xuid="${xuid}"]`) as HTMLElement;
          if (assetElement) {
            assetElement.textContent = assetType;
            assetElement.title = `资产类型: ${assetType}`;
            console.log('[UI-ASSET] 异步更新资产信息:', { xuid, assetType });
          }

          // 同时更新缓存
          if (this.networkMonitor) {
            this.networkMonitor.setCachedAssetInfoForList(xuid, assetType);
          }
        }
      }
    } catch (error) {
      console.error('[UI-ASSET] 异步更新资产信息失败:', error);
    }
  }
}
