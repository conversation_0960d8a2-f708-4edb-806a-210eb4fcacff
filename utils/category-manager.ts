/**
 * 分类管理系统
 * 提供分类的创建、删除、修改和查询功能
 */

export interface Category {
  id: string;
  name: string;
  color?: string;
  icon?: string;
  order: number;
  createdAt: number;
  updatedAt: number;
}

export interface CategoryStorage {
  getCategories(): Promise<Category[]>;
  saveCategory(category: Category): Promise<void>;
  updateCategory(id: string, updates: Partial<Category>): Promise<void>;
  removeCategory(id: string): Promise<void>;
  clear(): Promise<void>;
}

// 本地存储实现
class LocalCategoryStorage implements CategoryStorage {
  private readonly STORAGE_KEY = 'fwyy-tools-categories';

  async getCategories(): Promise<Category[]> {
    try {
      const result = await browser.storage.local.get(this.STORAGE_KEY);
      return result[this.STORAGE_KEY] || [];
    } catch (error) {
      console.error('获取分类列表失败:', error);
      return [];
    }
  }

  async saveCategory(category: Category): Promise<void> {
    try {
      const categories = await this.getCategories();
      const existingIndex = categories.findIndex(c => c.id === category.id);
      
      if (existingIndex >= 0) {
        categories[existingIndex] = category;
      } else {
        categories.push(category);
      }
      
      await browser.storage.local.set({ [this.STORAGE_KEY]: categories });
    } catch (error) {
      console.error('保存分类失败:', error);
      throw error;
    }
  }

  async updateCategory(id: string, updates: Partial<Category>): Promise<void> {
    try {
      const categories = await this.getCategories();
      const index = categories.findIndex(c => c.id === id);
      
      if (index === -1) {
        throw new Error(`分类 ${id} 不存在`);
      }
      
      categories[index] = { 
        ...categories[index], 
        ...updates, 
        updatedAt: Date.now() 
      };
      
      await browser.storage.local.set({ [this.STORAGE_KEY]: categories });
    } catch (error) {
      console.error('更新分类失败:', error);
      throw error;
    }
  }

  async removeCategory(id: string): Promise<void> {
    try {
      const categories = await this.getCategories();
      const filteredCategories = categories.filter(c => c.id !== id);
      await browser.storage.local.set({ [this.STORAGE_KEY]: filteredCategories });
    } catch (error) {
      console.error('删除分类失败:', error);
      throw error;
    }
  }

  async clear(): Promise<void> {
    try {
      await browser.storage.local.remove(this.STORAGE_KEY);
    } catch (error) {
      console.error('清空分类失败:', error);
      throw error;
    }
  }
}

// 分类管理器类
export class CategoryManager {
  private categories: Map<string, Category> = new Map();
  private listeners: Set<() => void> = new Set();
  private storage: CategoryStorage;
  private initialized = false;

  constructor(storage?: CategoryStorage) {
    this.storage = storage || new LocalCategoryStorage();
  }

  // 初始化分类管理器
  async init(): Promise<void> {
    if (this.initialized) return;
    
    try {
      const storedCategories = await this.storage.getCategories();
      
      // 如果没有分类，创建默认的"全部"分类
      if (storedCategories.length === 0) {
        await this.createDefaultCategories();
      } else {
        storedCategories.forEach(category => {
          this.categories.set(category.id, category);
        });
      }
      
      this.initialized = true;
      this.notifyListeners();
    } catch (error) {
      console.error('初始化分类管理器失败:', error);
    }
  }

  // 创建默认分类
  private async createDefaultCategories(): Promise<void> {
    const defaultCategory: Category = {
      id: 'all',
      name: '全部',
      color: '#6366f1',
      icon: '📁',
      order: 0,
      createdAt: Date.now(),
      updatedAt: Date.now()
    };

    await this.addCategory(defaultCategory);
  }

  // 添加分类
  async addCategory(category: Omit<Category, 'createdAt' | 'updatedAt'>): Promise<void> {
    try {
      // 检查分类ID是否已存在
      if (this.categories.has(category.id)) {
        throw new Error(`分类 ${category.id} 已存在`);
      }

      const newCategory: Category = {
        ...category,
        createdAt: Date.now(),
        updatedAt: Date.now()
      };

      // 保存到本地存储
      await this.storage.saveCategory(newCategory);
      
      // 更新内存中的分类
      this.categories.set(newCategory.id, newCategory);
      
      console.log(`✅ 分类 ${newCategory.name} (${newCategory.id}) 添加成功`);
      this.notifyListeners();
    } catch (error) {
      console.error('添加分类失败:', error);
      throw error;
    }
  }

  // 更新分类
  async updateCategory(id: string, updates: Partial<Omit<Category, 'id' | 'createdAt'>>): Promise<void> {
    try {
      const category = this.categories.get(id);
      if (!category) {
        throw new Error(`分类 ${id} 不存在`);
      }
      
      const updatedCategory = { 
        ...category, 
        ...updates, 
        updatedAt: Date.now() 
      };
      
      await this.storage.updateCategory(id, updates);
      this.categories.set(id, updatedCategory);
      this.notifyListeners();
    } catch (error) {
      console.error('更新分类失败:', error);
      throw error;
    }
  }

  // 删除分类
  async removeCategory(id: string): Promise<void> {
    try {
      // 不能删除"全部"分类
      if (id === 'all') {
        throw new Error('不能删除"全部"分类');
      }

      if (!this.categories.has(id)) {
        throw new Error(`分类 ${id} 不存在`);
      }

      // 级联删除：清理所有引用此分类的工具
      await this.cleanupToolsReferences(id);

      await this.storage.removeCategory(id);
      this.categories.delete(id);
      this.notifyListeners();
    } catch (error) {
      console.error('删除分类失败:', error);
      throw error;
    }
  }

  // 清理工具中对已删除分类的引用
  private async cleanupToolsReferences(categoryId: string): Promise<void> {
    try {
      // 动态导入 toolRegistry 以避免循环依赖
      const { toolRegistry } = await import('./tool-registry');

      const tools = toolRegistry.getAll();
      const toolsToUpdate: { id: string; categories: string[] }[] = [];

      for (const tool of tools) {
        if (tool.categories && tool.categories.includes(categoryId)) {
          // 从工具的分类数组中移除被删除的分类
          const updatedCategories = tool.categories.filter(cat => cat !== categoryId);

          // 确保工具至少有一个分类
          if (updatedCategories.length === 0) {
            updatedCategories.push('all');
          }

          toolsToUpdate.push({
            id: tool.id,
            categories: updatedCategories
          });
        }
      }

      // 批量更新工具
      for (const update of toolsToUpdate) {
        // 获取内存中的工具实例
        const toolInstance = toolRegistry.getById(update.id);
        if (toolInstance) {
          // 更新内存中的实例
          toolInstance.categories = update.categories;
          // 保存到存储
          await toolRegistry.updateTool(update.id, { categories: update.categories });
        }
      }

      if (toolsToUpdate.length > 0) {
        console.log(`✅ 已清理 ${toolsToUpdate.length} 个工具中对分类 ${categoryId} 的引用`);
      }
    } catch (error) {
      console.error('清理工具引用失败:', error);
      throw error;
    }
  }

  // 获取所有分类
  getAll(): Category[] {
    return Array.from(this.categories.values()).sort((a, b) => a.order - b.order);
  }

  // 根据ID获取分类
  getById(id: string): Category | undefined {
    return this.categories.get(id);
  }

  // 检查分类是否存在
  exists(id: string): boolean {
    return this.categories.has(id);
  }

  // 检查分类名称是否已存在
  existsByName(name: string): boolean {
    return Array.from(this.categories.values()).some(category =>
      category.name.toLowerCase() === name.toLowerCase()
    );
  }

  // 获取分类数量
  getCount(): number {
    return this.categories.size;
  }

  // 生成唯一的分类ID
  generateId(name: string): string {
    const baseId = name.toLowerCase().replace(/[^a-z0-9]/g, '-');
    let id = baseId;
    let counter = 1;
    
    while (this.categories.has(id)) {
      id = `${baseId}-${counter}`;
      counter++;
    }
    
    return id;
  }

  // 添加监听器
  addListener(listener: () => void): void {
    this.listeners.add(listener);
  }

  // 移除监听器
  removeListener(listener: () => void): void {
    this.listeners.delete(listener);
  }

  // 通知所有监听器
  private notifyListeners(): void {
    this.listeners.forEach(listener => {
      try {
        listener();
      } catch (error) {
        console.error('分类监听器执行失败:', error);
      }
    });
  }

  // 清空所有分类（仅用于测试）
  async clear(): Promise<void> {
    try {
      await this.storage.clear();
      this.categories.clear();
      this.notifyListeners();
    } catch (error) {
      console.error('清空分类失败:', error);
      throw error;
    }
  }
}

// 导出单例实例
export const categoryManager = new CategoryManager();
