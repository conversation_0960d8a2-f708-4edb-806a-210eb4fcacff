/**
 * 告警解析器核心类
 * 基于作业帮Falcon监控系统的告警格式
 */

// 类型定义
export interface ParsedAlert {
  level?: string;
  status?: string;
  name?: string;
  hostIP?: string;
  hostname?: string;
  monitorItem?: string;
  currentValue?: string;
  serviceUnit?: string;
  alertTime?: string;
  startTime?: string;
  recoveryTime?: string;
  duration?: string;
  hostInfo?: HostInfo;
  metricInfo?: MetricInfo;
}

export interface HostInfo {
  serviceName: string;
  moduleName: string;
  podId?: string;
  clusterName?: string;
  clusterId: string;
  clusterKey: string;
  originalClusterKey: string;
  fullCluster?: string;
  source?: string;
}

export interface MetricInfo {
  metricName?: string;
  operator?: string;
  threshold?: number;
  thresholdExpression?: string;
  remoteIp?: string;
}

export interface TimeRange {
  startTime: Date;
  endTime: Date;
  startTimestamp: number;
  endTimestamp: number;
  startTimeStr: string;
  endTimeStr: string;
}

export interface CollecterInfo {
  file_path?: string;
  pattern?: string;
  id?: string;
  name: string;
  type: string;
  originalMetric?: any;
  searchStrategy?: string;
  source?: string;
  error?: string;
  fallbackAttempted?: boolean;
  apiError?: string;
}

export interface GrafanaUrlResult {
  url: string;
  collecterInfo: CollecterInfo;
  searchType: string;
  searchCondition?: string;
  baseQuery: string;
}

export class AlertParserCore {
  private grafanaBaseUrl = 'https://log-search-docker.zuoyebang.cc/explore';
  private orgId = 4;
  private falconApiBase = 'https://falcon.op.zuoyebang.cc/api/v1';
  private devopsApiBase = 'https://devops-docker.zuoyebang.cc/api/odin';

  // 缓存
  private serviceTreeCache: any = null;
  private clusterMappingCache: any = null;
  private collecterMappingCache: any = null;

  /**
   * 解析告警文本，提取关键信息
   */
  parseAlert(alertText: string): ParsedAlert {
    try {
      const lines = alertText.trim().split('\n');
      const result: ParsedAlert = {};

      // 解析每一行
      lines.forEach(line => {
        const trimmedLine = line.trim();

        // 解析告警级别和状态 [P2][异常]
        if (trimmedLine.startsWith('[') && trimmedLine.includes('][')) {
          const matches = trimmedLine.match(/\[([^\]]+)\]\[([^\]]+)\]/);
          if (matches) {
            result.level = matches[1];      // P2
            result.status = matches[2];     // 异常
          }
        }

        // 解析各个字段
        if (trimmedLine.includes(':')) {
          const [key, ...valueParts] = trimmedLine.split(':');
          const value = valueParts.join(':').trim();

          switch (key.trim()) {
            case '名 称':
              result.name = value;
              break;
            case '主机IP':
              result.hostIP = value;
              break;
            case '主机名':
              result.hostname = value;
              break;
            case '监控项':
              result.monitorItem = value;
              break;
            case '当前值':
              result.currentValue = value;
              break;
            case '服务单元':
              result.serviceUnit = value;
              break;
            case '时间':
              result.alertTime = value;
              break;
            case '开始时间':
              result.startTime = value;
              // 对于恢复类型的告警，使用开始时间作为主要的告警时间
              if (!result.alertTime) {
                result.alertTime = value;
              }
              break;
            case '恢复时间':
              result.recoveryTime = value;
              break;
            case '持续时长':
              result.duration = value;
              break;
          }
        }
      });

      // 解析主机信息 - 优先使用服务单元字段
      if (result.serviceUnit) {
        try {
          console.log(`🔍 优先使用服务单元字段解析主机信息`);
          result.hostInfo = this.parseServiceUnit(result.serviceUnit, result.hostname);
        } catch (serviceUnitError) {
          console.warn(`⚠️ 服务单元解析失败，降级使用主机名解析: ${serviceUnitError.message}`);
          if (result.hostname) {
            result.hostInfo = this.parseHostname(result.hostname);
          }
        }
      } else if (result.hostname) {
        console.log(`🔍 使用主机名解析主机信息`);
        result.hostInfo = this.parseHostname(result.hostname);
      }

      // 解析监控项获取指标信息
      if (result.monitorItem) {
        result.metricInfo = this.parseMonitorItem(result.monitorItem);
      }

      return result;
    } catch (error) {
      throw new Error(`告警解析失败: ${error.message}`);
    }
  }

  /**
   * 解析主机名，提取模块名、集群信息等
   */
  parseHostname(hostname: string): HostInfo {
    console.log(`🔍 开始解析主机名: ${hostname}`);

    // 检测主机名格式类型
    if (hostname.includes('.k8s.')) {
      console.log(`📋 检测到完整k8s格式`);
      return this.parseK8sHostname(hostname);
    } else {
      console.log(`📋 检测到简化格式`);
      return this.parseSimpleHostname(hostname);
    }
  }

  /**
   * 解析完整k8s格式的主机名
   * 格式: support.assistantdeskgo-695f76f989-df6qh.k8s.online.alibjh2-docker
   */
  private parseK8sHostname(hostname: string): HostInfo {
    // 分步解析以处理复杂的Pod ID格式
    const parts = hostname.split('.k8s.');
    if (parts.length !== 2) {
      throw new Error(`无法解析k8s主机名格式: ${hostname}`);
    }

    const beforeK8s = parts[0]; // support.assistantdeskgo-695f76f989-df6qh
    const afterK8s = parts[1];  // online.alibjh2-docker

    // 解析前半部分：service.module-podId
    const serviceModuleParts = beforeK8s.split('.');
    if (serviceModuleParts.length !== 2) {
      throw new Error(`无法解析服务和模块部分: ${beforeK8s}`);
    }

    const serviceName = serviceModuleParts[0]; // support
    const moduleAndPod = serviceModuleParts[1]; // assistantdeskgo-695f76f989-df6qh

    // 解析模块名和Pod ID
    const podIdPattern = /^(.+)-([a-z0-9]{8,10}-[a-z0-9]{4,5})$/;
    const moduleMatch = moduleAndPod.match(podIdPattern);

    let moduleName: string;
    let podId: string;

    if (!moduleMatch) {
      // 降级解析
      console.warn(`⚠️ 无法识别Pod ID模式，使用降级解析: ${moduleAndPod}`);
      const fallbackMatch = moduleAndPod.match(/^([^-]+)-(.+)$/);
      if (!fallbackMatch) {
        throw new Error(`无法解析模块和Pod部分: ${moduleAndPod}`);
      }
      moduleName = fallbackMatch[1];
      podId = fallbackMatch[2];
    } else {
      moduleName = moduleMatch[1]; // assistantdeskgo
      podId = moduleMatch[2];      // 695f76f989-df6qh
    }

    // 解析后半部分：cluster.clusterId-docker
    const clusterMatch = afterK8s.match(/^(.+)\.(.+)-docker$/);
    if (!clusterMatch) {
      throw new Error(`无法解析集群部分: ${afterK8s}`);
    }

    const clusterName = clusterMatch[1]; // online
    const clusterId = clusterMatch[2];   // alibjh2

    // 构建集群映射键
    const clusterKey = `k8s.${clusterName}.${clusterId}`;

    console.log(`✅ k8s格式解析成功: 服务=${serviceName}, 模块=${moduleName}, 集群=${clusterName}, 集群ID=${clusterId}`);

    return {
      serviceName,
      moduleName,
      podId,
      clusterName,
      clusterId,
      clusterKey,
      originalClusterKey: `${clusterName}.${clusterId}`,
      fullCluster: undefined
    };
  }

  /**
   * 解析简化格式的主机名
   * 格式: assistantweb.support.qcvmbj6-docker
   */
  private parseSimpleHostname(hostname: string): HostInfo {
    const parts = hostname.split('.');
    if (parts.length !== 3) {
      throw new Error(`无法解析简化主机名格式，期望3个部分: ${hostname}`);
    }

    const moduleName = parts[0];  // assistantweb
    const serviceName = parts[1]; // support
    const clusterPart = parts[2]; // qcvmbj6-docker

    // 提取clusterId
    const clusterMatch = clusterPart.match(/^(.+)-docker$/);
    if (!clusterMatch) {
      throw new Error(`无法解析集群部分，期望以-docker结尾: ${clusterPart}`);
    }

    const clusterId = clusterMatch[1]; // qcvmbj6
    const clusterKey = clusterId;

    console.log(`✅ 简化格式解析成功: 服务=${serviceName}, 模块=${moduleName}, 集群ID=${clusterId}`);

    return {
      serviceName,
      moduleName,
      podId: undefined,
      clusterName: undefined,
      clusterId,
      clusterKey,
      originalClusterKey: clusterId,
      fullCluster: undefined
    };
  }

  /**
   * 解析服务单元字段
   * 格式: assistant-ai.support.qcvmbj6-docker
   */
  parseServiceUnit(serviceUnit: string, hostname?: string): HostInfo {
    console.log(`🔍 开始解析服务单元: ${serviceUnit}`);

    const parts = serviceUnit.split('.');
    if (parts.length !== 3) {
      throw new Error(`无法解析服务单元格式，期望3个部分: ${serviceUnit}`);
    }

    const moduleName = parts[0];  // assistant-ai
    const serviceName = parts[1]; // support
    const clusterPart = parts[2]; // qcvmbj6-docker

    // 提取clusterId
    const clusterMatch = clusterPart.match(/^(.+)-docker$/);
    if (!clusterMatch) {
      throw new Error(`无法解析集群部分，期望以-docker结尾: ${clusterPart}`);
    }

    let clusterId = clusterMatch[1]; // qcvmbj6
    let clusterKey = clusterId;
    let clusterName: string | undefined;

    // 尝试从主机名中提取完整的集群信息
    if (hostname && hostname.includes('.k8s.')) {
      try {
        console.log(`🔍 从主机名提取完整集群信息: ${hostname}`);
        const hostnameParts = hostname.split('.k8s.');
        if (hostnameParts.length === 2) {
          const afterK8s = hostnameParts[1];
          const clusterMatchFromHostname = afterK8s.match(/^(.+)\.(.+)-docker$/);
          if (clusterMatchFromHostname) {
            clusterName = clusterMatchFromHostname[1];
            const clusterIdFromHostname = clusterMatchFromHostname[2];

            if (clusterId.startsWith(clusterIdFromHostname)) {
              clusterKey = `k8s.${clusterName}.${clusterIdFromHostname}`;
              clusterId = clusterIdFromHostname;
              console.log(`✅ 从主机名提取到完整集群信息: ${clusterKey}`);
            } else if (clusterIdFromHostname === clusterId) {
              clusterKey = `k8s.${clusterName}.${clusterId}`;
              console.log(`✅ 从主机名提取到完整集群信息: ${clusterKey}`);
            }
          }
        }
      } catch (error) {
        console.warn(`⚠️ 从主机名提取集群信息失败: ${error.message}`);
      }
    }

    console.log(`✅ 服务单元解析成功: 服务=${serviceName}, 模块=${moduleName}, 集群=${clusterName || '未知'}, 集群ID=${clusterId}`);

    return {
      serviceName,
      moduleName,
      podId: undefined,
      clusterName,
      clusterId,
      clusterKey,
      originalClusterKey: clusterId,
      fullCluster: undefined,
      source: 'serviceUnit'
    };
  }

  /**
   * 解析监控项，提取监控指标等信息
   */
  parseMonitorItem(monitorItem: string): MetricInfo {
    const result: MetricInfo = {};

    // 提取监控指标名
    const metricMatch = monitorItem.match(/\b([a-zA-Z][a-zA-Z0-9_]*\.[a-zA-Z0-9_.\-\/=]+)/);
    if (metricMatch) {
      result.metricName = metricMatch[1];
    }

    // 改进的阈值解析
    const thresholdMatch = monitorItem.match(/(>=|<=|>|<|==|!=)\s*(-?\d+(?:\.\d+)?)/);
    if (thresholdMatch) {
      result.operator = thresholdMatch[1];
      result.threshold = parseFloat(thresholdMatch[2]);
      result.thresholdExpression = `${result.operator} ${result.threshold}`;
    }

    // 提取标签信息
    const tagsMatch = monitorItem.match(/remote_ip=([^,\s]+)/);
    if (tagsMatch) {
      result.remoteIp = tagsMatch[1];
    }

    return result;
  }

  /**
   * 计算时间范围 (告警时间前5分钟到告警时间)
   */
  calculateTimeRange(alertTimeStr: string): TimeRange {
    try {
      // 解析告警时间
      const alertTime = new Date(alertTimeStr);
      if (isNaN(alertTime.getTime())) {
        throw new Error(`无效的时间格式: ${alertTimeStr}`);
      }

      // 计算起始时间 (前5分钟)
      const startTime = new Date(alertTime.getTime() - 5 * 60 * 1000);

      return {
        startTime,
        endTime: alertTime,
        startTimestamp: startTime.getTime(),
        endTimestamp: alertTime.getTime(),
        startTimeStr: startTime.getTime().toString(),
        endTimeStr: alertTime.getTime().toString()
      };
    } catch (error) {
      throw new Error(`时间范围计算失败: ${error.message}`);
    }
  }

  /**
   * 解析原始指标名称
   * 支持移除连续的汇聚后缀，如：_cnt_sum, _avg_max 等
   */
  parseOriginalMetricName(metricName: string): string {
    if (!metricName) return metricName;

    console.log(`🔍 开始解析原始指标名: ${metricName}`);

    let originalName = metricName;

    // 移除 GROUP_BY 部分
    originalName = originalName.replace(/\s+GROUP_BY\s+\([^)]+\).*$/i, '');

    // 移除连续的汇聚后缀
    const aggrSuffixes = ['_cnt', '_avg', '_max', '_min', '_sum'];
    let removedSuffixes: string[] = [];
    let hasRemovedSuffix = true;

    while (hasRemovedSuffix) {
      hasRemovedSuffix = false;

      for (const suffix of aggrSuffixes) {
        if (originalName.endsWith(suffix)) {
          originalName = originalName.slice(0, -suffix.length);
          removedSuffixes.push(suffix);
          hasRemovedSuffix = true;
          console.log(`🔧 移除后缀 ${suffix}, 当前结果: ${originalName}`);
          break;
        }
      }
    }

    if (removedSuffixes.length > 0) {
      console.log(`✅ 移除的汇聚后缀: [${removedSuffixes.join(', ')}]`);
    } else {
      console.log(`ℹ️ 未检测到汇聚后缀`);
    }

    console.log(`✅ 解析结果: ${metricName} -> ${originalName}`);
    return originalName;
  }

  /**
   * 获取服务树信息 (使用本地JSON文件)
   */
  async getServiceTree(): Promise<any> {
    try {
      // 如果有缓存，直接返回
      if (this.serviceTreeCache) {
        console.log(`📋 使用缓存的服务树数据`);
        return this.serviceTreeCache;
      }

      console.log(`🔍 开始加载本地服务树数据...`);

      // 浏览器扩展环境：使用fetch加载JSON文件
      const configUrl = browser.runtime.getURL('config/productLineServiceTree.json');
      console.log(`🌐 浏览器环境：加载 ${configUrl}`);

      const response = await fetch(configUrl);
      if (!response.ok) {
        throw new Error(`无法加载服务树文件: HTTP ${response.status}`);
      }

      const serviceTreeData = await response.json();

      // 验证数据格式
      if (!serviceTreeData || !serviceTreeData.data || !serviceTreeData.data.tree) {
        throw new Error('服务树数据格式不正确');
      }

      console.log(`✅ 成功加载本地服务树数据`);

      // 缓存数据
      this.serviceTreeCache = serviceTreeData.data;
      return this.serviceTreeCache;

    } catch (error) {
      console.error('❌ 加载服务树失败:', error);
      throw error;
    }
  }

  /**
   * 根据服务名获取服务ID
   */
  async getServiceIdByName(serviceName: string): Promise<number | null> {
    try {
      console.log(`🔍 查找服务ID: ${serviceName}`);

      const serviceTree = await this.getServiceTree();

      // 遍历所有产品线查找服务
      for (const [productLineName, productLineData] of Object.entries(serviceTree.tree)) {
        if ((productLineData as any).services) {
          const service = (productLineData as any).services.find((s: any) => s.service_name === serviceName);
          if (service) {
            console.log(`✅ 找到服务: ${serviceName} -> ID: ${service.service_id} (产品线: ${productLineName})`);
            return service.service_id;
          }
        }
      }

      console.warn(`⚠️ 未找到服务: ${serviceName}`);
      return null;

    } catch (error) {
      console.error(`❌ 获取服务ID失败: ${error.message}`);
      return null;
    }
  }

  /**
   * 加载集群映射配置 (使用本地JSON文件)
   */
  async loadClusterMapping(): Promise<any> {
    try {
      // 检查缓存
      if (this.clusterMappingCache) {
        console.log(`📋 使用缓存的集群映射数据`);
        return this.clusterMappingCache;
      }

      console.log(`🔍 开始加载本地集群映射数据...`);

      // 浏览器扩展环境：使用fetch加载JSON文件
      const configUrl = browser.runtime.getURL('config/clusterMapping.json');
      console.log(`🌐 浏览器环境：加载 ${configUrl}`);

      const response = await fetch(configUrl);
      if (!response.ok) {
        throw new Error(`无法加载集群映射文件: HTTP ${response.status}`);
      }

      const clusterMappingData = await response.json();

      // 验证数据格式
      if (!clusterMappingData || typeof clusterMappingData !== 'object') {
        throw new Error('集群映射数据格式不正确');
      }

      const clusterKeys = Object.keys(clusterMappingData);
      console.log(`✅ 成功加载本地集群映射 (${clusterKeys.length}个集群): [${clusterKeys.join(', ')}]`);

      // 缓存数据
      this.clusterMappingCache = clusterMappingData;
      return clusterMappingData;

    } catch (error) {
      console.error(`❌ 加载本地集群映射失败: ${error.message}`);
      return null;
    }
  }

  /**
   * 加载指标映射配置 (使用本地JSON文件)
   */
  async loadCollecterMapping(): Promise<any[]> {
    try {
      // 检查缓存
      if (this.collecterMappingCache) {
        console.log(`📋 使用缓存的指标映射数据`);
        return this.collecterMappingCache;
      }

      console.log(`🔍 开始加载本地指标映射数据...`);

      // 浏览器扩展环境：使用fetch加载JSON文件
      const configUrl = browser.runtime.getURL('config/collecterMapping.json');
      console.log(`🌐 浏览器环境：加载 ${configUrl}`);

      const response = await fetch(configUrl);
      if (!response.ok) {
        throw new Error(`无法加载指标映射文件: HTTP ${response.status}`);
      }

      const collecterMappingData = await response.json();

      // 验证数据格式
      if (!Array.isArray(collecterMappingData)) {
        throw new Error('指标映射数据格式不正确，期望数组格式');
      }

      console.log(`✅ 成功加载本地指标映射 (${collecterMappingData.length}个条目)`);

      // 缓存数据
      this.collecterMappingCache = collecterMappingData;
      return collecterMappingData;

    } catch (error) {
      console.error(`❌ 加载本地指标映射失败: ${error.message}`);
      return null;
    }
  }

  /**
   * 在指标映射配置中查找指标信息
   */
  async findMetricInCollecterMapping(metricName: string): Promise<CollecterInfo | null> {
    try {
      console.log(`🔍 在collecterMapping中查找指标: ${metricName}`);

      const collecterMapping = await this.loadCollecterMapping();
      if (!collecterMapping) {
        console.warn(`⚠️ 无法加载collecterMapping数据`);
        return null;
      }

      // 遍历映射数组查找匹配的指标
      for (const mappingItem of collecterMapping) {
        if (mappingItem.source && Array.isArray(mappingItem.source)) {
          // 检查source数组中是否包含目标指标名称
          if (mappingItem.source.includes(metricName)) {
            console.log(`✅ 在collecterMapping中找到指标: ${metricName}`);
            console.log(`📋 映射数据:`, mappingItem.data);

            // 返回统一格式的数据
            return {
              file_path: mappingItem.data.file_path || undefined,
              pattern: mappingItem.data.pattern || undefined,
              name: mappingItem.data.name || metricName,
              id: undefined,
              type: mappingItem.data.type || 'fallback',
              originalMetric: undefined,
              searchStrategy: 'collecter_mapping_fallback',
              source: 'collecterMapping.json'
            };
          }
        }
      }

      console.log(`❌ 在collecterMapping中未找到指标: ${metricName}`);
      return null;

    } catch (error) {
      console.error(`❌ 在collecterMapping中查找指标失败: ${error.message}`);
      return null;
    }
  }

  /**
   * 获取集群标识 - 根据解析的集群信息映射到正确的集群标识
   */
  async getClusterIdentifier(clusterKey: string): Promise<string> {
    try {
      console.log(`🔍 开始集群映射: ${clusterKey}`);

      // 1. 加载集群映射配置
      const clusterMapping = await this.loadClusterMapping();

      // 2. 遍历所有的key，检查value数组中是否有匹配的值
      const matchedKeys: string[] = [];

      for (const [targetCluster, sourceValues] of Object.entries(clusterMapping)) {
        // 检查当前clusterKey是否在sourceValues数组中
        if (Array.isArray(sourceValues) && sourceValues.includes(clusterKey)) {
          matchedKeys.push(targetCluster);
          console.log(`✅ 找到匹配: ${clusterKey} -> ${targetCluster}`);
        }
      }

      // 3. 如果找到匹配的key，用|拼接返回
      if (matchedKeys.length > 0) {
        const result = matchedKeys.join('|');
        console.log(`✅ 集群映射成功: ${clusterKey} -> ${result}`);
        return result;
      }

      // 4. 如果没有匹配，返回所有key用|拼接
      const allKeys = Object.keys(clusterMapping);
      const fallbackResult = allKeys.join('|');
      console.warn(`⚠️ 集群映射失败，使用降级策略: ${clusterKey} -> ${fallbackResult}`);
      console.warn(`   可用集群: [${allKeys.join(', ')}]`);
      console.warn(`   映射配置:`, clusterMapping);

      return fallbackResult;

    } catch (error) {
      console.error(`❌ 集群映射失败: ${error.message}`);
      return '';
    }
  }

  /**
   * 验证file_path是否为有效的非空字符串
   */
  async validateFilePathForService(filePath: string, serviceName?: string): Promise<boolean> {
    try {
      // 简化的验证逻辑：只检查file_path是否为非空字符串
      const isValid = !!(filePath && typeof filePath === 'string' && filePath.trim().length > 0);

      console.log(`🔍 验证file_path: ${filePath || '空'} -> ${isValid ? '有效' : '无效'}`);

      return isValid;

    } catch (error) {
      console.error(`❌ file_path验证失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 构建日志查询表达式
   */
  buildLogQuery(queryExpr: {
    app: string;
    cluster: string;
    ns: string;
    timestamp: string;
    tp?: string;
  }): string {
    let query = `{app="${queryExpr.app}",cluster="${queryExpr.cluster}",ns="${queryExpr.ns}",timestamp="${queryExpr.timestamp}"`;

    if (queryExpr.tp) {
      query += `,tp="${queryExpr.tp}"`;
    }

    query += '}';
    return query;
  }

  /**
   * 格式化pattern为Grafana可用的搜索条件
   */
  formatPatternForGrafana(pattern: string, currentValue?: string): string | null {
    try {
      if (!pattern) {
        console.warn('⚠️ Pattern为空，无法格式化');
        return null;
      }

      console.log(`🔧 开始格式化pattern: ${pattern}`);
      console.log(`🔧 当前值: ${currentValue || '无'}`);

      let formattedPattern = pattern;

      // 移除多余的转义字符
      formattedPattern = formattedPattern.replace(/\\\\/g, '\\');
      formattedPattern = formattedPattern.replace(/\\\"/g, '"');

      console.log(`🔧 转义处理后: ${formattedPattern}`);

      // 如果有当前值，尝试用当前值替换数字捕获组
      if (currentValue && /^[\d\.]+$/.test(currentValue.toString())) {
        const captureGroupRegex = /\([^)]*\\d[^)]*\)/g;

        if (captureGroupRegex.test(formattedPattern)) {
          captureGroupRegex.lastIndex = 0;

          const replacedPattern = formattedPattern.replace(captureGroupRegex, (match) => {
            console.log(`🔧 匹配到数字捕获组: ${match}`);
            return `(${currentValue})`;
          });

          console.log(`🔧 检测到数字捕获组，用当前值 ${currentValue} 替换`);
          console.log(`🔧 替换前: ${formattedPattern}`);
          console.log(`🔧 替换后: ${replacedPattern}`);

          formattedPattern = replacedPattern;
        } else {
          console.log(`🔧 未检测到数字捕获组，保持原pattern`);
        }
      } else {
        console.log(`🔧 无有效当前值（当前值: ${currentValue}），保持原pattern`);
      }

      // 为Grafana格式化 - 使用grep格式
      const result = `grep -P '${formattedPattern}'`;
      console.log(`🔧 最终格式化结果: ${result}`);

      return result;

    } catch (error) {
      console.error('❌ Pattern格式化失败:', error);
      return `grep -P '${pattern}'`;
    }
  }

  /**
   * 通过监控指标名称获取collecter信息
   */
  async getCollecterInfo(metricName: string, moduleName?: string, productLineName?: string): Promise<CollecterInfo> {
    try {
      console.log(`🔍 开始查询监控指标: ${metricName}`);
      console.log(`📋 模块名: ${moduleName || '未提供'}, 产品线: ${productLineName || '未提供'}`);

      // 动态构建node参数
      let nodeParam: string;
      if (moduleName && productLineName) {
        nodeParam = `ZUOYEBANG_docker_${productLineName}_${moduleName}`;
        console.log(`🔧 动态构建node参数: ${nodeParam}`);
      } else {
        // 降级策略：使用默认值
        nodeParam = 'ZUOYEBANG_docker_support_assistantdeskgo';
        console.warn(`⚠️ 缺少模块名或产品线名，使用默认node参数: ${nodeParam}`);
      }

      // 第一步：通过node/collecter接口获取所有指标列表
      const nodeUrl = `${this.falconApiBase}/node/collecter?node=${nodeParam}&nodeType=service`;
      console.log(`📡 调用node接口: ${nodeUrl}`);

      const nodeResponse = await fetch(nodeUrl, {
        method: 'GET',
        credentials: 'include', // 包含cookie进行身份验证
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      });

      if (!nodeResponse.ok) {
        throw new Error(`Node接口调用失败: ${nodeResponse.status} ${nodeResponse.statusText}`);
      }

      const nodeData = await nodeResponse.json();
      console.log(`📊 Node接口返回数据条数: ${nodeData.data?.length || 0}`);

      if (nodeData.errNo !== 0) {
        throw new Error(`Node接口返回错误: ${nodeData.errStr}`);
      }

      // 第二步：检测指标类型并实施优先查找策略
      console.log(`🔍 开始指标类型检测: ${metricName}`);

      // 使用parseOriginalMetricName判断是否为汇聚指标
      const originalMetricName = this.parseOriginalMetricName(metricName);
      const isAggregatedMetric = originalMetricName !== metricName;

      console.log(`📊 指标类型分析:`, {
        inputMetric: metricName,
        originalMetric: originalMetricName,
        isAggregated: isAggregatedMetric
      });

      let currentMetricItem: any = null;
      let fallbackToOriginal = false;

      if (isAggregatedMetric) {
        console.log(`🔍 检测到汇聚指标，开始优先查找策略...`);

        // 优先查找汇聚指标本身
        currentMetricItem = nodeData.data.find((item: any) => item.name === metricName);

        if (currentMetricItem) {
          console.log(`✅ 找到汇聚指标: ${metricName} (ID: ${currentMetricItem.id}, Type: ${currentMetricItem.type})`);
        } else {
          console.warn(`⚠️ 未找到汇聚指标 ${metricName}，尝试查找原始指标 ${originalMetricName}`);

          // 回退到查找原始指标
          currentMetricItem = nodeData.data.find((item: any) =>
            item.name === originalMetricName && (item.type === 'log' || item.type === 'original' || item.type === 'custom')
          );

          if (currentMetricItem) {
            console.log(`✅ 回退成功，找到原始指标: ${originalMetricName} (ID: ${currentMetricItem.id}, Type: ${currentMetricItem.type})`);
            fallbackToOriginal = true;
          } else {
            console.error(`❌ 汇聚指标和原始指标都未找到: ${metricName} -> ${originalMetricName}`);
          }
        }
      } else {
        console.log(`🔍 检测到原始指标，直接查找...`);

        // 直接查找原始指标
        currentMetricItem = nodeData.data.find((item: any) => item.name === metricName);

        if (currentMetricItem) {
          console.log(`✅ 找到原始指标: ${metricName} (ID: ${currentMetricItem.id}, Type: ${currentMetricItem.type})`);
        } else {
          console.error(`❌ 未找到原始指标: ${metricName}`);
        }
      }

      // 如果所有查找策略都失败，尝试降级到collecterMapping.json
      if (!currentMetricItem) {
        const errorMsg = isAggregatedMetric
          ? `未找到汇聚指标 ${metricName} 及其对应的原始指标 ${originalMetricName}`
          : `未找到监控指标 ${metricName}`;

        console.warn(`⚠️ ${errorMsg}，尝试降级查找...`);

        // 降级策略：从collecterMapping.json查找
        console.log(`🔄 开始降级查找策略...`);

        // 首先尝试查找原始指标名称
        let fallbackResult = await this.findMetricInCollecterMapping(metricName);

        // 如果是汇聚指标且未找到，尝试查找原始指标
        if (!fallbackResult && isAggregatedMetric) {
          console.log(`🔄 汇聚指标降级查找失败，尝试查找原始指标: ${originalMetricName}`);
          fallbackResult = await this.findMetricInCollecterMapping(originalMetricName);
        }

        if (fallbackResult) {
          console.log(`✅ 降级查找成功，从collecterMapping.json获取指标信息`);
          return fallbackResult;
        } else {
          console.error(`❌ 降级查找也失败，所有数据源都无法找到指标: ${metricName}`);

          // 最终失败，返回空结果
          return {
            file_path: undefined,
            pattern: undefined,
            id: undefined,
            name: metricName,
            type: 'unknown',
            originalMetric: undefined,
            searchStrategy: isAggregatedMetric ? 'aggregated_fallback_failed' : 'direct_failed',
            error: `所有数据源都无法找到指标: ${metricName}`
          };
        }
      }

      console.log(`✅ 指标查找完成，ID: ${currentMetricItem.id}, Type: ${currentMetricItem.type}`);

      // 第三步：确定目标指标用于获取详细信息
      let targetMetricItem = currentMetricItem; // 默认使用找到的指标
      let originalMetricItem = null;

      // 如果当前找到的指标是通过回退策略找到的原始指标，需要特殊处理
      if (fallbackToOriginal) {
        console.log(`🔄 使用回退策略找到的原始指标，直接使用该指标获取详细信息`);
        targetMetricItem = currentMetricItem;
        originalMetricItem = currentMetricItem; // 标记为原始指标
      } else if (currentMetricItem.type === 'aggr' && !fallbackToOriginal) {
        console.log(`🔍 找到汇聚指标，尝试查找对应的原始指标以获取更好的pattern...`);

        // 在同一个指标列表中查找原始指标
        const foundOriginalMetric = nodeData.data.find((item: any) =>
          item.name === originalMetricName && (item.type === 'log' || item.type === 'original' || item.type === 'custom')
        );

        if (foundOriginalMetric) {
          console.log(`✅ 找到对应的原始指标: ${foundOriginalMetric.name} (ID: ${foundOriginalMetric.id})`);
          targetMetricItem = foundOriginalMetric; // 使用原始指标获取详细信息
          originalMetricItem = foundOriginalMetric;
        } else {
          console.warn(`⚠️ 未找到对应的原始指标: ${originalMetricName}，使用汇聚指标`);
          targetMetricItem = currentMetricItem;
        }
      } else {
        console.log(`ℹ️ 使用找到的指标 (type: ${currentMetricItem.type})`);
      }

      // 第四步：调用目标指标的详情接口获取完整配置信息
      const detailUrl = `${this.falconApiBase}/collecter?id=${targetMetricItem.id}`;
      console.log(`📡 调用详情接口: ${detailUrl} (指标: ${targetMetricItem.name})`);

      const detailResponse = await fetch(detailUrl, {
        method: 'GET',
        credentials: 'include', // 包含cookie进行身份验证
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      });

      if (!detailResponse.ok) {
        throw new Error(`详情接口调用失败: ${detailResponse.status} ${detailResponse.statusText}`);
      }

      const detailData = await detailResponse.json();

      if (detailData.errNo !== 0) {
        throw new Error(`详情接口返回错误: ${detailData.errStr}`);
      }

      console.log(`✅ 成功获取指标详情:`, {
        id: detailData.data.id,
        name: detailData.data.name,
        file_path: detailData.data.file_path,
        pattern: detailData.data.pattern || '无',
        patternLength: detailData.data.pattern ? detailData.data.pattern.length : 0
      });

      // 处理file_path字段
      let filePath = detailData.data.file_path;

      // 清理file_path中的多余空格
      if (filePath) {
        filePath = filePath.trim();
      }

      console.log(`📋 获取到file_path: ${filePath || '无'}`);

      // 第五步：验证file_path是否为有效的非空字符串
      const isFilePathValid = await this.validateFilePathForService(filePath, moduleName);
      if (!isFilePathValid) {
        console.log(`🔍 file_path为空或无效，将移除tp参数`);
        filePath = undefined; // 当file_path为空时，移除tp参数
      } else {
        console.log(`✅ file_path验证通过: ${filePath}`);
      }

      // 第六步：构建返回结果
      const result: CollecterInfo = {
        // 当前指标的基础信息（从第一步获取）
        id: currentMetricItem.id,
        name: currentMetricItem.name,
        type: currentMetricItem.type,
        // 详细配置信息（从第四步获取，可能是原始指标的信息）
        file_path: filePath,
        pattern: detailData.data.pattern,
        originalMetric: undefined
      };

      // 如果当前指标是汇聚指标且找到了原始指标，则在originalMetric中包含原始指标信息
      if (currentMetricItem.type === 'aggr' && originalMetricItem && targetMetricItem === originalMetricItem) {
        result.originalMetric = {
          id: detailData.data.id,
          name: detailData.data.name,
          file_path: filePath,
          pattern: detailData.data.pattern,
          type: detailData.data.type || 'log'
        };
        console.log(`✅ 构建结果完成，包含原始指标信息: ${result.originalMetric.name}`);
      } else {
        console.log(`✅ 构建结果完成，当前指标类型: ${result.type}`);
      }

      return result;

    } catch (error) {
      console.error('❌ 获取collecter信息失败:', error);

      // 在API调用异常时也尝试降级查找
      console.log(`🔄 API调用异常，尝试降级查找策略...`);

      try {
        // 使用parseOriginalMetricName判断是否为汇聚指标
        const originalMetricName = this.parseOriginalMetricName(metricName);
        const isAggregatedMetric = originalMetricName !== metricName;

        // 首先尝试查找原始指标名称
        let fallbackResult = await this.findMetricInCollecterMapping(metricName);

        // 如果是汇聚指标且未找到，尝试查找原始指标
        if (!fallbackResult && isAggregatedMetric) {
          console.log(`🔄 汇聚指标降级查找失败，尝试查找原始指标: ${originalMetricName}`);
          fallbackResult = await this.findMetricInCollecterMapping(originalMetricName);
        }

        if (fallbackResult) {
          console.log(`✅ 异常情况下降级查找成功，从collecterMapping.json获取指标信息`);

          // 添加API错误信息到降级结果中
          fallbackResult.apiError = error instanceof Error ? error.message : '未知错误';
          fallbackResult.searchStrategy = 'api_exception_fallback';

          return fallbackResult;
        } else {
          console.error(`❌ 异常情况下降级查找也失败，所有数据源都无法找到指标: ${metricName}`);
        }
      } catch (fallbackError) {
        console.error(`❌ 降级查找过程中发生错误: ${fallbackError instanceof Error ? fallbackError.message : '未知错误'}`);
      }

      // 最终失败，返回默认值
      return {
        file_path: undefined, // 错误情况下不提供file_path
        pattern: undefined,
        id: undefined,
        name: metricName,
        type: 'unknown',
        originalMetric: undefined,
        error: error instanceof Error ? error.message : '未知错误',
        fallbackAttempted: true
      };
    }
  }

  /**
   * 生成Grafana查询URL
   */
  async generateGrafanaUrl(parsedAlert: ParsedAlert, usePatternSearch = false, customSearchCondition?: string, ignoreFilePath = false, ignoreCluster = false): Promise<GrafanaUrlResult> {
    try {
      if (!parsedAlert.hostInfo || !parsedAlert.alertTime) {
        throw new Error('缺少必要的告警信息');
      }

      const timeRange = this.calculateTimeRange(parsedAlert.alertTime);
      const hostInfo = parsedAlert.hostInfo;

      // 获取collecter信息 (包含file_path和pattern)
      let collecterInfo: CollecterInfo = {
        file_path: undefined,
        pattern: undefined,
        name: 'unknown',
        type: 'unknown'
      };

      if (parsedAlert.metricInfo && parsedAlert.metricInfo.metricName) {
        // 传递模块名和产品线名以支持动态构建node参数和file_path验证
        const moduleName = hostInfo.moduleName;      // assistantdeskgo (模块名)
        const productLineName = hostInfo.serviceName; // support (产品线名)
        collecterInfo = await this.getCollecterInfo(parsedAlert.metricInfo.metricName, moduleName, productLineName);
        console.log(`📋 获取到collecter信息:`, {
          file_path: collecterInfo.file_path || '无',
          pattern: collecterInfo.pattern || '无',
          patternLength: collecterInfo.pattern ? collecterInfo.pattern.length : 0,
          hasError: !!collecterInfo.error,
          moduleName: moduleName,
          productLineName: productLineName,
          nodeParam: `ZUOYEBANG_docker_${productLineName}_${moduleName}`
        });
      }

      // 获取集群标识
      const clusterIdentifier = await this.getClusterIdentifier(hostInfo.clusterKey);
      console.log(`🏷️ 集群映射结果: ${hostInfo.clusterKey} -> ${clusterIdentifier}`);

      // 将集群标识设置到hostInfo中，用于summary显示
      parsedAlert.hostInfo.fullCluster = clusterIdentifier;

      // 构建基础查询表达式
      const queryExpr = {
        app: hostInfo.moduleName,           // assistantdeskgo
        cluster: '',                        // 将在下面根据ignoreCluster设置
        ns: hostInfo.serviceName,           // support
        timestamp: timeRange.endTimestamp.toString()
      };

      // 根据ignoreCluster参数决定cluster参数值
      if (ignoreCluster) {
        // 查询所有集群
        queryExpr.cluster = 'k8s.online.tx.bj6|k8s.online.ali.bjh|k8s.stable.tx.bj|k8s.stable.ali.bj|k8s.small.tx.bj';
        console.log(`🔓 忽略集群限制，查询所有集群: ${queryExpr.cluster}`);
      } else {
        queryExpr.cluster = clusterIdentifier;  // 映射后的集群标识或降级的多集群标识
        console.log(`🔗 构建查询表达式，cluster参数: ${clusterIdentifier}`);
      }

      // 根据ignoreFilePath参数决定是否添加tp参数
      if (ignoreFilePath) {
        console.log(`🔓 忽略日志文件限制，不添加tp参数`);
      } else if (collecterInfo.file_path) {
        queryExpr.tp = collecterInfo.file_path;
        console.log(`🔗 构建查询表达式，tp参数: ${collecterInfo.file_path}`);
      } else {
        console.log(`🔗 构建查询表达式，无tp参数 (file_path为空)`);
      }

      const baseQuery = this.buildLogQuery(queryExpr);

      // 确定搜索条件 - 优先使用自定义搜索条件
      let searchCondition: string | null = null;
      let searchType = 'none';

      console.log(`🔍 搜索条件判断:`, {
        usePatternSearch,
        hasPattern: !!collecterInfo.pattern,
        patternValue: collecterInfo.pattern,
        customSearchCondition
      });

      if (customSearchCondition) {
        // 优先使用自定义搜索条件 - 格式与期望的URL一致
        searchCondition = `grep -P '${customSearchCondition}'`;
        searchType = 'custom';
        console.log(`🔍 使用自定义搜索: ${searchCondition}`);
      } else if (collecterInfo.pattern) {
        // 如果没有自定义搜索条件，才使用pattern作为搜索条件
        console.log(`📋 原始pattern: ${collecterInfo.pattern}`);
        console.log(`📋 当前值: ${parsedAlert.currentValue || '无'}`);
        searchCondition = this.formatPatternForGrafana(collecterInfo.pattern, parsedAlert.currentValue);
        searchType = 'pattern';
        console.log(`🔍 自动使用pattern搜索: ${searchCondition}`);
      } else {
        console.log(`🔍 无搜索条件，使用基础查询`);
      }

      // 构建Grafana查询参数
      let leftParam: any[];

      if (searchCondition) {
        // 注意：搜索条件需要直接拼接到基础查询后面，没有空格
        const fullQuery = `${baseQuery}${searchCondition}`;
        console.log(`🔗 构建完整查询: ${fullQuery}`);
        leftParam = [
          timeRange.startTimeStr,
          timeRange.endTimeStr,
          "LogSearch",
          {
            expr: fullQuery,
            queryText: baseQuery,
            value: searchCondition
          }
        ];
      } else {
        console.log(`🔗 构建基础查询: ${baseQuery}`);
        leftParam = [
          timeRange.startTimeStr,
          timeRange.endTimeStr,
          "LogSearch",
          {
            expr: baseQuery,
            queryText: baseQuery,
            value: ""
          }
        ];
      }

      // 生成完整URL
      let url = `${this.grafanaBaseUrl}?orgId=${this.orgId}&left=${encodeURIComponent(JSON.stringify(leftParam))}`;

      // 如果有搜索条件，添加URL片段
      if (searchCondition) {
        url += `#${encodeURIComponent(searchCondition)}`;
      }

      console.log(`🌐 生成的Grafana URL: ${url.substring(0, 200)}...`);
      console.log(`📊 URL参数详情:`, {
        baseQuery,
        searchCondition,
        searchType,
        hasPattern: !!collecterInfo.pattern
      });

      return {
        url: url,
        collecterInfo: collecterInfo,
        searchType: searchType,
        searchCondition: searchCondition || undefined,
        baseQuery: baseQuery
      };
    } catch (error) {
      throw new Error(`Grafana URL生成失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }
}
