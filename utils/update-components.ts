/**
 * 更新相关UI组件
 */
import { Modal } from './ui-components';
import { UpdateCheckResult } from './version-manager';
import { updateManager } from './update-manager';
import { notificationManager } from './notification-manager';

export class UpdateDetailModal {
  private modal: Modal;

  constructor(updateResults: Map<string, UpdateCheckResult>) {
    const content = this.createUpdateContent(updateResults);
    this.modal = new Modal(content, {
      title: '工具更新详情',
      size: 'lg',
      closable: true,
      backdrop: true,
      keyboard: true
    });

    this.addFooterButtons();
    this.bindEvents(updateResults);
  }

  /**
   * 创建更新内容
   */
  private createUpdateContent(updateResults: Map<string, UpdateCheckResult>): HTMLElement {
    const container = document.createElement('div');
    container.className = 'update-detail-container';

    updateResults.forEach((result, toolId) => {
      if (result.hasUpdate) {
        const updateItem = this.createUpdateItem(toolId, result);
        container.appendChild(updateItem);
      }
    });

    return container;
  }

  /**
   * 创建单个更新项
   */
  private createUpdateItem(toolId: string, result: UpdateCheckResult): HTMLElement {
    const item = document.createElement('div');
    item.className = 'update-item';
    item.dataset.toolId = toolId;

    const currentVersion = this.formatVersion(result.currentVersion);
    const latestVersion = result.latestVersion ? this.formatVersion(result.latestVersion) : '未知';
    
    const priorityClass = result.priority || 'medium';
    const priorityText = {
      low: '低',
      medium: '中',
      high: '高',
      critical: '紧急'
    }[priorityClass] || '中';

    const securityBadge = '';

    const sizeInfo = '';

    item.innerHTML = `
      <div class="update-item-header">
        <h4 class="update-item-title">${toolId}</h4>
        <div class="update-item-meta">
          <span class="update-priority priority-${priorityClass}">${priorityText}优先级</span>
          ${securityBadge}
          ${sizeInfo}
        </div>
      </div>
      <div class="update-item-version">
        <span class="version-current">v${currentVersion}</span>
        <span class="version-arrow">→</span>
        <span class="version-latest">v${latestVersion}</span>
        <span class="update-type">${this.getUpdateTypeText(result.updateType)}</span>
      </div>
      <div class="update-item-content">
        <div class="update-changelog">
          ${this.formatChangelog(result.changelog)}
        </div>
        ${result.releaseDate ? `
          <div class="update-date">
            <span class="date-label">发布时间:</span>
            <span class="date-value">${this.formatDate(result.releaseDate)}</span>
          </div>
        ` : ''}
      </div>
    `;

    return item;
  }

  /**
   * 添加底部按钮
   */
  private addFooterButtons(): void {
    const footerContent = `
      <button class="btn btn-secondary close-btn">关闭</button>
      <button class="btn btn-ghost ignore-all-btn">忽略所有</button>
      <button class="btn btn-primary full-update-btn">
        <span class="btn-icon">📦</span>
        全量更新
      </button>
    `;
    this.modal.addFooter(footerContent);
  }

  /**
   * 绑定事件
   */
  private bindEvents(updateResults: Map<string, UpdateCheckResult>): void {
    const modalElement = this.modal.getElement();

    // 关闭按钮
    modalElement.addEventListener('click', (e) => {
      const target = e.target as HTMLElement;
      if (target.classList.contains('close-btn')) {
        this.hide();
      }
    });

    // 全量更新按钮
    modalElement.addEventListener('click', (e) => {
      const target = e.target as HTMLElement;
      if (target.classList.contains('full-update-btn')) {
        this.handleFullUpdate(updateResults);
      }
    });

    // 忽略所有更新
    modalElement.addEventListener('click', async (e) => {
      const target = e.target as HTMLElement;
      if (target.classList.contains('ignore-all-btn')) {
        for (const toolId of updateResults.keys()) {
          await updateManager.ignoreUpdate(toolId);
        }
        this.hide();
        notificationManager.success('已忽略所有更新');
      }
    });




  }

  /**
   * 处理全量更新
   */
  private handleFullUpdate(updateResults: Map<string, UpdateCheckResult>): void {
    // 检查是否有可用更新
    const hasUpdates = Array.from(updateResults.values()).some(result => result.hasUpdate);

    if (!hasUpdates) {
      notificationManager.info('暂无可用更新');
      return;
    }

    // 扩展下载地址（根据实际情况修改）
    const downloadUrl = 'https://assistantdesk.zuoyebang.cc/fwyytool/index';

    // 直接跳转到扩展下载地址
    window.open(downloadUrl, '_blank');

    // 关闭当前模态框
    this.hide();

    notificationManager.success('已跳转到扩展下载页面');
  }

  /**
   * 创建全量更新页面内容（已废弃，保留用于向后兼容）
   */
  private createFullUpdatePage(updateLinks: Array<{toolId: string, url: string, version: string}>): string {
    const linksList = updateLinks.map(link => `
      <div class="update-link-item">
        <h3>${link.toolId}</h3>
        <p>版本: v${link.version}</p>
        <a href="${link.url}" target="_blank" class="download-link">下载更新包</a>
      </div>
    `).join('');

    return `
      <!DOCTYPE html>
      <html lang="zh-CN">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>fwyy-tools 全量更新</title>
        <style>
          body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            background: #f8fafc;
          }
          .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
          }
          .update-link-item {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
          }
          .update-link-item h3 {
            margin: 0 0 10px 0;
            color: #1e293b;
          }
          .update-link-item p {
            margin: 0 0 15px 0;
            color: #64748b;
          }
          .download-link {
            display: inline-block;
            background: #3b82f6;
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 6px;
            font-weight: 500;
            transition: background-color 0.2s;
          }
          .download-link:hover {
            background: #2563eb;
          }
          .instructions {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
          }
          .instructions h4 {
            margin: 0 0 10px 0;
            color: #92400e;
          }
          .instructions p {
            margin: 0;
            color: #92400e;
          }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>🔄 fwyy-tools 全量更新</h1>
          <p>请下载以下工具的更新包，然后重新安装扩展</p>
        </div>

        <div class="update-links">
          ${linksList}
        </div>

        <div class="instructions">
          <h4>📋 更新说明</h4>
          <p>1. 点击上方链接下载所需的工具更新包</p>
          <p>2. 下载完成后，请重新安装 fwyy-tools 扩展</p>
          <p>3. 安装完成后，所有工具将更新到最新版本</p>
        </div>
      </body>
      </html>
    `;
  }

  /**
   * 显示下载链接列表（已废弃，保留用于向后兼容）
   */
  private showDownloadLinks(updateLinks: Array<{toolId: string, url: string, version: string}>): void {
    const linksList = updateLinks.map(link =>
      `• ${link.toolId} v${link.version}: ${link.url}`
    ).join('\n');

    const message = `全量更新下载链接：\n\n${linksList}\n\n请复制链接到浏览器中下载更新包。`;

    // 创建一个简单的文本显示模态框
    const textModal = new Modal(`
      <div style="white-space: pre-wrap; font-family: monospace; font-size: 12px; line-height: 1.5;">
        ${message}
      </div>
    `, {
      title: '全量更新下载链接',
      size: 'lg',
      closable: true
    });

    textModal.open();
  }



  /**
   * 格式化版本号
   */
  private formatVersion(version: any): string {
    return `${version.major}.${version.minor}.${version.patch}`;
  }

  /**
   * 格式化更新日志
   */
  private formatChangelog(changelog?: string): string {
    if (!changelog) {
      return '<p>性能优化和问题修复</p>';
    }
    
    // 如果是简单字符串，转换为段落
    if (typeof changelog === 'string') {
      return changelog.split('\n').map(line => 
        line.trim() ? `<p>${line.trim()}</p>` : ''
      ).join('');
    }
    
    return '<p>性能优化和问题修复</p>';
  }

  /**
   * 格式化文件大小
   */
  private formatFileSize(bytes: number): string {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }
    
    return `${size.toFixed(1)} ${units[unitIndex]}`;
  }

  /**
   * 格式化日期
   */
  private formatDate(dateString: string): string {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    } catch {
      return dateString;
    }
  }

  /**
   * 获取更新类型文本
   */
  private getUpdateTypeText(updateType?: string): string {
    const typeMap = {
      major: '重大更新',
      minor: '功能更新', 
      patch: '修复更新',
      hotfix: '热修复'
    };
    return typeMap[updateType as keyof typeof typeMap] || '常规更新';
  }

  show(): void {
    this.modal.open();
  }

  hide(): void {
    this.modal.close();
  }
}

/**
 * 更新横幅组件
 */
export class UpdateBanner {
  private element: HTMLElement;

  constructor(updateCount: number) {
    this.element = this.createElement(updateCount);
    this.bindEvents();
  }

  private createElement(updateCount: number): HTMLElement {
    const banner = document.createElement('div');
    banner.className = 'update-banner';
    banner.innerHTML = `
      <div class="update-banner-content">
        <div class="update-banner-icon">🔔</div>
        <div class="update-banner-text">
          <span class="update-banner-title">发现 ${updateCount} 个工具更新</span>
          <span class="update-banner-subtitle">点击查看详情并下载</span>
        </div>
        <div class="update-banner-actions">
          <button class="btn btn-sm btn-primary details-btn">查看详情</button>
          <button class="btn btn-sm btn-ghost dismiss-btn">稍后提醒</button>
          <button class="btn btn-sm btn-ghost close-btn">×</button>
        </div>
      </div>
    `;
    return banner;
  }

  private bindEvents(): void {
    // 查看详情
    const detailsBtn = this.element.querySelector('.details-btn');
    detailsBtn?.addEventListener('click', () => {
      const event = new CustomEvent('showUpdateDetails');
      document.dispatchEvent(event);
    });

    // 稍后提醒
    const dismissBtn = this.element.querySelector('.dismiss-btn');
    dismissBtn?.addEventListener('click', () => {
      this.dismiss();
    });

    // 关闭
    const closeBtn = this.element.querySelector('.close-btn');
    closeBtn?.addEventListener('click', () => {
      this.hide();
    });

    // 点击文本区域也可以查看详情
    const textArea = this.element.querySelector('.update-banner-text');
    textArea?.addEventListener('click', () => {
      const event = new CustomEvent('showUpdateDetails');
      document.dispatchEvent(event);
    });
  }

  private async dismiss(): Promise<void> {
    // 暂时关闭所有更新通知
    await updateManager.dismissAllUpdates();
    this.hide();
  }

  show(container: HTMLElement): void {
    container.insertBefore(this.element, container.firstChild);
  }

  hide(): void {
    if (this.element.parentNode) {
      this.element.parentNode.removeChild(this.element);
    }
  }

  getElement(): HTMLElement {
    return this.element;
  }
}
