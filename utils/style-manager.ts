/**
 * 样式管理器
 * 支持模块化样式加载和主题管理
 */

export interface StyleModule {
  name: string;
  path: string;
  dependencies?: string[];
  loaded?: boolean;
}

export interface ThemeConfig {
  name: string;
  variables: Record<string, string>;
}

/**
 * 样式管理器类
 */
export class StyleManager {
  private static instance: StyleManager;
  private loadedModules: Set<string> = new Set();
  private loadingModules: Set<string> = new Set();
  private styleElements: Map<string, HTMLStyleElement> = new Map();
  private currentTheme: string = 'auto';

  // 预定义的样式模块
  private modules: Map<string, StyleModule> = new Map([
    ['design-tokens', { name: 'design-tokens', path: '/styles/design-tokens.css' }],
    ['components', { name: 'components', path: '/styles/components.css', dependencies: ['design-tokens'] }],
    ['layout', { name: 'layout', path: '/styles/layout.css', dependencies: ['design-tokens'] }],
    ['utilities', { name: 'utilities', path: '/styles/utilities.css', dependencies: ['design-tokens'] }],
    ['animations', { name: 'animations', path: '/styles/animations.css' }],
    ['modal', { name: 'modal', path: '/styles/components/modal.css', dependencies: ['components'] }],
    ['form', { name: 'form', path: '/styles/components/form.css', dependencies: ['components'] }],
    ['button', { name: 'button', path: '/styles/components/button.css', dependencies: ['components'] }],
    ['card', { name: 'card', path: '/styles/components/card.css', dependencies: ['components'] }],
    ['toast', { name: 'toast', path: '/styles/components/toast.css', dependencies: ['components'] }],
    ['dropdown', { name: 'dropdown', path: '/styles/components/dropdown.css', dependencies: ['components'] }]
  ]);

  private constructor() {
    this.init();
  }

  static getInstance(): StyleManager {
    if (!StyleManager.instance) {
      StyleManager.instance = new StyleManager();
    }
    return StyleManager.instance;
  }

  private init(): void {
    // 自动加载核心样式
    this.loadModule('design-tokens');
    
    // 监听主题变化
    this.watchThemeChanges();
  }

  /**
   * 注册样式模块
   */
  registerModule(module: StyleModule): void {
    this.modules.set(module.name, module);
  }

  /**
   * 加载样式模块
   */
  async loadModule(moduleName: string): Promise<void> {
    if (this.loadedModules.has(moduleName)) {
      return; // 已加载
    }

    if (this.loadingModules.has(moduleName)) {
      // 正在加载，等待完成
      return new Promise((resolve) => {
        const checkLoaded = () => {
          if (this.loadedModules.has(moduleName)) {
            resolve();
          } else {
            setTimeout(checkLoaded, 10);
          }
        };
        checkLoaded();
      });
    }

    const module = this.modules.get(moduleName);
    if (!module) {
      throw new Error(`样式模块 ${moduleName} 不存在`);
    }

    this.loadingModules.add(moduleName);

    try {
      // 先加载依赖
      if (module.dependencies) {
        await Promise.all(
          module.dependencies.map(dep => this.loadModule(dep))
        );
      }

      // 加载模块样式
      await this.loadStyleSheet(module);
      
      this.loadedModules.add(moduleName);
      module.loaded = true;
      
      console.log(`✅ 样式模块 ${moduleName} 加载成功`);
    } catch (error) {
      console.error(`❌ 样式模块 ${moduleName} 加载失败:`, error);
      throw error;
    } finally {
      this.loadingModules.delete(moduleName);
    }
  }

  /**
   * 批量加载样式模块
   */
  async loadModules(moduleNames: string[]): Promise<void> {
    await Promise.all(moduleNames.map(name => this.loadModule(name)));
  }

  /**
   * 卸载样式模块
   */
  unloadModule(moduleName: string): void {
    const styleElement = this.styleElements.get(moduleName);
    if (styleElement && styleElement.parentNode) {
      styleElement.parentNode.removeChild(styleElement);
      this.styleElements.delete(moduleName);
      this.loadedModules.delete(moduleName);
      
      const module = this.modules.get(moduleName);
      if (module) {
        module.loaded = false;
      }
      
      console.log(`🗑️ 样式模块 ${moduleName} 已卸载`);
    }
  }

  /**
   * 加载样式表
   */
  private async loadStyleSheet(module: StyleModule): Promise<void> {
    return new Promise((resolve, reject) => {
      // 检查是否是内联样式
      if (module.path.startsWith('data:') || module.path.includes('inline:')) {
        const style = document.createElement('style');
        style.setAttribute('data-module', module.name);
        style.textContent = module.path.replace('inline:', '');
        document.head.appendChild(style);
        this.styleElements.set(module.name, style);
        resolve();
        return;
      }

      // 加载外部样式文件
      const link = document.createElement('link');
      link.rel = 'stylesheet';
      // 使用chrome.runtime.getURL获取正确的扩展资源URL
      link.href = chrome.runtime.getURL(module.path.startsWith('/') ? module.path.slice(1) : module.path);
      link.setAttribute('data-module', module.name);
      
      link.onload = () => {
        this.styleElements.set(module.name, link as any);
        resolve();
      };
      
      link.onerror = () => {
        reject(new Error(`无法加载样式文件: ${module.path}`));
      };
      
      document.head.appendChild(link);
    });
  }

  /**
   * 设置主题
   */
  setTheme(theme: 'light' | 'dark' | 'auto'): void {
    this.currentTheme = theme;
    const root = document.documentElement;
    
    if (theme === 'auto') {
      root.removeAttribute('data-theme');
    } else {
      root.setAttribute('data-theme', theme);
    }
    
    // 触发主题变化事件
    document.dispatchEvent(new CustomEvent('theme-changed', {
      detail: { theme }
    }));
    
    console.log(`🎨 主题已切换到: ${theme}`);
  }

  /**
   * 获取当前主题
   */
  getCurrentTheme(): string {
    return this.currentTheme;
  }

  /**
   * 监听系统主题变化
   */
  private watchThemeChanges(): void {
    if (window.matchMedia) {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      
      const handleChange = (e: MediaQueryListEvent) => {
        if (this.currentTheme === 'auto') {
          document.dispatchEvent(new CustomEvent('theme-changed', {
            detail: { theme: 'auto', systemTheme: e.matches ? 'dark' : 'light' }
          }));
        }
      };
      
      mediaQuery.addEventListener('change', handleChange);
    }
  }

  /**
   * 动态添加CSS变量
   */
  setCSSVariable(name: string, value: string, scope: HTMLElement = document.documentElement): void {
    scope.style.setProperty(name.startsWith('--') ? name : `--${name}`, value);
  }

  /**
   * 获取CSS变量值
   */
  getCSSVariable(name: string, scope: HTMLElement = document.documentElement): string {
    return getComputedStyle(scope).getPropertyValue(name.startsWith('--') ? name : `--${name}`).trim();
  }

  /**
   * 批量设置CSS变量
   */
  setCSSVariables(variables: Record<string, string>, scope: HTMLElement = document.documentElement): void {
    Object.entries(variables).forEach(([name, value]) => {
      this.setCSSVariable(name, value, scope);
    });
  }

  /**
   * 应用自定义主题
   */
  applyCustomTheme(theme: ThemeConfig): void {
    this.setCSSVariables(theme.variables);
    console.log(`🎨 自定义主题 ${theme.name} 已应用`);
  }

  /**
   * 获取已加载的模块列表
   */
  getLoadedModules(): string[] {
    return Array.from(this.loadedModules);
  }

  /**
   * 检查模块是否已加载
   */
  isModuleLoaded(moduleName: string): boolean {
    return this.loadedModules.has(moduleName);
  }

  /**
   * 获取所有可用模块
   */
  getAvailableModules(): StyleModule[] {
    return Array.from(this.modules.values());
  }

  /**
   * 预加载关键样式模块
   */
  async preloadCriticalModules(): Promise<void> {
    const criticalModules = ['design-tokens', 'components', 'layout'];
    await this.loadModules(criticalModules);
  }

  /**
   * 懒加载样式模块
   */
  async lazyLoadModule(moduleName: string, condition: () => boolean): Promise<void> {
    if (condition()) {
      await this.loadModule(moduleName);
    }
  }

  /**
   * 清理未使用的样式模块
   */
  cleanup(): void {
    // 这里可以实现清理逻辑，比如卸载长时间未使用的模块
    console.log('🧹 样式管理器清理完成');
  }
}

// 导出单例实例
export const styleManager = StyleManager.getInstance();

// 工具函数
export const StyleUtils = {
  /**
   * 为工具加载必要的样式模块
   */
  async loadToolStyles(toolType: string): Promise<void> {
    const moduleMap: Record<string, string[]> = {
      'ui': ['components', 'modal', 'form', 'button'],
      'api': ['components', 'form', 'button'],
      'content': ['components', 'button'],
      'basic': ['components', 'button']
    };

    const modules = moduleMap[toolType] || ['components'];
    await styleManager.loadModules(modules);
  },

  /**
   * 创建主题切换器
   */
  createThemeToggle(): HTMLElement {
    const toggle = document.createElement('button');
    toggle.className = 'btn btn-ghost btn-sm';
    toggle.innerHTML = '🌙';
    toggle.title = '切换主题';
    
    toggle.addEventListener('click', () => {
      const currentTheme = styleManager.getCurrentTheme();
      const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
      styleManager.setTheme(newTheme);
      toggle.innerHTML = newTheme === 'dark' ? '☀️' : '🌙';
    });
    
    return toggle;
  },

  /**
   * 检测系统主题偏好
   */
  getSystemTheme(): 'light' | 'dark' {
    if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
      return 'dark';
    }
    return 'light';
  }
};
