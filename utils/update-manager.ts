/**
 * 更新管理器 - 扩展现有版本管理功能
 */
import { VersionManager, UpdateCheckResult } from './version-manager';
import { Tool, ToolVersion, VersionUtils } from './tool-template';
import { settingsManager } from './settings-manager';

export interface UpdateNotificationOptions {
  showBanner: boolean;
  showBadge: boolean;
  autoCheck: boolean;
  checkInterval: number; // 小时
}

export interface UpdateState {
  hasUpdate: boolean;
  updateInfo?: UpdateCheckResult;
  lastNotified?: number;
  userIgnored?: boolean; // 保持向后兼容性，将逐步废弃
  userDismissed?: boolean;
  ignoredVersion?: ToolVersion; // 新增：被忽略的具体版本号
}

export class UpdateManager {
  private static instance: UpdateManager;
  private versionManager: VersionManager;
  private updateStates = new Map<string, UpdateState>();
  private notificationOptions: UpdateNotificationOptions = {
    showBanner: true,
    showBadge: true,
    autoCheck: true,
    checkInterval: 30
  };

  static getInstance(): UpdateManager {
    if (!UpdateManager.instance) {
      UpdateManager.instance = new UpdateManager();
    }
    return UpdateManager.instance;
  }

  constructor() {
    this.versionManager = VersionManager.getInstance();
    // 延迟加载通知选项，避免循环依赖
  }

  /**
   * 确保通知选项已加载（延迟加载以避免循环依赖）
   */
  private ensureNotificationOptionsLoaded(): void {
    // 检查是否需要加载通知选项
    if (this.notificationOptions.autoCheck === true &&
        this.notificationOptions.checkInterval === 30 &&
        this.notificationOptions.showBanner === true) {
      // 这些是默认值，说明还没有从设置中加载过
      this.loadNotificationOptions();
    }
  }

  /**
   * 加载通知选项
   */
  private loadNotificationOptions(): void {
    try {
      this.notificationOptions = {
        showBanner: true, // 始终显示横幅通知
        showBadge: true, // 始终显示徽章
        autoCheck: settingsManager.getSetting('autoCheckUpdates'),
        checkInterval: settingsManager.getSetting('updateCheckInterval')
      };
    } catch (error) {
      console.warn('加载通知选项失败，使用默认值:', error);
      // 保持默认值
    }
  }

  /**
   * 检查更新并处理通知
   */
  async checkAndNotifyUpdates(tools: Tool[], forceCheck: boolean = false): Promise<void> {
    // 确保通知选项已加载
    this.ensureNotificationOptionsLoaded();

    // 如果不是强制检查且自动检查被禁用，则跳过
    if (!forceCheck && !this.notificationOptions.autoCheck) return;

    const updateResults = await this.versionManager.checkMultipleUpdates(tools);

    for (const [toolId, result] of updateResults) {
      await this.processUpdateResult(toolId, result);
    }

    // 触发UI更新事件
    this.dispatchUpdateEvent();
  }

  /**
   * 处理单个工具的更新结果
   */
  private async processUpdateResult(toolId: string, result: UpdateCheckResult): Promise<void> {
    const currentState = this.updateStates.get(toolId) || {
      hasUpdate: false,
      userIgnored: false,
      userDismissed: false
    };

    // 检查是否有更新
    if (result.hasUpdate) {
      // 检查是否需要清除旧版本的忽略状态
      const shouldClearIgnore = this.shouldClearIgnoreState(currentState, result);

      // 如果没有被忽略，或者需要清除忽略状态，则显示更新
      if (!this.isUpdateIgnored(currentState, result) || shouldClearIgnore) {
        const newState: UpdateState = {
          hasUpdate: true,
          updateInfo: result,
          lastNotified: Date.now(),
          userIgnored: false,
          userDismissed: false,
          ignoredVersion: undefined // 清除忽略版本
        };

        this.updateStates.set(toolId, newState);
        await this.saveUpdateState(toolId, newState);

        if (shouldClearIgnore) {
          console.log(`🔄 检测到新版本 ${VersionUtils.versionToString(result.latestVersion!)}, 已清除工具 ${toolId} 的忽略状态`);
        }
      }
    } else {
      // 如果没有更新，清除更新状态
      if (currentState.hasUpdate) {
        const newState: UpdateState = {
          ...currentState,
          hasUpdate: false,
          updateInfo: undefined
        };

        this.updateStates.set(toolId, newState);
        await this.saveUpdateState(toolId, newState);
      }
    }
  }

  /**
   * 获取有更新的工具数量
   */
  getUpdateCount(): number {
    return Array.from(this.updateStates.values()).filter(state => {
      if (!state.hasUpdate || state.userDismissed) {
        return false;
      }

      // 使用新的忽略逻辑检查
      return !this.isUpdateIgnored(state, state.updateInfo!);
    }).length;
  }

  /**
   * 获取所有更新状态
   */
  getAllUpdateStates(): Map<string, UpdateState> {
    return new Map(this.updateStates);
  }

  /**
   * 忽略工具更新
   */
  async ignoreUpdate(toolId: string): Promise<void> {
    const state = this.updateStates.get(toolId);
    if (state && state.updateInfo?.latestVersion) {
      // 记录被忽略的具体版本号
      state.userIgnored = true; // 保持向后兼容性
      state.ignoredVersion = state.updateInfo.latestVersion;
      this.updateStates.set(toolId, state);
      await this.saveUpdateState(toolId, state);
      this.dispatchUpdateEvent();

      console.log(`🚫 已忽略工具 ${toolId} 的版本 ${VersionUtils.versionToString(state.ignoredVersion)}`);
    }
  }

  /**
   * 暂时关闭更新通知
   */
  async dismissUpdate(toolId: string): Promise<void> {
    const state = this.updateStates.get(toolId);
    if (state) {
      state.userDismissed = true;
      this.updateStates.set(toolId, state);
      await this.saveUpdateState(toolId, state);
      this.dispatchUpdateEvent();
    }
  }

  /**
   * 批量暂时关闭所有更新通知
   */
  async dismissAllUpdates(): Promise<void> {
    const updatePromises: Promise<void>[] = [];
    let hasChanges = false;

    for (const [toolId, state] of this.updateStates) {
      if (state.hasUpdate && !state.userIgnored && !state.userDismissed) {
        state.userDismissed = true;
        this.updateStates.set(toolId, state);
        updatePromises.push(this.saveUpdateState(toolId, state));
        hasChanges = true;
      }
    }

    if (hasChanges) {
      // 等待所有保存操作完成
      await Promise.all(updatePromises);
      // 只触发一次更新事件
      this.dispatchUpdateEvent();
    }
  }

  /**
   * 重置工具的更新状态
   */
  async resetUpdateState(toolId: string): Promise<void> {
    this.updateStates.delete(toolId);
    await this.removeUpdateState(toolId);
    this.dispatchUpdateEvent();
  }

  /**
   * 保存更新状态到存储
   */
  private async saveUpdateState(toolId: string, state: UpdateState): Promise<void> {
    try {
      const key = `updateState_${toolId}`;
      await browser.storage.local.set({ [key]: state });
    } catch (error) {
      console.error(`保存更新状态失败 (${toolId}):`, error);
    }
  }

  /**
   * 从存储中移除更新状态
   */
  private async removeUpdateState(toolId: string): Promise<void> {
    try {
      const key = `updateState_${toolId}`;
      await browser.storage.local.remove(key);
    } catch (error) {
      console.error(`移除更新状态失败 (${toolId}):`, error);
    }
  }

  /**
   * 从存储中加载所有更新状态
   */
  async loadUpdateStates(): Promise<void> {
    try {
      const result = await browser.storage.local.get();
      let needsMigration = false;

      for (const [key, value] of Object.entries(result)) {
        if (key.startsWith('updateState_')) {
          const toolId = key.replace('updateState_', '');
          const state = value as UpdateState;

          // 数据迁移：处理旧的boolean格式
          const migratedState = this.migrateUpdateState(state);
          if (migratedState !== state) {
            needsMigration = true;
          }

          this.updateStates.set(toolId, migratedState);
        }
      }

      // 如果有数据迁移，保存迁移后的数据
      if (needsMigration) {
        console.log('检测到旧版本更新状态数据，正在进行数据迁移...');
        for (const [toolId, state] of this.updateStates) {
          await this.saveUpdateState(toolId, state);
        }
        console.log('更新状态数据迁移完成');
      }
    } catch (error) {
      console.error('加载更新状态失败:', error);
    }
  }

  /**
   * 触发更新事件
   */
  private dispatchUpdateEvent(): void {
    const event = new CustomEvent('updateStatesChanged', {
      detail: {
        updateStates: this.getAllUpdateStates(),
        updateCount: this.getUpdateCount()
      }
    });
    document.dispatchEvent(event);
  }

  /**
   * 清理过期的更新状态
   */
  async cleanupExpiredStates(): Promise<void> {
    const now = Date.now();
    const expireTime = 7 * 24 * 60 * 60 * 1000; // 7天
    
    for (const [toolId, state] of this.updateStates) {
      if (state.lastNotified && (now - state.lastNotified) > expireTime) {
        await this.resetUpdateState(toolId);
      }
    }
  }

  /**
   * 更新通知选项
   */
  updateNotificationOptions(options: Partial<UpdateNotificationOptions>): void {
    this.notificationOptions = { ...this.notificationOptions, ...options };
  }

  /**
   * 重新加载通知选项（供外部调用）
   */
  reloadNotificationOptions(): void {
    this.loadNotificationOptions();
  }

  /**
   * 检查更新是否被忽略
   */
  isUpdateIgnored(state: UpdateState, result: UpdateCheckResult): boolean {
    // 兼容旧的boolean格式
    if (state.userIgnored === true && !state.ignoredVersion) {
      return true;
    }

    // 检查版本基础的忽略状态
    if (state.ignoredVersion && result.latestVersion) {
      const comparison = VersionUtils.compareVersions(result.latestVersion, state.ignoredVersion);
      return comparison <= 0; // 如果最新版本小于等于被忽略的版本，则仍然忽略
    }

    return false;
  }

  /**
   * 检查是否应该清除忽略状态
   */
  private shouldClearIgnoreState(state: UpdateState, result: UpdateCheckResult): boolean {
    // 如果没有忽略状态，不需要清除
    if (!state.userIgnored && !state.ignoredVersion) {
      return false;
    }

    // 如果有新的版本信息
    if (result.latestVersion) {
      // 如果是旧的boolean格式，且有新版本，则清除忽略状态
      if (state.userIgnored === true && !state.ignoredVersion) {
        return true;
      }

      // 如果有被忽略的版本，比较版本号
      if (state.ignoredVersion) {
        const comparison = VersionUtils.compareVersions(result.latestVersion, state.ignoredVersion);
        return comparison > 0; // 如果最新版本大于被忽略的版本，则清除忽略状态
      }
    }

    return false;
  }

  /**
   * 迁移旧版本的更新状态数据
   */
  private migrateUpdateState(state: UpdateState): UpdateState {
    // 如果已经是新格式，直接返回
    if (state.ignoredVersion !== undefined || state.userIgnored !== true) {
      return state;
    }

    // 如果是旧的boolean格式且为true，但没有版本信息，保持原状
    // 这种情况下，当下次检查更新时会自动清除忽略状态
    if (state.userIgnored === true && !state.updateInfo?.latestVersion) {
      console.log('发现旧格式的忽略状态，但缺少版本信息，将在下次更新检查时自动清除');
      return state;
    }

    // 如果有版本信息，迁移到新格式
    if (state.userIgnored === true && state.updateInfo?.latestVersion) {
      return {
        ...state,
        ignoredVersion: state.updateInfo.latestVersion
      };
    }

    return state;
  }
}

// 导出单例实例
export const updateManager = UpdateManager.getInstance();
