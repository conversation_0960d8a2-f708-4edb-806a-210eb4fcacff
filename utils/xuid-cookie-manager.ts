/**
 * XUID切换助手 - Cookie管理器 (TypeScript版本)
 * 负责Cookie的读取、设置和XUID检测
 */

// 类型定义
export interface XuidCookie {
  name: string;
  value: string;
}

export interface CurrentXuidInfo {
  value: string;
  cookieName: string;
  allCookies: XuidCookie[];
}

export interface CookieOperationResult {
  success: boolean;
  error?: string;
  message?: string;
}

export interface CookieDetails {
  name: string;
  value: string;
  domain: string;
  path: string;
  secure: boolean;
  httpOnly: boolean;
  expirationDate?: number;
}

export interface ClusterStatus {
  cluster: 'tips' | 'small' | 'stable' | 'online' | 'unknown';
  cookie: chrome.cookies.Cookie | null;
}

export interface ValidationResult {
  valid: boolean;
  message?: string;
  value?: string;
  inputType?: 'xuid' | 'phone' | 'name' | 'asset';
  needsConversion?: boolean;
}

export class XuidCookieManager {
  private xuidCookieNames: string[] = ['XUID', 'xuid', 'Xuid', 'XUid', 'xuId', 'xUid', 'xUId'];

  /**
   * 获取当前标签页信息
   */
  async getCurrentTab(): Promise<chrome.tabs.Tab | null> {
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      return tab;
    } catch (error) {
      console.error('获取当前标签页失败:', error);
      return null;
    }
  }

  /**
   * 从URL获取域名
   */
  getDomainFromUrl(url: string): string {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname;
    } catch (error) {
      console.error('解析URL失败:', error);
      return '';
    }
  }

  /**
   * 获取所有XUID相关的Cookie
   */
  async getAllXuidCookies(tabUrl: string): Promise<XuidCookie[]> {
    if (!tabUrl) return [];

    const xuidCookies: XuidCookie[] = [];
    
    for (const cookieName of this.xuidCookieNames) {
      try {
        const cookie = await chrome.cookies.get({
          url: tabUrl,
          name: cookieName
        });
        
        if (cookie && cookie.value) {
          xuidCookies.push({
            name: cookie.name,
            value: cookie.value
          });
        }
      } catch (error) {
        // 忽略单个cookie获取失败
        console.debug(`获取Cookie ${cookieName} 失败:`, error);
      }
    }
    
    return xuidCookies;
  }

  /**
   * 获取当前页面的主要XUID
   */
  async getCurrentXuid(tabUrl: string): Promise<CurrentXuidInfo> {
    try {
      const xuidCookies = await this.getAllXuidCookies(tabUrl);
      
      if (xuidCookies.length > 0) {
        // 返回第一个找到的XUID
        return {
          value: xuidCookies[0].value,
          cookieName: xuidCookies[0].name,
          allCookies: xuidCookies
        };
      }
      
      return {
        value: '',
        cookieName: '',
        allCookies: []
      };
      
    } catch (error) {
      console.error('获取当前XUID失败:', error);
      return {
        value: '',
        cookieName: '',
        allCookies: []
      };
    }
  }

  /**
   * 设置XUID Cookie
   */
  async setXuidCookie(tabUrl: string, domain: string, cookieName: string, xuidValue: string): Promise<CookieOperationResult> {
    try {
      await chrome.cookies.set({
        url: tabUrl,
        name: cookieName,
        value: xuidValue,
        domain: domain,
        path: '/'
      });
      return { success: true };
    } catch (error) {
      console.error('设置XUID Cookie失败:', error);
      return { success: false, error: (error as Error).message };
    }
  }

  /**
   * 设置XUID Cookie（带过期时间）
   */
  async setXuidCookieWithExpiration(
    tabUrl: string, 
    domain: string, 
    cookieName: string, 
    xuidValue: string, 
    maxAgeSeconds?: number
  ): Promise<CookieOperationResult> {
    try {
      const cookieOptions: chrome.cookies.SetDetails = {
        url: tabUrl,
        name: cookieName,
        value: xuidValue,
        domain: domain,
        path: '/'
      };

      // 如果提供了过期时间，则设置
      if (maxAgeSeconds && maxAgeSeconds > 0) {
        cookieOptions.expirationDate = Math.floor(Date.now() / 1000) + maxAgeSeconds;
      }

      await chrome.cookies.set(cookieOptions);
      return { success: true };
    } catch (error) {
      console.error('设置XUID Cookie（带过期时间）失败:', error);
      return { success: false, error: (error as Error).message };
    }
  }

  /**
   * 删除所有XUID相关的Cookie
   */
  async clearAllXuidCookies(tabUrl: string): Promise<Array<{ cookieName: string; success: boolean; error?: string }>> {
    const results: Array<{ cookieName: string; success: boolean; error?: string }> = [];
    
    for (const cookieName of this.xuidCookieNames) {
      try {
        await chrome.cookies.remove({
          url: tabUrl,
          name: cookieName
        });
        results.push({ cookieName, success: true });
      } catch (error) {
        console.debug(`删除Cookie ${cookieName} 失败:`, error);
        results.push({ cookieName, success: false, error: (error as Error).message });
      }
    }
    
    return results;
  }

  /**
   * 刷新当前标签页
   */
  async refreshCurrentTab(): Promise<CookieOperationResult> {
    try {
      const tab = await this.getCurrentTab();
      if (tab) {
        await chrome.tabs.reload(tab.id);
        return { success: true };
      }
      return { success: false, error: '无法获取当前标签页' };
    } catch (error) {
      console.error('刷新页面失败:', error);
      return { success: false, error: (error as Error).message };
    }
  }

  /**
   * 验证XUID值格式，支持XUID、手机号、姓名、资产ID四种输入类型
   */
  validateXuidValue(inputValue: string): ValidationResult {
    if (!inputValue || typeof inputValue !== 'string') {
      return { valid: false, message: '输入不能为空' };
    }

    const trimmedValue = inputValue.trim();

    if (trimmedValue.length === 0) {
      return { valid: false, message: '输入不能为空' };
    }

    // 识别输入类型
    const inputType = this.identifyInputType(trimmedValue);

    switch (inputType) {
      case 'xuid':
        // XUID验证：纯数字且长度8-9位或12-20位（排除10位资产ID和11位手机号）
        if (!/^\d+$/.test(trimmedValue)) {
          return { valid: false, message: 'XUID必须为纯数字' };
        }
        if (trimmedValue.length < 8 || trimmedValue.length > 20) {
          return { valid: false, message: 'XUID长度应在8-20位之间' };
        }
        return {
          valid: true,
          value: trimmedValue,
          inputType: 'xuid',
          needsConversion: false
        };

      case 'phone':
        // 手机号验证：11位数字
        if (!/^\d{11}$/.test(trimmedValue)) {
          return { valid: false, message: '手机号必须为11位数字' };
        }
        return {
          valid: true,
          value: trimmedValue,
          inputType: 'phone',
          needsConversion: true
        };

      case 'asset':
        // 资产ID验证：10位数字
        if (!/^\d{10}$/.test(trimmedValue)) {
          return { valid: false, message: '资产ID必须为10位数字' };
        }
        return {
          valid: true,
          value: trimmedValue,
          inputType: 'asset',
          needsConversion: true
        };

      case 'name':
        // 姓名验证：长度限制
        if (trimmedValue.length < 1 || trimmedValue.length > 20) {
          return { valid: false, message: '姓名长度应在1-20个字符之间' };
        }

        // 特殊处理：纯数字但不符合XUID、手机号或资产ID格式的情况
        if (/^\d+$/.test(trimmedValue)) {
          if (trimmedValue.length < 8) {
            return { valid: false, message: '数字长度不足，无法识别为有效的XUID、手机号、资产ID或姓名' };
          }
        }

        return {
          valid: true,
          value: trimmedValue,
          inputType: 'name',
          needsConversion: true
        };

      default:
        return { valid: false, message: '不支持的输入格式' };
    }
  }

  /**
   * 识别输入类型：XUID、手机号、资产ID或姓名
   */
  private identifyInputType(input: string): 'xuid' | 'phone' | 'asset' | 'name' {
    // 优先检查手机号：纯数字且长度恰好为11位
    if (/^\d{11}$/.test(input)) {
      return 'phone';
    }

    // 检查资产ID：纯数字且长度恰好为10位
    if (/^\d{10}$/.test(input)) {
      return 'asset';
    }

    // 然后检查XUID：纯数字且长度8-9位或12-20位（排除10位资产ID和11位手机号）
    if (/^\d{8,9}$/.test(input) || /^\d{12,20}$/.test(input)) {
      return 'xuid';
    }

    // 包含中文字符或其他情况，判断为姓名
    return 'name';
  }

  /**
   * 检测Cookie变化
   */
  setupCookieChangeListener(callback: (changeInfo: chrome.cookies.CookieChangeInfo) => void): void {
    if (chrome.cookies && chrome.cookies.onChanged) {
      chrome.cookies.onChanged.addListener((changeInfo) => {
        // 只关注XUID相关的Cookie变化
        if (this.xuidCookieNames.includes(changeInfo.cookie.name)) {
          callback(changeInfo);
        }
      });
    }
  }

  /**
   * 获取Cookie的详细信息
   */
  async getCookieDetails(tabUrl: string, cookieName: string): Promise<CookieDetails | null> {
    try {
      const cookie = await chrome.cookies.get({
        url: tabUrl,
        name: cookieName
      });

      if (cookie) {
        return {
          name: cookie.name,
          value: cookie.value,
          domain: cookie.domain,
          path: cookie.path,
          secure: cookie.secure,
          httpOnly: cookie.httpOnly,
          expirationDate: cookie.expirationDate
        };
      }

      return null;
    } catch (error) {
      console.error('获取Cookie详情失败:', error);
      return null;
    }
  }

  /**
   * 批量设置多个XUID Cookie
   */
  async setMultipleXuidCookies(
    tabUrl: string,
    domain: string,
    xuidMappings: Array<{ cookieName: string; value: string }>
  ): Promise<Array<{ cookieName: string; value: string; success: boolean; error?: string }>> {
    const results: Array<{ cookieName: string; value: string; success: boolean; error?: string }> = [];

    for (const mapping of xuidMappings) {
      const result = await this.setXuidCookie(
        tabUrl,
        domain,
        mapping.cookieName,
        mapping.value
      );
      results.push({
        cookieName: mapping.cookieName,
        value: mapping.value,
        success: result.success,
        error: result.error
      });
    }

    return results;
  }

  /**
   * 检查是否有权限访问Cookie
   */
  async checkCookiePermissions(tabUrl: string): Promise<boolean> {
    try {
      // 尝试获取一个测试Cookie来检查权限
      await chrome.cookies.get({
        url: tabUrl,
        name: 'test_permission_check'
      });
      return true;
    } catch (error) {
      console.error('Cookie权限检查失败:', error);
      return false;
    }
  }

  /**
   * 获取当前集群状态
   * 检测优先级：tips > small > stable > online(默认)
   */
  async getClusterStatus(tabUrl: string): Promise<ClusterStatus> {
    try {
      // 检查tips集群
      const tipsCookie = await chrome.cookies.get({
        url: tabUrl,
        name: '__tips__'
      });
      if (tipsCookie && tipsCookie.value === '1') {
        return {
          cluster: 'tips',
          cookie: tipsCookie
        };
      }

      // 检查small集群
      const smallCookie = await chrome.cookies.get({
        url: tabUrl,
        name: '__small__'
      });
      if (smallCookie && smallCookie.value === '1') {
        return {
          cluster: 'small',
          cookie: smallCookie
        };
      }

      // 检查stable集群
      const stableCookie = await chrome.cookies.get({
        url: tabUrl,
        name: 'na__zyb_env__'
      });
      if (stableCookie && stableCookie.value === 'stable') {
        return {
          cluster: 'stable',
          cookie: stableCookie
        };
      }

      // 默认为online集群
      return {
        cluster: 'online',
        cookie: null
      };
    } catch (error) {
      console.error('获取集群状态失败:', error);
      return { cluster: 'unknown', cookie: null };
    }
  }

  /**
   * 清除所有集群相关的Cookie
   */
  async clearAllClusterCookies(tabUrl: string): Promise<Array<{ cookieName: string; success: boolean; error?: string }>> {
    const clusterCookies = ['__tips__', '__small__', 'na__zyb_env__'];
    const results: Array<{ cookieName: string; success: boolean; error?: string }> = [];

    for (const cookieName of clusterCookies) {
      try {
        await chrome.cookies.remove({
          url: tabUrl,
          name: cookieName
        });
        results.push({ cookieName, success: true });
      } catch (error) {
        console.debug(`删除集群Cookie ${cookieName} 失败:`, error);
        results.push({ cookieName, success: false, error: (error as Error).message });
      }
    }

    return results;
  }

  /**
   * 设置集群模式
   */
  async setClusterMode(tabUrl: string, domain: string, clusterType: 'tips' | 'small' | 'stable' | 'online'): Promise<CookieOperationResult> {
    try {
      console.log(`[COOKIE-MANAGER] 设置集群模式: ${clusterType}`);
      console.log(`[COOKIE-MANAGER] URL: ${tabUrl}`);
      console.log(`[COOKIE-MANAGER] Domain: ${domain}`);

      // 首先使用document.cookie方式清除所有集群Cookie
      console.log(`[COOKIE-MANAGER] 开始清除所有集群Cookie (使用document.cookie)`);
      const clusterCookies = ['__tips__', '__small__', 'na__zyb_env__'];
      for (const cookieName of clusterCookies) {
        await this.clearDocumentCookie(cookieName);
      }

      // 等待一小段时间确保清除操作完成
      await new Promise(resolve => setTimeout(resolve, 300));

      let result: CookieOperationResult = { success: true, message: '' };

      // 根据集群类型设置对应的Cookie
      switch (clusterType) {
        case 'tips':
          console.log(`[COOKIE-MANAGER] 设置Tips集群Cookie (使用document.cookie)`);
          const tipsSuccess = await this.setDocumentCookie('__tips__', '1');
          if (!tipsSuccess) {
            throw new Error('Tips Cookie设置失败');
          }
          result.message = '已切换到Tips集群';
          break;

        case 'small':
          console.log(`[COOKIE-MANAGER] 设置Small集群Cookie (使用document.cookie)`);
          const smallSuccess = await this.setDocumentCookie('__small__', '1');
          if (!smallSuccess) {
            throw new Error('Small Cookie设置失败');
          }
          result.message = '已切换到Small集群';
          break;

        case 'stable':
          console.log(`[COOKIE-MANAGER] 设置Stable集群Cookie (使用document.cookie)`);
          const stableSuccess = await this.setDocumentCookie('na__zyb_env__', 'stable');
          if (!stableSuccess) {
            throw new Error('Stable Cookie设置失败');
          }
          result.message = '已切换到Stable集群';
          break;

        case 'online':
          // online集群不需要设置Cookie，只需清除其他Cookie即可
          result.message = '已切换到Online集群';
          break;

        default:
          throw new Error(`不支持的集群类型: ${clusterType}`);
      }

      return result;
    } catch (error) {
      console.error('设置集群模式失败:', error);
      return { success: false, error: (error as Error).message };
    }
  }

  /**
   * 使用document.cookie方式设置Cookie
   */
  async setDocumentCookie(cookieName: string, cookieValue: string): Promise<boolean> {
    try {
      console.log(`[COOKIE-MANAGER] 使用document.cookie设置: ${cookieName}=${cookieValue}`);

      const tab = await this.getCurrentTab();
      if (!tab) {
        throw new Error('无法获取当前标签页');
      }

      // 注入脚本设置Cookie
      const results = await chrome.scripting.executeScript({
        target: { tabId: tab.id },
        func: (name: string, value: string) => {
          // 设置Cookie，24小时过期
          const expires = new Date();
          expires.setTime(expires.getTime() + (24 * 60 * 60 * 1000));
          const cookieString = `${name}=${value}; expires=${expires.toUTCString()}; path=/; SameSite=Lax`;

          console.log(`[CONTENT-SCRIPT] 设置Cookie: ${cookieString}`);
          document.cookie = cookieString;

          // 验证设置结果
          const verification = document.cookie.split(';').find(c => c.trim().startsWith(`${name}=`));
          const success = verification && verification.includes(`${name}=${value}`);

          console.log(`[CONTENT-SCRIPT] Cookie设置${success ? '成功' : '失败'}: ${name}=${value}`);
          return { success, cookieString, verification };
        },
        args: [cookieName, cookieValue]
      });

      const result = results[0].result;
      console.log(`[COOKIE-MANAGER] document.cookie设置结果:`, result);

      return result.success;
    } catch (error) {
      console.error(`[COOKIE-MANAGER] document.cookie设置失败:`, error);
      return false;
    }
  }

  /**
   * 使用document.cookie方式清除Cookie
   */
  async clearDocumentCookie(cookieName: string): Promise<boolean> {
    try {
      console.log(`[COOKIE-MANAGER] 使用document.cookie清除: ${cookieName}`);

      const tab = await this.getCurrentTab();
      if (!tab) {
        throw new Error('无法获取当前标签页');
      }

      // 注入脚本清除Cookie
      const results = await chrome.scripting.executeScript({
        target: { tabId: tab.id },
        func: (name: string) => {
          // 清除Cookie
          const cookieString = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/`;

          console.log(`[CONTENT-SCRIPT] 清除Cookie: ${cookieString}`);
          document.cookie = cookieString;

          // 验证清除结果
          const verification = document.cookie.split(';').find(c => c.trim().startsWith(`${name}=`));
          const success = !verification || verification.trim() === `${name}=`;

          console.log(`[CONTENT-SCRIPT] Cookie清除${success ? '成功' : '失败'}: ${name}`);
          return { success, cookieString };
        },
        args: [cookieName]
      });

      const result = results[0].result;
      console.log(`[COOKIE-MANAGER] document.cookie清除结果:`, result);

      return result.success;
    } catch (error) {
      console.error(`[COOKIE-MANAGER] document.cookie清除失败:`, error);
      return false;
    }
  }

  /**
   * 清除当前域名的XUID Cookie
   */
  async clearCurrentDomainXuid(tabUrl: string): Promise<CookieOperationResult & { details?: any }> {
    try {
      const results = await this.clearAllXuidCookies(tabUrl);

      // 检查是否有成功的删除操作
      const hasSuccess = results.some(result => result.success);

      if (hasSuccess) {
        return {
          success: true,
          message: '当前域名XUID已清除',
          details: results
        };
      } else {
        return {
          success: false,
          error: '清除XUID失败：' + results.map(r => r.error || '未知错误').join(', ')
        };
      }
    } catch (error) {
      console.error('清除当前域名XUID失败:', error);
      return {
        success: false,
        error: (error as Error).message || '未知错误'
      };
    }
  }

  /**
   * 清除所有相关子域名的XUID Cookie
   */
  async clearAllRelatedDomainsXuid(currentDomain: string): Promise<CookieOperationResult & { details?: any }> {
    try {
      const results: Array<{ domain: string; results: any }> = [];

      // 获取当前标签页信息
      const currentTab = await this.getCurrentTab();
      if (!currentTab) {
        return { success: false, error: '无法获取当前标签页' };
      }

      // 为当前域名清除Cookie
      const currentResult = await this.clearAllXuidCookies(currentTab.url);
      results.push({
        domain: currentDomain,
        results: currentResult
      });

      return {
        success: true,
        message: '已清除当前域名的XUID Cookie',
        details: results
      };

    } catch (error) {
      console.error('清除相关域名XUID失败:', error);
      return { success: false, error: (error as Error).message };
    }
  }

  /**
   * 获取当前标签页URL（内部方法）
   */
  async getCurrentTabUrl(): Promise<string | null> {
    try {
      const tab = await this.getCurrentTab();
      return tab ? tab.url : null;
    } catch (error) {
      console.error('获取当前标签页URL失败:', error);
      return null;
    }
  }
}
