/**
 * 通知管理器
 * 提供自定义的成功、错误、确认等提示功能，替换原生的 alert/confirm
 */

export type NotificationType = 'success' | 'error' | 'warning' | 'info';

export interface NotificationOptions {
  title?: string;
  message: string;
  type: NotificationType;
  duration?: number; // 自动关闭时间（毫秒），0表示不自动关闭
  showCloseButton?: boolean;
}

export interface ConfirmOptions {
  title?: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  type?: 'warning' | 'danger';
}

export interface PromptOptions {
  title?: string;
  message: string;
  defaultValue?: string;
  placeholder?: string;
  confirmText?: string;
  cancelText?: string;
}

class NotificationManager {
  private container: HTMLElement | null = null;
  private notifications: Set<HTMLElement> = new Set();

  // 初始化通知容器
  private initContainer(): void {
    if (this.container) return;

    this.container = document.createElement('div');
    this.container.id = 'notification-container';
    this.container.className = 'notification-container';
    document.body.appendChild(this.container);
  }

  // 显示通知
  show(options: NotificationOptions): void {
    this.initContainer();

    const notification = this.createNotification(options);
    this.container!.appendChild(notification);
    this.notifications.add(notification);

    // 添加进入动画
    setTimeout(() => {
      notification.classList.add('notification-show');
    }, 10);

    // 自动关闭
    if (options.duration && options.duration > 0) {
      setTimeout(() => {
        this.remove(notification);
      }, options.duration);
    }
  }

  // 显示成功提示
  success(message: string, duration: number = 3000): void {
    this.show({
      type: 'success',
      message,
      duration,
      showCloseButton: true
    });
  }

  // 显示错误提示
  error(message: string, duration: number = 5000): void {
    this.show({
      type: 'error',
      message,
      duration,
      showCloseButton: true
    });
  }

  // 显示警告提示
  warning(message: string, duration: number = 4000): void {
    this.show({
      type: 'warning',
      message,
      duration,
      showCloseButton: true
    });
  }

  // 显示信息提示
  info(message: string, duration: number = 3000): void {
    this.show({
      type: 'info',
      message,
      duration,
      showCloseButton: true
    });
  }

  // 显示确认对话框
  confirm(options: ConfirmOptions): Promise<boolean> {
    return new Promise((resolve) => {
      const modal = this.createConfirmModal(options, resolve);
      document.body.appendChild(modal);
    });
  }

  // 显示输入对话框
  prompt(options: PromptOptions): Promise<string | null> {
    return new Promise((resolve) => {
      const modal = this.createPromptModal(options, resolve);
      document.body.appendChild(modal);
    });
  }

  // 创建通知元素
  private createNotification(options: NotificationOptions): HTMLElement {
    const notification = document.createElement('div');
    notification.className = `notification notification-${options.type}`;

    const iconMap = {
      success: '✅',
      error: '❌',
      warning: '⚠️',
      info: 'ℹ️'
    };

    notification.innerHTML = `
      <div class="notification-content">
        <div class="notification-icon">${iconMap[options.type]}</div>
        <div class="notification-body">
          ${options.title ? `<div class="notification-title">${options.title}</div>` : ''}
          <div class="notification-message">${options.message}</div>
        </div>
        ${options.showCloseButton ? '<button class="notification-close">×</button>' : ''}
      </div>
    `;

    // 绑定关闭事件
    if (options.showCloseButton) {
      const closeBtn = notification.querySelector('.notification-close');
      closeBtn?.addEventListener('click', () => {
        this.remove(notification);
      });
    }

    return notification;
  }

  // 创建确认模态框
  private createConfirmModal(options: ConfirmOptions, resolve: (result: boolean) => void): HTMLElement {
    const modal = document.createElement('div');
    modal.className = 'modal-overlay confirm-modal';

    const typeClass = options.type === 'danger' ? 'confirm-danger' : 'confirm-warning';

    modal.innerHTML = `
      <div class="modal-content confirm-modal-content ${typeClass}">
        <div class="modal-header">
          <h3>${options.title || '确认操作'}</h3>
        </div>
        <div class="modal-body">
          <div class="confirm-message">${options.message}</div>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary confirm-cancel">${options.cancelText || '取消'}</button>
          <button class="btn ${options.type === 'danger' ? 'btn-danger' : 'btn-primary'} confirm-ok">
            ${options.confirmText || '确定'}
          </button>
        </div>
      </div>
    `;

    // 绑定事件
    const cancelBtn = modal.querySelector('.confirm-cancel');
    const okBtn = modal.querySelector('.confirm-ok');

    const cleanup = () => {
      document.body.removeChild(modal);
    };

    cancelBtn?.addEventListener('click', () => {
      cleanup();
      resolve(false);
    });

    okBtn?.addEventListener('click', () => {
      cleanup();
      resolve(true);
    });

    // 点击遮罩关闭
    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        cleanup();
        resolve(false);
      }
    });

    return modal;
  }

  // 创建输入对话框
  private createPromptModal(options: PromptOptions, resolve: (result: string | null) => void): HTMLElement {
    const modal = document.createElement('div');
    modal.className = 'modal-overlay prompt-modal';

    modal.innerHTML = `
      <div class="modal-content prompt-modal-content">
        <div class="modal-header">
          <h3>${options.title || '输入信息'}</h3>
        </div>
        <div class="modal-body">
          <div class="prompt-message">${options.message}</div>
          <input type="text" class="prompt-input form-control"
                 placeholder="${options.placeholder || ''}"
                 value="${options.defaultValue || ''}">
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary prompt-cancel">${options.cancelText || '取消'}</button>
          <button class="btn btn-primary prompt-ok">${options.confirmText || '确定'}</button>
        </div>
      </div>
    `;

    // 绑定事件
    const cancelBtn = modal.querySelector('.prompt-cancel');
    const okBtn = modal.querySelector('.prompt-ok');
    const input = modal.querySelector('.prompt-input') as HTMLInputElement;

    const cleanup = () => {
      document.body.removeChild(modal);
    };

    cancelBtn?.addEventListener('click', () => {
      cleanup();
      resolve(null);
    });

    okBtn?.addEventListener('click', () => {
      const value = input.value.trim();
      cleanup();
      resolve(value || null);
    });

    // 回车确认
    input?.addEventListener('keypress', (e) => {
      if (e.key === 'Enter') {
        const value = input.value.trim();
        cleanup();
        resolve(value || null);
      }
    });

    // 点击遮罩关闭
    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        cleanup();
        resolve(null);
      }
    });

    // 自动聚焦输入框
    setTimeout(() => {
      input?.focus();
      input?.select();
    }, 100);

    return modal;
  }

  // 移除通知
  private remove(notification: HTMLElement): void {
    if (!this.notifications.has(notification)) return;

    notification.classList.add('notification-hide');
    
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
      this.notifications.delete(notification);
    }, 300);
  }

  // 清除所有通知
  clearAll(): void {
    this.notifications.forEach(notification => {
      this.remove(notification);
    });
  }
}

// 导出单例实例
export const notificationManager = new NotificationManager();
