/**
 * 设置管理器
 * 管理扩展的各种设置和配置
 */

import { toolRegistry } from './tool-registry';
import { versionManager } from './version-manager';
import { notificationManager } from './notification-manager';
import { categoryManager } from './category-manager';
import type { Tool } from './tool-template';

interface Settings {
  theme: 'light' | 'dark' | 'auto';
  defaultCategory: string;
  enableAnimations: boolean;
  enableNotifications: boolean;
  autoSave: boolean;
  compactMode: boolean;
  // 工具更新设置
  autoCheckUpdates: boolean;
  updateCheckInterval: number; // 检查间隔（分钟）
}

const DEFAULT_SETTINGS: Settings = {
  theme: 'auto',
  defaultCategory: 'all',
  enableAnimations: true,
  enableNotifications: true,
  autoSave: true,
  compactMode: false,
  // 工具更新默认设置
  autoCheckUpdates: true,
  updateCheckInterval: 30 // 30分钟检查一次
};

export class SettingsManager {
  private settings: Settings = { ...DEFAULT_SETTINGS };
  private listeners: Set<(settings: Settings) => void> = new Set();
  private categoryChangeListener?: () => void;

  async init(): Promise<void> {
    try {
      console.log(`[SETTINGS-MANAGER] 开始初始化设置管理器...`);
      const result = await browser.storage.sync.get('settings');
      console.log(`[SETTINGS-MANAGER] 从存储加载的设置:`, result.settings);

      if (result.settings) {
        this.settings = { ...DEFAULT_SETTINGS, ...result.settings };
      }

      console.log(`[SETTINGS-MANAGER] 最终设置:`, this.settings);
      console.log(`[SETTINGS-MANAGER] 当前主题设置:`, this.settings.theme);

      this.applySettings();

      // 监听分类变更，用于更新设置界面中的分类选项
      this.categoryChangeListener = () => {
        this.updateCategoryOptionsInOpenModals();
      };
      categoryManager.addListener(this.categoryChangeListener);
    } catch (error) {
      console.error('初始化设置失败:', error);
    }
  }

  async updateSetting<K extends keyof Settings>(key: K, value: Settings[K]): Promise<void> {
    try {
      this.settings[key] = value;
      await browser.storage.sync.set({ settings: this.settings });
      this.applySettings();
      this.notifyListeners();
    } catch (error) {
      console.error('更新设置失败:', error);
      throw error;
    }
  }

  async updateSettings(newSettings: Partial<Settings>): Promise<void> {
    try {
      this.settings = { ...this.settings, ...newSettings };
      await browser.storage.sync.set({ settings: this.settings });
      this.applySettings();
      this.notifyListeners();
    } catch (error) {
      console.error('批量更新设置失败:', error);
      throw error;
    }
  }

  getSettings(): Settings {
    return { ...this.settings };
  }

  getSetting<K extends keyof Settings>(key: K): Settings[K] {
    return this.settings[key];
  }

  async resetSettings(): Promise<void> {
    try {
      this.settings = { ...DEFAULT_SETTINGS };
      await browser.storage.sync.set({ settings: this.settings });
      this.applySettings();
      this.notifyListeners();
    } catch (error) {
      console.error('重置设置失败:', error);
      throw error;
    }
  }

  onChange(listener: (settings: Settings) => void): () => void {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  // 清理资源
  destroy(): void {
    if (this.categoryChangeListener) {
      categoryManager.removeListener(this.categoryChangeListener);
      this.categoryChangeListener = undefined;
    }
  }

  // 更新已打开的设置模态框中的分类选项
  private updateCategoryOptionsInOpenModals(): void {
    const openModals = document.querySelectorAll('.settings-modal #default-category');
    openModals.forEach(select => {
      const selectElement = select as HTMLSelectElement;
      const currentValue = selectElement.value;

      // 获取最新的分类列表
      const categories = categoryManager.getAll();
      const categoryOptions = categories.map(category =>
        `<option value="${category.id}">${category.name}</option>`
      ).join('');

      // 更新选项
      selectElement.innerHTML = categoryOptions;

      // 尝试恢复之前的选择，如果分类不存在则选择"全部"
      if (categories.some(cat => cat.id === currentValue)) {
        selectElement.value = currentValue;
      } else {
        selectElement.value = 'all';
        // 如果当前设置的默认分类已被删除，更新设置
        if (this.settings.defaultCategory === currentValue) {
          this.updateSetting('defaultCategory', 'all');
        }
      }
    });
  }

  private applySettings(): void {
    // 应用主题
    this.applyTheme();
    
    // 应用动画设置
    this.applyAnimations();
    
    // 应用紧凑模式
    this.applyCompactMode();
  }

  private applyTheme(): void {
    const { theme } = this.settings;
    const root = document.documentElement;

    console.log(`[SETTINGS-MANAGER] 应用主题: ${theme}`);
    console.log(`[SETTINGS-MANAGER] 当前data-theme属性:`, root.getAttribute('data-theme'));

    if (theme === 'auto') {
      // 使用系统主题
      root.removeAttribute('data-theme');
      console.log(`[SETTINGS-MANAGER] 移除data-theme属性，使用系统主题`);
    } else {
      root.setAttribute('data-theme', theme);
      console.log(`[SETTINGS-MANAGER] 设置data-theme为: ${theme}`);
    }

    console.log(`[SETTINGS-MANAGER] 应用后data-theme属性:`, root.getAttribute('data-theme'));
  }

  private applyAnimations(): void {
    const { enableAnimations } = this.settings;
    const root = document.documentElement;
    
    if (enableAnimations) {
      root.classList.remove('no-animations');
    } else {
      root.classList.add('no-animations');
    }
  }

  private applyCompactMode(): void {
    const { compactMode } = this.settings;
    const root = document.documentElement;
    
    if (compactMode) {
      root.classList.add('compact-mode');
    } else {
      root.classList.remove('compact-mode');
    }
  }

  private notifyListeners(): void {
    this.listeners.forEach(listener => {
      try {
        listener(this.settings);
      } catch (error) {
        console.error('设置监听器执行失败:', error);
      }
    });
  }

  // 创建设置界面
  createSettingsModal(): HTMLElement {
    const modal = document.createElement('div');
    modal.className = 'settings-modal';

    // 获取动态分类选项
    const categories = categoryManager.getAll();
    const categoryOptions = categories.map(category =>
      `<option value="${category.id}">${category.name}</option>`
    ).join('');

    modal.innerHTML = `
      <div class="settings-modal-content">
        <div class="settings-modal-header">
          <h3>⚙️ 设置</h3>
          <button class="settings-modal-close">&times;</button>
        </div>
        
        <div class="settings-modal-body">
          <div class="settings-section">
            <h4>外观设置</h4>
            <div class="setting-item">
              <label>
                <span>主题:</span>
                <select id="theme-select">
                  <option value="auto">跟随系统</option>
                  <option value="light">浅色</option>
                  <option value="dark">深色</option>
                </select>
              </label>
            </div>
            <div class="setting-item">
              <label>
                <input type="checkbox" id="enable-animations">
                启用动画效果
              </label>
            </div>
            <div class="setting-item">
              <label>
                <input type="checkbox" id="compact-mode">
                紧凑模式
              </label>
            </div>
          </div>
          
          <div class="settings-section">
            <h4>功能设置</h4>
            <div class="setting-item">
              <label>
                <span>默认分类:</span>
                <select id="default-category">
                  ${categoryOptions}
                </select>
              </label>
            </div>
            <div class="setting-item">
              <label>
                <input type="checkbox" id="enable-notifications">
                启用通知
              </label>
            </div>
            <div class="setting-item">
              <label>
                <input type="checkbox" id="auto-save">
                自动保存设置
              </label>
            </div>
          </div>
          
          <div class="settings-section">
            <h4>工具更新</h4>
            <div class="update-settings">
              <div class="setting-item">
                <label>
                  <input type="checkbox" id="auto-check-updates">
                  自动检查更新
                </label>
                <div class="setting-description">
                  开启后将定期检查工具更新，并在工具卡片上显示更新提示
                </div>
              </div>
              <div class="setting-item">
                <label>
                  <span>检查间隔:</span>
                  <select id="update-check-interval">
                    <option value="15">15分钟</option>
                    <option value="30">30分钟</option>
                    <option value="60">1小时</option>
                    <option value="180">3小时</option>
                    <option value="360">6小时</option>
                  </select>
                </label>
              </div>
              <div class="setting-item">
                <button id="manual-check-updates" class="action-btn">🔄 手动检查更新</button>
                <div class="setting-description">
                  立即检查所有工具的更新状态
                </div>
              </div>
            </div>
          </div>

          <div class="settings-section">
            <h4>工具管理</h4>
            <div class="tool-management">
              <div class="tool-stats">
                <div class="stat-item">
                  <span class="stat-label">已安装工具:</span>
                  <span class="stat-value" id="total-tools">0</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">已启用工具:</span>
                  <span class="stat-value" id="enabled-tools">0</span>
                </div>
              </div>
              <div class="tool-actions">
                <button id="export-tools" class="action-btn">📤 导出配置</button>
                <button id="import-tools" class="action-btn">📥 导入配置</button>
                <button id="reset-tools" class="action-btn danger">🔄 重置工具</button>
              </div>
            </div>
          </div>
          
          <div class="settings-section">
            <h4>关于</h4>
            <div class="about-info">
              <div class="info-item">
                <span>版本:</span>
                <span>1.0.0</span>
              </div>
              <div class="info-item">
                <span>框架:</span>
                <span>WXT</span>
              </div>
              <div class="info-item">
                <span>开发者:</span>
                <span>服务运营工具集合</span>
              </div>
            </div>
          </div>
        </div>
        
        <div class="settings-modal-footer">
          <button id="reset-settings" class="secondary-btn">重置设置</button>
          <button id="save-settings" class="primary-btn">保存设置</button>
        </div>
      </div>
    `;

    this.addSettingsStyles();
    this.bindSettingsEvents(modal);
    this.loadCurrentSettings(modal);
    
    return modal;
  }

  private addSettingsStyles(): void {
    if (document.getElementById('settings-styles')) return;
    
    const styles = document.createElement('style');
    styles.id = 'settings-styles';
    styles.textContent = `
      .settings-modal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 10000;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      }
      
      .settings-modal-content {
        background: var(--background);
        border-radius: 12px;
        width: 420px;
        max-width: 90vw;
        max-height: 90vh;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        display: flex;
        flex-direction: column;
      }
      
      .settings-modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px;
        border-bottom: 1px solid var(--border);
        flex-shrink: 0;
      }

      .settings-modal-header h3 {
        margin: 0;
        font-size: 18px;
        color: var(--text-primary);
      }

      .settings-modal-close {
        background: none;
        border: none;
        font-size: 24px;
        cursor: pointer;
        color: var(--text-secondary);
        padding: 0;
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 6px;
      }

      .settings-modal-close:hover {
        background: var(--surface-hover);
      }
      
      .settings-modal-body {
        padding: 20px;
        overflow-y: auto;
        flex: 1;
      }
      
      .settings-section {
        margin-bottom: 24px;
      }
      
      .settings-section h4 {
        margin: 0 0 16px 0;
        font-size: 16px;
        font-weight: 600;
        color: var(--text-primary);
        border-bottom: 1px solid var(--border);
        padding-bottom: 8px;
      }

      .setting-item {
        margin-bottom: 16px;
      }

      .setting-item label {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 14px;
        color: var(--text-primary);
        cursor: pointer;
      }

      .setting-item select {
        padding: 6px 10px;
        border: 1px solid var(--border);
        border-radius: 6px;
        background: var(--background);
        color: var(--text-primary);
        min-width: 120px;
      }
      
      .setting-item input[type="checkbox"] {
        margin-left: 12px;
      }
      
      .tool-management,
      .update-settings {
        background: #f9fafb;
        padding: 16px;
        border-radius: 8px;
        border: 1px solid #e5e7eb;
      }

      .setting-description {
        font-size: 12px;
        color: #6b7280;
        margin-top: 4px;
        margin-left: 20px;
      }
      
      .tool-stats {
        margin-bottom: 16px;
      }
      
      .stat-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
      }
      
      .stat-label {
        font-size: 14px;
        color: #6b7280;
      }
      
      .stat-value {
        font-size: 14px;
        font-weight: 600;
        color: #4f46e5;
      }
      
      .tool-actions {
        display: flex;
        gap: 8px;
        flex-wrap: wrap;
      }
      
      .action-btn {
        padding: 8px 12px;
        border: 1px solid #d1d5db;
        border-radius: 6px;
        background: white;
        color: #374151;
        font-size: 12px;
        cursor: pointer;
        transition: all 0.2s;
      }
      
      .action-btn:hover {
        background: #f3f4f6;
      }
      
      .action-btn.danger {
        color: #ef4444;
        border-color: #fecaca;
      }
      
      .action-btn.danger:hover {
        background: #fef2f2;
      }
      
      .about-info {
        background: #f9fafb;
        padding: 16px;
        border-radius: 8px;
        border: 1px solid #e5e7eb;
      }
      
      .info-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
        font-size: 14px;
      }
      
      .info-item:last-child {
        margin-bottom: 0;
      }
      
      .settings-modal-footer {
        padding: 20px;
        border-top: 1px solid #e5e7eb;
        display: flex;
        justify-content: flex-end;
        gap: 12px;
        flex-shrink: 0;
      }
      
      .primary-btn, .secondary-btn {
        padding: 10px 16px;
        border: none;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s;
      }
      
      .primary-btn {
        background: #4f46e5;
        color: white;
      }
      
      .primary-btn:hover {
        background: #4338ca;
      }
      
      .secondary-btn {
        background: white;
        color: #374151;
        border: 1px solid #d1d5db;
      }
      
      .secondary-btn:hover {
        background: #f3f4f6;
      }
      
      /* 暗色主题支持 */
      [data-theme="dark"] .settings-modal-content {
        background: #1f2937;
        color: #f9fafb;
      }
      
      [data-theme="dark"] .settings-modal-header {
        border-bottom-color: #374151;
      }
      
      [data-theme="dark"] .settings-modal-header h3 {
        color: #f9fafb;
      }
      
      [data-theme="dark"] .settings-section h4 {
        color: #f9fafb;
        border-bottom-color: #374151;
      }
      
      [data-theme="dark"] .setting-item label {
        color: #d1d5db;
      }
      
      [data-theme="dark"] .tool-management,
      [data-theme="dark"] .update-settings,
      [data-theme="dark"] .about-info {
        background: #374151;
        border-color: #4b5563;
      }

      [data-theme="dark"] .setting-description {
        color: #9ca3af;
      }
      
      [data-theme="dark"] .settings-modal-footer {
        border-top-color: #374151;
      }
    `;
    
    document.head.appendChild(styles);
  }

  private bindSettingsEvents(modal: HTMLElement): void {
    const closeBtn = modal.querySelector('.settings-modal-close') as HTMLElement;
    
    // 关闭模态框
    const closeModal = () => {
      modal.remove();
      const styles = document.getElementById('settings-styles');
      if (styles) styles.remove();
    };
    
    closeBtn.addEventListener('click', closeModal);
    modal.addEventListener('click', (e) => {
      if (e.target === modal) closeModal();
    });
    
    // 保存设置
    modal.querySelector('#save-settings')?.addEventListener('click', async () => {
      await this.saveSettingsFromModal(modal);
      closeModal();
    });
    
    // 重置设置
    modal.querySelector('#reset-settings')?.addEventListener('click', async () => {
      const confirmed = await notificationManager.confirm({
        title: '重置设置',
        message: '确定要重置所有设置吗？此操作将恢复所有设置到默认值。',
        confirmText: '重置',
        cancelText: '取消',
        type: 'danger'
      });

      if (confirmed) {
        await this.resetSettings();
        this.loadCurrentSettings(modal);
        notificationManager.success('设置已重置为默认值');
      }
    });
    
    // 工具管理按钮
    this.bindToolManagementEvents(modal);

    // 工具更新按钮
    this.bindUpdateEvents(modal);
  }

  private async saveSettingsFromModal(modal: HTMLElement): Promise<void> {
    try {
      const selectedCategory = (modal.querySelector('#default-category') as HTMLSelectElement).value;

      // 验证选择的分类是否存在
      if (!categoryManager.exists(selectedCategory)) {
        notificationManager.warning('选择的默认分类不存在，已重置为"全部"');
        (modal.querySelector('#default-category') as HTMLSelectElement).value = 'all';
        return;
      }

      const newSettings: Partial<Settings> = {
        theme: (modal.querySelector('#theme-select') as HTMLSelectElement).value as Settings['theme'],
        defaultCategory: selectedCategory,
        enableAnimations: (modal.querySelector('#enable-animations') as HTMLInputElement).checked,
        enableNotifications: (modal.querySelector('#enable-notifications') as HTMLInputElement).checked,
        autoSave: (modal.querySelector('#auto-save') as HTMLInputElement).checked,
        compactMode: (modal.querySelector('#compact-mode') as HTMLInputElement).checked,
        autoCheckUpdates: (modal.querySelector('#auto-check-updates') as HTMLInputElement).checked,
        updateCheckInterval: parseInt((modal.querySelector('#update-check-interval') as HTMLSelectElement).value)
      };

      await this.updateSettings(newSettings);

      // 显示成功通知
      notificationManager.success('设置已保存');

    } catch (error) {
      console.error('保存设置失败:', error);
      notificationManager.error('保存设置失败，请重试');
    }
  }

  private loadCurrentSettings(modal: HTMLElement): void {
    (modal.querySelector('#theme-select') as HTMLSelectElement).value = this.settings.theme;

    // 验证并设置默认分类
    const defaultCategorySelect = modal.querySelector('#default-category') as HTMLSelectElement;
    if (categoryManager.exists(this.settings.defaultCategory)) {
      defaultCategorySelect.value = this.settings.defaultCategory;
    } else {
      // 如果当前设置的分类不存在，回退到"全部"
      defaultCategorySelect.value = 'all';
      // 静默更新设置
      this.updateSetting('defaultCategory', 'all').catch(console.error);
    }

    (modal.querySelector('#enable-animations') as HTMLInputElement).checked = this.settings.enableAnimations;
    (modal.querySelector('#enable-notifications') as HTMLInputElement).checked = this.settings.enableNotifications;
    (modal.querySelector('#auto-save') as HTMLInputElement).checked = this.settings.autoSave;
    (modal.querySelector('#compact-mode') as HTMLInputElement).checked = this.settings.compactMode;
    (modal.querySelector('#auto-check-updates') as HTMLInputElement).checked = this.settings.autoCheckUpdates;
    (modal.querySelector('#update-check-interval') as HTMLSelectElement).value = this.settings.updateCheckInterval.toString();

    // 更新工具统计
    this.updateToolStats(modal);
  }

  private updateToolStats(modal: HTMLElement): void {
    const stats = toolRegistry.getStats();
    modal.querySelector('#total-tools')!.textContent = stats.total.toString();
    modal.querySelector('#enabled-tools')!.textContent = stats.enabled.toString();
  }

  private bindToolManagementEvents(modal: HTMLElement): void {
    // 导出工具配置
    modal.querySelector('#export-tools')?.addEventListener('click', async () => {
      try {
        const tools = toolRegistry.export();
        const data = JSON.stringify(tools, null, 2);
        const blob = new Blob([data], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        await browser.downloads.download({
          url: url,
          filename: `fwyy-tools-config-${new Date().toISOString().split('T')[0]}.json`,
          saveAs: true
        });
        
        URL.revokeObjectURL(url);
        
      } catch (error) {
        console.error('导出配置失败:', error);
        notificationManager.error('导出配置失败');
      }
    });
    
    // 导入工具配置
    modal.querySelector('#import-tools')?.addEventListener('click', () => {
      const input = document.createElement('input');
      input.type = 'file';
      input.accept = '.json';
      input.onchange = async (e) => {
        const file = (e.target as HTMLInputElement).files?.[0];
        if (!file) return;
        
        try {
          const text = await file.text();
          const tools = JSON.parse(text);
          await toolRegistry.import(tools);
          this.updateToolStats(modal);
          notificationManager.success('配置导入成功');
        } catch (error) {
          console.error('导入配置失败:', error);
          notificationManager.error('导入配置失败，请检查文件格式');
        }
      };
      input.click();
    });
    
    // 重置工具
    modal.querySelector('#reset-tools')?.addEventListener('click', async () => {
      const confirmed = await notificationManager.confirm({
        title: '重置工具配置',
        message: '确定要重置所有工具配置吗？这将删除所有自定义工具。',
        confirmText: '重置',
        cancelText: '取消',
        type: 'danger'
      });

      if (confirmed) {
        try {
          await toolRegistry.clear();
          this.updateToolStats(modal);
          notificationManager.success('工具配置已重置');
        } catch (error) {
          console.error('重置工具失败:', error);
          notificationManager.error('重置工具失败');
        }
      }
    });
  }

  private bindUpdateEvents(modal: HTMLElement): void {
    // 手动检查更新按钮
    modal.querySelector('#manual-check-updates')?.addEventListener('click', async () => {
      const button = modal.querySelector('#manual-check-updates') as HTMLButtonElement;
      const originalText = button.textContent;

      try {
        // 显示检查中状态
        button.textContent = '🔄 检查中...';
        button.disabled = true;

        // 获取所有工具并检查更新
        const tools = toolRegistry.getAll();

        // 动态导入updateManager来避免循环依赖
        const { updateManager } = await import('./update-manager');

        // 使用updateManager来处理更新，这样会经过我们的版本基础忽略逻辑
        // 传入forceCheck=true来强制检查，忽略自动检查设置
        await updateManager.checkAndNotifyUpdates(tools, true);

        // 检查是否有可显示的更新
        const updateCount = updateManager.getUpdateCount();
        if (updateCount > 0) {
          button.textContent = '✅ 发现更新';
          // 触发更新事件，让主界面更新徽章和通知
          const event = new CustomEvent('manualUpdateCheckCompleted', {
            detail: { updateCount }
          });
          document.dispatchEvent(event);
        } else {
          button.textContent = '✅ 已是最新';
        }

        // 2秒后恢复按钮状态
        setTimeout(() => {
          button.textContent = originalText;
          button.disabled = false;
        }, 2000);

      } catch (error) {
        console.error('手动检查更新失败:', error);
        button.textContent = '❌ 检查失败';
        setTimeout(() => {
          button.textContent = originalText;
          button.disabled = false;
        }, 2000);
      }
    });
  }

  private async checkAllToolsForUpdates(tools: Tool[]): Promise<Map<string, any>> {
    try {
      // 使用版本管理器检查更新
      return await versionManager.checkMultipleUpdates(tools);
    } catch (error) {
      console.error('检查工具更新失败:', error);
      return new Map();
    }
  }

  private notifyUpdateResults(updateResults: Map<string, any>): void {
    // 通知主界面更新徽章状态
    // 这里可以通过事件或者直接调用主界面的方法来更新徽章
    const event = new CustomEvent('toolUpdatesChecked', {
      detail: { updateResults }
    });
    document.dispatchEvent(event);
  }
}

// 创建全局实例
export const settingsManager = new SettingsManager();
