/**
 * 工具注册管理系统
 * 提供工具的注册、管理和查询功能
 */

import type { Tool, ToolDependency } from './tool-template';
import { VersionUtils } from './tool-template';

// 工具存储接口
interface ToolStorage {
  getTools(): Promise<Tool[]>;
  saveTool(tool: Tool): Promise<void>;
  removeTool(id: string): Promise<void>;
  updateTool(id: string, updates: Partial<Tool>): Promise<void>;
}

// 本地存储实现
class LocalToolStorage implements ToolStorage {
  private readonly STORAGE_KEY = 'fwyy-tools-registry';

  async getTools(): Promise<Tool[]> {
    try {
      const result = await browser.storage.local.get(this.STORAGE_KEY);
      return result[this.STORAGE_KEY] || [];
    } catch (error) {
      console.error('获取工具列表失败:', error);
      return [];
    }
  }

  async saveTool(tool: Tool): Promise<void> {
    try {
      const tools = await this.getTools();
      const existingIndex = tools.findIndex(t => t.id === tool.id);
      
      if (existingIndex >= 0) {
        tools[existingIndex] = tool;
      } else {
        tools.push(tool);
      }
      
      await browser.storage.local.set({ [this.STORAGE_KEY]: tools });
    } catch (error) {
      console.error('保存工具失败:', error);
      throw error;
    }
  }

  async removeTool(id: string): Promise<void> {
    try {
      const tools = await this.getTools();
      const filteredTools = tools.filter(t => t.id !== id);
      await browser.storage.local.set({ [this.STORAGE_KEY]: filteredTools });
    } catch (error) {
      console.error('删除工具失败:', error);
      throw error;
    }
  }

  async updateTool(id: string, updates: Partial<Tool>): Promise<void> {
    try {
      const tools = await this.getTools();
      const toolIndex = tools.findIndex(t => t.id === id);
      
      if (toolIndex >= 0) {
        tools[toolIndex] = { ...tools[toolIndex], ...updates };
        await browser.storage.local.set({ [this.STORAGE_KEY]: tools });
      } else {
        throw new Error(`工具 ${id} 不存在`);
      }
    } catch (error) {
      console.error('更新工具失败:', error);
      throw error;
    }
  }
}

// 工具注册表类
export class ToolRegistry {
  private tools: Map<string, Tool> = new Map(); // 内存中的工具实例（包含action方法）
  private toolConfigs: Map<string, Partial<Tool>> = new Map(); // 存储中的工具配置（不包含action方法）
  private listeners: Set<() => void> = new Set();
  private storage: ToolStorage;
  private initialized = false;

  constructor(storage?: ToolStorage) {
    this.storage = storage || new LocalToolStorage();
  }

  // 初始化注册表
  async init(): Promise<void> {
    if (this.initialized) return;

    try {
      const storedConfigs = await this.storage.getTools();

      // 数据迁移：将旧的单分类格式转换为新的多分类格式
      const migratedConfigs = storedConfigs.map(config => this.migrateToolData(config));

      migratedConfigs.forEach(config => {
        this.toolConfigs.set(config.id!, config);
      });

      // 如果有数据迁移，保存迁移后的数据
      const needsMigration = storedConfigs.some(config => this.needsMigration(config));
      if (needsMigration) {
        console.log('检测到旧版本数据，正在进行数据迁移...');
        for (const config of migratedConfigs) {
          await this.storage.saveTool(config as Tool);
        }
        console.log('数据迁移完成');
      }

      this.initialized = true;
      console.log(`✅ 工具注册表初始化完成，加载了 ${migratedConfigs.length} 个工具配置`);
      this.notifyListeners();
    } catch (error) {
      console.error('初始化工具注册表失败:', error);
    }
  }

  // 注册工具（增强版，包含依赖检查和生命周期管理）
  async register(tool: Tool): Promise<void> {
    try {
      // 检查工具ID是否已存在
      if (this.tools.has(tool.id)) {
        throw new Error(`工具 ${tool.id} 已存在`);
      }

      // 从存储中恢复用户配置（如果存在）
      const storedConfig = this.toolConfigs.get(tool.id);
      if (storedConfig) {
        // 合并存储的配置到工具实例
        tool.categories = storedConfig.categories || tool.categories;
        tool.position = storedConfig.position !== undefined ? storedConfig.position : tool.position;
        tool.enabled = storedConfig.enabled !== undefined ? storedConfig.enabled : tool.enabled;
        // 恢复新标签页相关配置
        tool.displayMode = storedConfig.displayMode || tool.displayMode;
        tool.newtabData = storedConfig.newtabData || tool.newtabData;
        tool.requiresFullscreen = storedConfig.requiresFullscreen !== undefined ? storedConfig.requiresFullscreen : tool.requiresFullscreen;
      }

      // 检查依赖
      await this.checkDependencies(tool);

      // 初始化工具
      if (tool.onInit) {
        tool.lifecycleState = 'initializing';
        await tool.onInit();
      }
      tool.lifecycleState = 'ready';

      // 注册工具到内存
      this.tools.set(tool.id, tool);

      // 保存工具配置到存储（不包含action方法）
      await this.saveToolConfig(tool);

      console.log(`✅ 工具 ${tool.name} (${tool.id}) 注册成功`);
      this.notifyListeners();
    } catch (error) {
      console.error('注册工具失败:', error);
      tool.lifecycleState = 'error';
      throw error;
    }
  }

  // 批量注册工具
  async registerBatch(tools: Tool[]): Promise<void> {
    try {
      for (const tool of tools) {
        this.tools.set(tool.id, tool);
        await this.saveToolConfig(tool);
      }
      this.notifyListeners();
    } catch (error) {
      console.error('批量注册工具失败:', error);
      throw error;
    }
  }

  // 注销工具
  async unregister(id: string): Promise<void> {
    try {
      this.tools.delete(id);
      this.toolConfigs.delete(id);
      await this.storage.removeTool(id);
      this.notifyListeners();
    } catch (error) {
      console.error('注销工具失败:', error);
      throw error;
    }
  }

  // 更新工具
  async updateTool(id: string, updates: Partial<Tool>): Promise<void> {
    try {
      const tool = this.tools.get(id);
      if (!tool) {
        throw new Error(`工具 ${id} 不存在`);
      }

      // 直接更新原始工具实例的属性，而不是创建新对象
      Object.assign(tool, updates);

      // 保存配置到存储（不包含action方法）
      await this.saveToolConfig(tool);

      this.notifyListeners();
    } catch (error) {
      console.error('更新工具失败:', error);
      throw error;
    }
  }

  // 更新工具位置
  async updateToolPosition(id: string, position: number): Promise<void> {
    try {
      await this.updateTool(id, { position });
    } catch (error) {
      console.error('更新工具位置失败:', error);
      throw error;
    }
  }

  // 批量更新工具位置
  async updateToolPositions(positions: { id: string; position: number }[]): Promise<void> {
    try {
      for (const { id, position } of positions) {
        const tool = this.tools.get(id);
        if (tool) {
          // 直接更新原始工具实例的position属性
          tool.position = position;
          await this.saveToolConfig(tool);
        }
      }
      this.notifyListeners();
    } catch (error) {
      console.error('批量更新工具位置失败:', error);
      throw error;
    }
  }

  // 获取所有工具（按position排序）
  getAll(): Tool[] {
    return Array.from(this.tools.values()).sort((a, b) => {
      const posA = typeof a.position === 'number' ? a.position : 0;
      const posB = typeof b.position === 'number' ? b.position : 0;
      return posA - posB;
    });
  }

  // 根据ID获取工具
  getById(id: string): Tool | undefined {
    return this.tools.get(id);
  }

  // 根据分类获取工具
  getByCategory(category: string): Tool[] {
    if (category === 'all') return this.getAll();
    return this.getAll().filter(tool => {
      // 使用新的多分类标签系统
      return tool.categories && Array.isArray(tool.categories) && tool.categories.includes(category);
    });
  }

  // 搜索工具
  search(query: string): Tool[] {
    const lowerQuery = query.toLowerCase().trim();
    if (!lowerQuery) return this.getAll();
    
    return this.getAll().filter(tool => 
      tool.name.toLowerCase().includes(lowerQuery) ||
      tool.description.toLowerCase().includes(lowerQuery) ||
      tool.id.toLowerCase().includes(lowerQuery)
    );
  }

  // 获取启用的工具
  getEnabled(): Tool[] {
    return this.getAll().filter(tool => tool.enabled);
  }

  // 获取禁用的工具
  getDisabled(): Tool[] {
    return this.getAll().filter(tool => !tool.enabled);
  }

  // 获取工具统计信息
  getStats() {
    const all = this.getAll();
    const enabled = all.filter(t => t.enabled);
    const byCategory = {
      productivity: all.filter(t => t.category === 'productivity').length,
      development: all.filter(t => t.category === 'development').length,
      utility: all.filter(t => t.category === 'utility').length
    };

    return {
      total: all.length,
      enabled: enabled.length,
      disabled: all.length - enabled.length,
      byCategory
    };
  }

  // 监听变化
  onChange(listener: () => void): () => void {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  // 清空所有工具
  async clear(): Promise<void> {
    try {
      this.tools.clear();
      this.toolConfigs.clear();
      await browser.storage.local.remove('fwyy-tools-registry');
      this.notifyListeners();
    } catch (error) {
      console.error('清空工具注册表失败:', error);
      throw error;
    }
  }

  // 导出工具配置
  export(): Tool[] {
    return this.getAll();
  }

  // 导入工具配置
  async import(tools: Tool[]): Promise<void> {
    try {
      await this.clear();
      await this.registerBatch(tools);
    } catch (error) {
      console.error('导入工具配置失败:', error);
      throw error;
    }
  }

  // 检查工具依赖
  private async checkDependencies(tool: Tool): Promise<void> {
    if (!tool.dependencies || tool.dependencies.length === 0) {
      return;
    }

    const missingDependencies: string[] = [];
    const incompatibleDependencies: string[] = [];

    for (const dep of tool.dependencies) {
      const dependencyTool = this.tools.get(dep.id);

      if (!dependencyTool) {
        if (!dep.optional) {
          missingDependencies.push(dep.id);
        }
        continue;
      }

      // 检查版本兼容性
      if (dep.version && !VersionUtils.isVersionCompatible(dep.version, dependencyTool.version)) {
        incompatibleDependencies.push(`${dep.id} (需要: ${dep.version}, 当前: ${VersionUtils.versionToString(dependencyTool.version)})`);
      }
    }

    if (missingDependencies.length > 0) {
      throw new Error(`缺少必需的依赖: ${missingDependencies.join(', ')}`);
    }

    if (incompatibleDependencies.length > 0) {
      throw new Error(`依赖版本不兼容: ${incompatibleDependencies.join(', ')}`);
    }
  }

  // 启用工具
  async enableTool(id: string): Promise<void> {
    const tool = this.tools.get(id);
    if (!tool) {
      throw new Error(`工具 ${id} 不存在`);
    }

    if (tool.enabled) {
      return; // 已经启用
    }

    try {
      if (tool.onEnable) {
        await tool.onEnable();
      }
      tool.enabled = true;
      await this.saveToolConfig(tool);
      this.notifyListeners();
      console.log(`✅ 工具 ${tool.name} 已启用`);
    } catch (error) {
      console.error(`启用工具 ${tool.name} 失败:`, error);
      throw error;
    }
  }

  // 禁用工具
  async disableTool(id: string): Promise<void> {
    const tool = this.tools.get(id);
    if (!tool) {
      throw new Error(`工具 ${id} 不存在`);
    }

    if (!tool.enabled) {
      return; // 已经禁用
    }

    try {
      if (tool.onDisable) {
        await tool.onDisable();
      }
      tool.enabled = false;
      await this.saveToolConfig(tool);
      this.notifyListeners();
      console.log(`⏸️ 工具 ${tool.name} 已禁用`);
    } catch (error) {
      console.error(`禁用工具 ${tool.name} 失败:`, error);
      throw error;
    }
  }

  // 销毁工具
  async destroyTool(id: string): Promise<void> {
    const tool = this.tools.get(id);
    if (!tool) {
      throw new Error(`工具 ${id} 不存在`);
    }

    try {
      if (tool.onDestroy) {
        await tool.onDestroy();
      }
      tool.lifecycleState = 'destroyed';
      console.log(`🗑️ 工具 ${tool.name} 已销毁`);
    } catch (error) {
      console.error(`销毁工具 ${tool.name} 失败:`, error);
      throw error;
    }
  }

  // 获取工具依赖图
  getDependencyGraph(): Map<string, string[]> {
    const graph = new Map<string, string[]>();

    for (const tool of this.tools.values()) {
      const dependencies = tool.dependencies?.map(dep => dep.id) || [];
      graph.set(tool.id, dependencies);
    }

    return graph;
  }

  // 检查循环依赖
  checkCircularDependencies(): string[] {
    const graph = this.getDependencyGraph();
    const visited = new Set<string>();
    const recursionStack = new Set<string>();
    const cycles: string[] = [];

    const dfs = (toolId: string, path: string[]): void => {
      if (recursionStack.has(toolId)) {
        const cycleStart = path.indexOf(toolId);
        cycles.push(path.slice(cycleStart).concat(toolId).join(' -> '));
        return;
      }

      if (visited.has(toolId)) {
        return;
      }

      visited.add(toolId);
      recursionStack.add(toolId);

      const dependencies = graph.get(toolId) || [];
      for (const dep of dependencies) {
        dfs(dep, [...path, toolId]);
      }

      recursionStack.delete(toolId);
    };

    for (const toolId of graph.keys()) {
      if (!visited.has(toolId)) {
        dfs(toolId, []);
      }
    }

    return cycles;
  }

  private notifyListeners(): void {
    this.listeners.forEach(listener => {
      try {
        listener();
      } catch (error) {
        console.error('工具注册表监听器执行失败:', error);
      }
    });
  }

  // 数据迁移：检查工具是否需要迁移
  private needsMigration(tool: Tool): boolean {
    // 检查是否需要添加默认字段
    return !tool.categories || tool.categories.length === 0 || typeof tool.position !== 'number';
  }

  // 数据迁移：确保工具数据格式正确
  private migrateToolData(tool: Tool): Tool {
    const migratedTool = { ...tool };

    // 确保有categories字段
    if (!migratedTool.categories || migratedTool.categories.length === 0) {
      migratedTool.categories = ['all'];
      console.log(`为工具 ${tool.id} 添加默认分类: all`);
    }

    // 添加position字段（如果没有的话）
    if (typeof migratedTool.position !== 'number') {
      migratedTool.position = 0;
    }

    return migratedTool;
  }

  // 保存工具配置到存储（不包含action方法）
  private async saveToolConfig(tool: Tool): Promise<void> {
    const config: Partial<Tool> = {
      id: tool.id,
      name: tool.name,
      description: tool.description,
      icon: tool.icon,
      categories: tool.categories,
      enabled: tool.enabled,
      badge: tool.badge,
      position: tool.position,
      version: tool.version,
      dependencies: tool.dependencies,
      lifecycleState: tool.lifecycleState,
      config: tool.config,
      permissions: tool.permissions,
      // 新标签页跳转相关字段
      displayMode: tool.displayMode,
      newtabData: tool.newtabData,
      requiresFullscreen: tool.requiresFullscreen
      // 注意：不包含 action 和生命周期钩子方法
    };

    this.toolConfigs.set(tool.id, config);
    await this.storage.saveTool(config as Tool);
  }
}

// 创建全局实例
export const toolRegistry = new ToolRegistry();

// 默认导出
export default toolRegistry;
